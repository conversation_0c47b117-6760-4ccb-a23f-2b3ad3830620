/**
 * Security and encryption type definitions for InkSight
 * Provides comprehensive types for encryption, key management, and security operations
 */

// Encryption algorithm types
export type EncryptionAlgorithm = 'AES-256-GCM' | 'AES-256-CBC';

// Key derivation types
export type KeyDerivationFunction = 'PBKDF2' | 'scrypt' | 'Argon2';

// Security levels
export type SecurityLevel = 'standard' | 'high' | 'maximum';

// Encryption configuration
export interface EncryptionConfig {
  algorithm: EncryptionAlgorithm;
  keySize: number; // Key size in bits (256 for AES-256)
  ivSize: number; // Initialization vector size in bytes
  tagSize: number; // Authentication tag size in bytes (for GCM)
  iterations: number; // PBKDF2 iterations
  saltSize: number; // Salt size in bytes
}

// Encrypted data structure
export interface EncryptedData {
  data: string; // Base64 encoded encrypted data
  iv: string; // Base64 encoded initialization vector
  salt: string; // Base64 encoded salt
  tag?: string; // Base64 encoded authentication tag (for GCM)
  algorithm: EncryptionAlgorithm;
  keyDerivation: KeyDerivationFunction;
  iterations: number;
  timestamp: number; // Encryption timestamp
  version: string; // Encryption version for future compatibility
}

// Key management types
export interface EncryptionKey {
  id: string; // Unique key identifier
  key: string; // Base64 encoded key
  algorithm: EncryptionAlgorithm;
  created: number; // Creation timestamp
  lastUsed: number; // Last usage timestamp
  rotationDue: number; // Next rotation timestamp
  purpose: KeyPurpose;
  securityLevel: SecurityLevel;
}

export type KeyPurpose =
  | 'document-encryption'
  | 'metadata-encryption'
  | 'database-encryption'
  | 'backup-encryption'
  | 'master-key';

// Key derivation parameters
export interface KeyDerivationParams {
  password: string;
  salt: string;
  iterations: number;
  keyLength: number;
  algorithm: KeyDerivationFunction;
}

// Security context for operations
export interface SecurityContext {
  userId?: string;
  sessionId: string;
  timestamp: number;
  operation: SecurityOperation;
  securityLevel: SecurityLevel;
  auditRequired: boolean;
}

export type SecurityOperation =
  | 'encrypt'
  | 'decrypt'
  | 'key-generation'
  | 'key-rotation'
  | 'key-derivation'
  | 'secure-delete'
  | 'backup'
  | 'restore'
  | 'import'
  | 'export';

// Security audit log entry
export interface SecurityAuditEntry {
  id: string;
  timestamp: number;
  operation: SecurityOperation;
  success: boolean;
  context: SecurityContext;
  details?: Record<string, any>;
  error?: string;
}

// Key rotation policy
export interface KeyRotationPolicy {
  enabled: boolean;
  intervalDays: number; // Rotation interval in days
  maxKeyAge: number; // Maximum key age in days
  autoRotate: boolean; // Automatic rotation enabled
  notifyBeforeRotation: number; // Days before rotation to notify
}

// Security settings
export interface SecuritySettings {
  encryptionConfig: EncryptionConfig;
  keyRotationPolicy: KeyRotationPolicy;
  securityLevel: SecurityLevel;
  auditEnabled: boolean;
  biometricEnabled: boolean;
  autoLockTimeout: number; // Auto-lock timeout in minutes
  maxFailedAttempts: number;
  lockoutDuration: number; // Lockout duration in minutes
}

// Hardware security module types
export interface HSMCapabilities {
  available: boolean;
  keyGeneration: boolean;
  keyStorage: boolean;
  encryption: boolean;
  biometric: boolean;
  secureEnclave: boolean;
}

// Secure storage options
export interface SecureStorageOptions {
  useHardwareBackedKeys: boolean;
  requireBiometric: boolean;
  accessGroup?: string; // iOS keychain access group
  securityLevel: SecurityLevel;
}

// File integrity verification
export interface FileIntegrity {
  filePath: string;
  checksum: string; // SHA-256 checksum
  algorithm: 'SHA-256' | 'SHA-512';
  size: number; // File size in bytes
  timestamp: number; // Verification timestamp
  verified: boolean;
}

// Secure deletion options
export interface SecureDeletionOptions {
  overwritePasses: number; // Number of overwrite passes
  verifyDeletion: boolean; // Verify deletion completed
  auditDeletion: boolean; // Log deletion in audit trail
}

// Security error types
export type SecurityErrorType =
  | 'ENCRYPTION_FAILED'
  | 'DECRYPTION_FAILED'
  | 'KEY_GENERATION_FAILED'
  | 'KEY_DERIVATION_FAILED'
  | 'INVALID_KEY'
  | 'INVALID_DATA'
  | 'AUTHENTICATION_FAILED'
  | 'INTEGRITY_VIOLATION'
  | 'HARDWARE_UNAVAILABLE'
  | 'BIOMETRIC_FAILED'
  | 'LOCKOUT_ACTIVE'
  | 'PERMISSION_DENIED';

export class SecurityError extends Error {
  public readonly type: SecurityErrorType;
  public readonly context?: SecurityContext;
  public readonly details?: Record<string, any>;

  constructor(
    type: SecurityErrorType,
    message: string,
    context?: SecurityContext,
    details?: Record<string, any>,
  ) {
    super(message);
    this.name = 'SecurityError';
    this.type = type;
    this.context = context;
    this.details = details;
  }
}

// Security service interface
export interface ISecurityService {
  // Encryption operations
  encrypt(data: string, context: SecurityContext): Promise<EncryptedData>;
  decrypt(
    encryptedData: EncryptedData,
    context: SecurityContext,
  ): Promise<string>;

  // Key management
  generateKey(
    purpose: KeyPurpose,
    securityLevel: SecurityLevel,
  ): Promise<EncryptionKey>;
  deriveKey(params: KeyDerivationParams): Promise<EncryptionKey>;
  rotateKey(keyId: string): Promise<EncryptionKey>;

  // Security utilities
  verifyIntegrity(filePath: string): Promise<FileIntegrity>;
  secureDelete(
    filePath: string,
    options?: SecureDeletionOptions,
  ): Promise<boolean>;

  // Audit and monitoring
  auditOperation(entry: SecurityAuditEntry): Promise<void>;
  getAuditLog(startDate?: Date, endDate?: Date): Promise<SecurityAuditEntry[]>;
}

// Export default encryption configuration
export const DEFAULT_ENCRYPTION_CONFIG: EncryptionConfig = {
  algorithm: 'AES-256-GCM',
  keySize: 256,
  ivSize: 16,
  tagSize: 16,
  iterations: 100000,
  saltSize: 32,
};

export const DEFAULT_SECURITY_SETTINGS: SecuritySettings = {
  encryptionConfig: DEFAULT_ENCRYPTION_CONFIG,
  keyRotationPolicy: {
    enabled: true,
    intervalDays: 90,
    maxKeyAge: 365,
    autoRotate: false,
    notifyBeforeRotation: 7,
  },
  securityLevel: 'high',
  auditEnabled: true,
  biometricEnabled: false,
  autoLockTimeout: 15,
  maxFailedAttempts: 5,
  lockoutDuration: 30,
};
