/**
 * Document Import Service
 * Handles file picker integration, batch import processing, and progress tracking
 */

import DocumentPicker, {
  DocumentPickerResponse,
} from 'react-native-document-picker';
import RNFS from 'react-native-fs';
import {
  ImportProgress,
  ImportStatus,
  ImportOptions,
  ImportResult,
  DocumentManagementError,
  DocumentManagementException,
} from '../../types/document-management';
import { DocumentFormat } from '../../types/document';
import { DocumentManager } from './DocumentManager';
import { Logger } from '../Logger';

export class ImportService {
  private static instance: ImportService;
  private documentManager: DocumentManager;
  private logger: typeof Logger;
  private activeImports: Map<string, ImportProgress> = new Map();

  private constructor() {
    this.documentManager = DocumentManager.getInstance();
    this.logger = Logger;
  }

  public static getInstance(): ImportService {
    if (!ImportService.instance) {
      ImportService.instance = new ImportService();
    }
    return ImportService.instance;
  }

  /**
   * Open file picker and select documents for import
   */
  public async pickDocuments(
    allowMultiple: boolean = true,
  ): Promise<DocumentPickerResponse[]> {
    try {
      const supportedTypes = this.getSupportedMimeTypes();

      const result = await DocumentPicker.pick({
        type: supportedTypes,
        allowMultiSelection: allowMultiple,
        copyTo: 'cachesDirectory', // Copy to app cache for processing
      });

      this.logger.info(`Documents selected for import: ${result.length}`);
      return result;
    } catch (error) {
      if (DocumentPicker.isCancel(error)) {
        this.logger.info('Document picker cancelled by user');
        return [];
      }

      this.logger.error(
        'Failed to pick documents',
        'import',
        undefined,
        error as Error,
      );
      throw new DocumentManagementException(
        DocumentManagementError.PERMISSION_DENIED,
        'Failed to access file picker',
        error,
      );
    }
  }

  /**
   * Import multiple documents with progress tracking
   */
  public async batchImport(
    files: DocumentPickerResponse[],
    options: ImportOptions = {
      generateThumbnail: true,
      extractMetadata: true,
      overwriteExisting: false,
    },
    onProgress?: (progress: ImportProgress) => void,
  ): Promise<ImportResult[]> {
    const results: ImportResult[] = [];

    try {
      for (const file of files) {
        const importProgress = this.createImportProgress(file);
        this.activeImports.set(importProgress.id, importProgress);

        if (onProgress) {
          onProgress(importProgress);
        }

        try {
          // Update progress to processing
          importProgress.status = ImportStatus.PROCESSING;
          importProgress.progress = 10;
          this.activeImports.set(importProgress.id, importProgress);

          if (onProgress) {
            onProgress(importProgress);
          }

          // Validate file
          const isValid = await this.validateFile(file);
          if (!isValid) {
            throw new Error('Invalid or unsupported file format');
          }

          importProgress.progress = 30;
          this.activeImports.set(importProgress.id, importProgress);

          if (onProgress) {
            onProgress(importProgress);
          }

          // Import document
          const result = await this.documentManager.importDocument(
            file.fileCopyUri || file.uri,
            options,
          );

          if (result.success) {
            importProgress.status = ImportStatus.COMPLETED;
            importProgress.progress = 100;
            importProgress.endTime = new Date();
            importProgress.documentId = result.documentId;
          } else {
            importProgress.status = ImportStatus.FAILED;
            importProgress.error = result.error;
            importProgress.endTime = new Date();
          }

          this.activeImports.set(importProgress.id, importProgress);

          if (onProgress) {
            onProgress(importProgress);
          }

          results.push(result);

          // Clean up temporary file if it was copied
          if (file.fileCopyUri && file.fileCopyUri !== file.uri) {
            await this.cleanupTempFile(file.fileCopyUri);
          }
        } catch (error) {
          importProgress.status = ImportStatus.FAILED;
          importProgress.error =
            error instanceof Error ? error.message : 'Import failed';
          importProgress.endTime = new Date();
          this.activeImports.set(importProgress.id, importProgress);

          if (onProgress) {
            onProgress(importProgress);
          }

          results.push({
            success: false,
            error: importProgress.error,
          });

          this.logger.error(
            `Failed to import document: ${file.name} - ${importProgress.error}`,
          );
        }
      }

      return results;
    } catch (error) {
      this.logger.error(
        'Batch import failed',
        'import',
        undefined,
        error as Error,
      );
      throw new DocumentManagementException(
        DocumentManagementError.IMPORT_FAILED,
        'Batch import operation failed',
        error,
      );
    }
  }

  /**
   * Import single document
   */
  public async importSingleDocument(
    file: DocumentPickerResponse,
    options: ImportOptions = {
      generateThumbnail: true,
      extractMetadata: true,
      overwriteExisting: false,
    },
    onProgress?: (progress: ImportProgress) => void,
  ): Promise<ImportResult> {
    const results = await this.batchImport([file], options, onProgress);
    return results[0];
  }

  /**
   * Get import progress for a specific import
   */
  public getImportProgress(importId: string): ImportProgress | undefined {
    return this.activeImports.get(importId);
  }

  /**
   * Get all active imports
   */
  public getActiveImports(): ImportProgress[] {
    return Array.from(this.activeImports.values());
  }

  /**
   * Cancel an import operation
   */
  public async cancelImport(importId: string): Promise<void> {
    const importProgress = this.activeImports.get(importId);
    if (importProgress && importProgress.status === ImportStatus.PROCESSING) {
      importProgress.status = ImportStatus.CANCELLED;
      importProgress.endTime = new Date();
      this.activeImports.set(importId, importProgress);

      this.logger.info(
        `Import cancelled: ${importProgress.fileName} (${importId})`,
      );
    }
  }

  /**
   * Clear completed imports from tracking
   */
  public clearCompletedImports(): void {
    for (const [id, progress] of this.activeImports.entries()) {
      if (
        progress.status === ImportStatus.COMPLETED ||
        progress.status === ImportStatus.FAILED ||
        progress.status === ImportStatus.CANCELLED
      ) {
        this.activeImports.delete(id);
      }
    }
  }

  /**
   * Get import statistics
   */
  public getImportStats(): {
    total: number;
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    cancelled: number;
  } {
    const imports = Array.from(this.activeImports.values());

    return {
      total: imports.length,
      pending: imports.filter(i => i.status === ImportStatus.PENDING).length,
      processing: imports.filter(i => i.status === ImportStatus.PROCESSING)
        .length,
      completed: imports.filter(i => i.status === ImportStatus.COMPLETED)
        .length,
      failed: imports.filter(i => i.status === ImportStatus.FAILED).length,
      cancelled: imports.filter(i => i.status === ImportStatus.CANCELLED)
        .length,
    };
  }

  // Private helper methods
  private createImportProgress(file: DocumentPickerResponse): ImportProgress {
    return {
      id: `import_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fileName: file.name || 'Unknown',
      filePath: file.fileCopyUri || file.uri,
      fileSize: file.size || 0,
      status: ImportStatus.PENDING,
      progress: 0,
      startTime: new Date(),
    };
  }

  private async validateFile(file: DocumentPickerResponse): Promise<boolean> {
    try {
      // Check if file exists
      const filePath = file.fileCopyUri || file.uri;
      const exists = await RNFS.exists(filePath);
      if (!exists) {
        return false;
      }

      // Check file size (limit to 500MB)
      const maxSize = 500 * 1024 * 1024; // 500MB
      if (file.size && file.size > maxSize) {
        throw new Error('File size exceeds maximum limit (500MB)');
      }

      // Validate MIME type
      const supportedTypes = this.getSupportedMimeTypes();
      if (file.type && !supportedTypes.includes(file.type)) {
        return false;
      }

      // Additional format validation based on file extension
      const extension = this.getFileExtension(file.name || '');
      const supportedExtensions = this.getSupportedExtensions();

      return supportedExtensions.includes(extension.toLowerCase());
    } catch (error) {
      this.logger.error(
        `File validation failed: ${file.name}`,
        'import',
        undefined,
        error as Error,
      );
      return false;
    }
  }

  private getSupportedMimeTypes(): string[] {
    return [
      'application/pdf',
      'application/epub+zip',
      'text/plain',
      'application/rtf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/csv',
      'text/markdown',
      'text/html',
      'application/x-mobipocket-ebook',
      'application/vnd.comicbook+zip',
      'application/vnd.comicbook-rar',
    ];
  }

  private getSupportedExtensions(): string[] {
    return [
      '.pdf',
      '.epub',
      '.txt',
      '.rtf',
      '.docx',
      '.doc',
      '.csv',
      '.md',
      '.markdown',
      '.html',
      '.htm',
      '.mobi',
      '.cbz',
      '.cbr',
    ];
  }

  private getFileExtension(fileName: string): string {
    const lastDotIndex = fileName.lastIndexOf('.');
    return lastDotIndex !== -1 ? fileName.substring(lastDotIndex) : '';
  }

  private async cleanupTempFile(filePath: string): Promise<void> {
    try {
      const exists = await RNFS.exists(filePath);
      if (exists) {
        await RNFS.unlink(filePath);
        this.logger.debug(`Cleaned up temporary file: ${filePath}`);
      }
    } catch (error) {
      this.logger.warn(`Failed to cleanup temporary file: ${filePath}`);
    }
  }
}
