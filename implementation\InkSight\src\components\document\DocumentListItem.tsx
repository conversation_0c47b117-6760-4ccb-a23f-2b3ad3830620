/**
 * Document List Item Component
 * List item for displaying document information in list view
 */

import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import {
  Text,
  Chip,
  IconButton,
  ProgressBar,
  Checkbox,
} from 'react-native-paper';
import { DocumentLibraryItem } from '../../types/document-management';

interface DocumentListItemProps {
  document: DocumentLibraryItem;
  onPress: () => void;
  onLongPress: () => void;
  isSelected: boolean;
}

export const DocumentListItem: React.FC<DocumentListItemProps> = ({
  document,
  onPress,
  onLongPress,
  isSelected,
}) => {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getFormatColor = (format: string): string => {
    const colors: Record<string, string> = {
      pdf: '#FF5722',
      epub: '#4CAF50',
      txt: '#607D8B',
      rtf: '#795548',
      docx: '#2196F3',
      doc: '#2196F3',
      md: '#9C27B0',
      html: '#FF9800',
      csv: '#8BC34A',
    };
    return colors[format.toLowerCase()] || '#757575';
  };

  const formatDate = (date: Date): string => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      onLongPress={onLongPress}
      activeOpacity={0.7}
      style={[styles.container, isSelected && styles.selectedContainer]}
    >
      {/* Selection checkbox */}
      {isSelected && (
        <View style={styles.checkboxContainer}>
          <Checkbox status="checked" />
        </View>
      )}

      {/* Document thumbnail or icon */}
      <View style={styles.thumbnailContainer}>
        {document.thumbnailPath ? (
          <Image
            source={{ uri: document.thumbnailPath }}
            style={styles.thumbnail}
            resizeMode="cover"
          />
        ) : (
          <View
            style={[
              styles.iconContainer,
              { backgroundColor: getFormatColor(document.format) },
            ]}
          >
            <Text style={styles.formatText}>
              {document.format.toUpperCase()}
            </Text>
          </View>
        )}
      </View>

      {/* Document information */}
      <View style={styles.contentContainer}>
        <View style={styles.headerRow}>
          <Text variant="titleMedium" numberOfLines={1} style={styles.title}>
            {document.title}
          </Text>
          {document.isFavorite && (
            <IconButton
              icon="heart"
              iconColor="#E91E63"
              size={16}
              style={styles.favoriteIcon}
            />
          )}
        </View>

        {/* Author and metadata */}
        <View style={styles.metadataRow}>
          {document.author && (
            <Text variant="bodyMedium" numberOfLines={1} style={styles.author}>
              {document.author}
            </Text>
          )}
          <View style={styles.metadataChips}>
            <Chip
              mode="outlined"
              compact
              style={[
                styles.formatChip,
                { borderColor: getFormatColor(document.format) },
              ]}
              textStyle={[
                styles.formatChipText,
                { color: getFormatColor(document.format) },
              ]}
            >
              {document.format.toUpperCase()}
            </Chip>
          </View>
        </View>

        {/* Reading progress */}
        {document.readingProgress > 0 && (
          <View style={styles.progressContainer}>
            <Text variant="bodySmall" style={styles.progressText}>
              {Math.round(document.readingProgress)}% read
            </Text>
            <ProgressBar
              progress={document.readingProgress / 100}
              style={styles.progressBar}
            />
          </View>
        )}

        {/* File info and date */}
        <View style={styles.footerRow}>
          <Text variant="bodySmall" style={styles.fileInfo}>
            {formatFileSize(document.fileSize)}
          </Text>
          <Text variant="bodySmall" style={styles.dateInfo}>
            {document.lastOpenedAt
              ? `Read ${formatDate(document.lastOpenedAt)}`
              : `Added ${formatDate(document.importedAt)}`}
          </Text>
        </View>

        {/* Tags */}
        {document.tags.length > 0 && (
          <View style={styles.tagsContainer}>
            {document.tags.slice(0, 3).map(tagId => (
              <Chip
                key={tagId}
                mode="outlined"
                compact
                style={styles.tagChip}
                textStyle={styles.tagChipText}
              >
                {tagId} {/* TODO: Replace with actual tag name */}
              </Chip>
            ))}
            {document.tags.length > 3 && (
              <Text variant="bodySmall" style={styles.moreTagsText}>
                +{document.tags.length - 3} more
              </Text>
            )}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#FFFFFF',
    alignItems: 'flex-start',
  },
  selectedContainer: {
    backgroundColor: '#E3F2FD',
  },
  checkboxContainer: {
    marginRight: 8,
    justifyContent: 'center',
  },
  thumbnailContainer: {
    width: 60,
    height: 80,
    backgroundColor: '#F5F5F5',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  thumbnail: {
    width: '100%',
    height: '100%',
    borderRadius: 4,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formatText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  contentContainer: {
    flex: 1,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  title: {
    flex: 1,
    fontWeight: '600',
    color: '#212121',
  },
  favoriteIcon: {
    margin: 0,
    padding: 0,
  },
  metadataRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  author: {
    flex: 1,
    color: '#757575',
  },
  metadataChips: {
    flexDirection: 'row',
  },
  formatChip: {
    height: 24,
    backgroundColor: 'transparent',
  },
  formatChipText: {
    fontSize: 10,
    fontWeight: '600',
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressText: {
    color: '#757575',
    marginBottom: 4,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
  },
  footerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  fileInfo: {
    color: '#9E9E9E',
  },
  dateInfo: {
    color: '#9E9E9E',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  tagChip: {
    height: 20,
    marginRight: 4,
    marginBottom: 4,
    backgroundColor: 'transparent',
    borderColor: '#E0E0E0',
  },
  tagChipText: {
    fontSize: 9,
    color: '#757575',
  },
  moreTagsText: {
    color: '#9E9E9E',
    marginLeft: 4,
  },
});
