/**
 * Document Management types and interfaces for InkSight
 * Handles document library, collections, tags, and organization features
 */

import { DocumentMetadata, DocumentFormat } from './document';

// Document Library and Organization
export interface DocumentCollection {
  id: string;
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  documentIds: string[];
  createdAt: Date;
  modifiedAt: Date;
  isSystem?: boolean; // for built-in collections like "Recent", "Favorites"
}

export interface DocumentTag {
  id: string;
  name: string;
  color?: string;
  documentIds: string[];
  createdAt: Date;
  usageCount: number;
}

export interface DocumentLibraryItem extends DocumentMetadata {
  collections: string[]; // collection IDs
  tags: string[]; // tag IDs
  isFavorite: boolean;
  isRecent: boolean;
  importedAt: Date;
  filePath: string;
  thumbnailPath?: string;
  notes?: string;
}

// View and Display Options
export enum DocumentViewMode {
  GRID = 'grid',
  LIST = 'list',
  COMPACT = 'compact',
}

export enum DocumentSortBy {
  NAME = 'name',
  DATE_ADDED = 'dateAdded',
  DATE_MODIFIED = 'dateModified',
  LAST_OPENED = 'lastOpened',
  FILE_SIZE = 'fileSize',
  READING_PROGRESS = 'readingProgress',
  AUTHOR = 'author',
  FORMAT = 'format',
}

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

export interface DocumentFilter {
  formats?: DocumentFormat[];
  collections?: string[];
  tags?: string[];
  isFavorite?: boolean;
  hasNotes?: boolean;
  readingProgress?: {
    min: number;
    max: number;
  };
  dateRange?: {
    start: Date;
    end: Date;
  };
  fileSize?: {
    min: number;
    max: number;
  };
}

export interface DocumentLibraryState {
  documents: DocumentLibraryItem[];
  collections: DocumentCollection[];
  tags: DocumentTag[];
  viewMode: DocumentViewMode;
  sortBy: DocumentSortBy;
  sortOrder: SortOrder;
  filter: DocumentFilter;
  searchQuery: string;
  selectedDocuments: string[];
  isLoading: boolean;
  error: string | null;
}

// Document Import
export interface ImportProgress {
  id: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  status: ImportStatus;
  progress: number; // 0-100
  error?: string;
  startTime: Date;
  endTime?: Date;
  documentId?: string;
}

export enum ImportStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export interface ImportOptions {
  addToCollection?: string;
  addTags?: string[];
  generateThumbnail: boolean;
  extractMetadata: boolean;
  overwriteExisting: boolean;
}

export interface ImportResult {
  success: boolean;
  documentId?: string;
  error?: string;
  warnings?: string[];
  metadata?: DocumentMetadata;
}

export interface BatchImportState {
  imports: ImportProgress[];
  isImporting: boolean;
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
}

// Search and Discovery
export interface DocumentSearchState {
  query: string;
  results: DocumentLibraryItem[];
  isSearching: boolean;
  searchHistory: string[];
  suggestions: string[];
}

export interface SmartCollection {
  id: string;
  name: string;
  description: string;
  rules: SmartCollectionRule[];
  documentIds: string[];
  isEnabled: boolean;
  createdAt: Date;
  modifiedAt: Date;
}

export interface SmartCollectionRule {
  field: keyof DocumentLibraryItem;
  operator:
    | 'equals'
    | 'contains'
    | 'startsWith'
    | 'endsWith'
    | 'greaterThan'
    | 'lessThan'
    | 'between';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

// User Preferences
export interface DocumentLibraryPreferences {
  defaultViewMode: DocumentViewMode;
  defaultSortBy: DocumentSortBy;
  defaultSortOrder: SortOrder;
  gridColumns: number;
  showThumbnails: boolean;
  showMetadata: boolean;
  autoGenerateThumbnails: boolean;
  defaultImportCollection?: string;
  defaultImportTags: string[];
  recentDocumentsLimit: number;
  searchHistoryLimit: number;
}

// Actions and Events
export interface DocumentAction {
  type:
    | 'open'
    | 'edit'
    | 'delete'
    | 'share'
    | 'addToCollection'
    | 'removeFromCollection'
    | 'addTag'
    | 'removeTag'
    | 'favorite'
    | 'unfavorite';
  documentId: string;
  payload?: any;
  timestamp: Date;
}

export interface DocumentEvent {
  id: string;
  type: 'imported' | 'opened' | 'modified' | 'deleted' | 'shared';
  documentId: string;
  details?: any;
  timestamp: Date;
}

// Statistics and Analytics
export interface DocumentLibraryStats {
  totalDocuments: number;
  totalCollections: number;
  totalTags: number;
  formatDistribution: Record<DocumentFormat, number>;
  collectionDistribution: Record<string, number>;
  tagDistribution: Record<string, number>;
  averageDocumentSize: number;
  totalLibrarySize: number;
  mostUsedTags: DocumentTag[];
  largestCollections: DocumentCollection[];
  recentActivity: DocumentEvent[];
}

// Error types
export enum DocumentManagementError {
  COLLECTION_NOT_FOUND = 'COLLECTION_NOT_FOUND',
  TAG_NOT_FOUND = 'TAG_NOT_FOUND',
  DOCUMENT_NOT_FOUND = 'DOCUMENT_NOT_FOUND',
  DUPLICATE_COLLECTION = 'DUPLICATE_COLLECTION',
  DUPLICATE_TAG = 'DUPLICATE_TAG',
  INVALID_FILTER = 'INVALID_FILTER',
  IMPORT_FAILED = 'IMPORT_FAILED',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  STORAGE_FULL = 'STORAGE_FULL',
}

export class DocumentManagementException extends Error {
  constructor(
    public errorType: DocumentManagementError,
    message: string,
    public context?: any,
  ) {
    super(message);
    this.name = 'DocumentManagementException';
  }
}
