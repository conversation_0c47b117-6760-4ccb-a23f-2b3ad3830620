/**
 * Storage Redux Slice - Manages storage state and operations
 * Handles database connections, file storage, and backup operations
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  DatabaseConfig,
  DatabaseStatus,
  StoredFile,
  BackupMetadata,
  FileStorageConfig,
  DEFAULT_DATABASE_CONFIG,
  DEFAULT_FILE_STORAGE_CONFIG,
} from '../../types/storage';

// Storage state interface
export interface StorageState {
  // Database state
  database: {
    config: DatabaseConfig;
    status: DatabaseStatus;
    schemaVersion: number;
    lastBackup: number | null;
    migrationInProgress: boolean;
  };

  // File storage state
  fileStorage: {
    config: FileStorageConfig;
    totalFiles: number;
    totalSize: number; // Total size in bytes
    availableSpace: number; // Available space in bytes
    lastCleanup: number | null;
  };

  // Backup state
  backups: {
    available: BackupMetadata[];
    inProgress: boolean;
    lastBackup: BackupMetadata | null;
    autoBackupEnabled: boolean;
  };

  // File management
  files: {
    stored: Record<string, StoredFile>;
    recentlyAccessed: string[]; // File IDs
    uploadQueue: string[]; // File paths waiting to be processed
  };

  // Loading states
  loading: {
    databaseConnection: boolean;
    fileOperation: boolean;
    backup: boolean;
    restore: boolean;
    migration: boolean;
  };

  // Error state
  error: string | null;
}

// Initial state
const initialState: StorageState = {
  database: {
    config: DEFAULT_DATABASE_CONFIG,
    status: 'disconnected',
    schemaVersion: 0,
    lastBackup: null,
    migrationInProgress: false,
  },
  fileStorage: {
    config: DEFAULT_FILE_STORAGE_CONFIG,
    totalFiles: 0,
    totalSize: 0,
    availableSpace: 0,
    lastCleanup: null,
  },
  backups: {
    available: [],
    inProgress: false,
    lastBackup: null,
    autoBackupEnabled: true,
  },
  files: {
    stored: {},
    recentlyAccessed: [],
    uploadQueue: [],
  },
  loading: {
    databaseConnection: false,
    fileOperation: false,
    backup: false,
    restore: false,
    migration: false,
  },
  error: null,
};

// Async thunks for storage operations

/**
 * Connect to database
 */
export const connectDatabase = createAsyncThunk(
  'storage/connectDatabase',
  async (config: Partial<DatabaseConfig> | undefined, { rejectWithValue }) => {
    try {
      // This would integrate with DatabaseService
      // For now, return mock connection result
      const finalConfig = { ...DEFAULT_DATABASE_CONFIG, ...config };

      return {
        config: finalConfig,
        status: 'connected' as DatabaseStatus,
        schemaVersion: 1,
      };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Database connection failed',
      );
    }
  },
);

/**
 * Disconnect from database
 */
export const disconnectDatabase = createAsyncThunk(
  'storage/disconnectDatabase',
  async (_, { rejectWithValue }) => {
    try {
      // This would integrate with DatabaseService
      return { status: 'disconnected' as DatabaseStatus };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error
          ? error.message
          : 'Database disconnection failed',
      );
    }
  },
);

/**
 * Store file
 */
export const storeFile = createAsyncThunk(
  'storage/storeFile',
  async (
    params: { filePath: string; content: Buffer },
    { rejectWithValue },
  ) => {
    try {
      // This would integrate with FileSystemService
      // For now, return mock stored file
      const storedFile: StoredFile = {
        id: `file-${Date.now()}`,
        originalName: params.filePath.split('/').pop() || 'unknown',
        storedPath: `/stored/${Date.now()}`,
        mimeType: 'application/octet-stream',
        size: params.content.length,
        checksum: 'mock-checksum',
        encrypted: true,
        compressed: false,
        created: Date.now(),
        lastAccessed: Date.now(),
      };

      return storedFile;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'File storage failed',
      );
    }
  },
);

/**
 * Retrieve file
 */
export const retrieveFile = createAsyncThunk(
  'storage/retrieveFile',
  async (fileId: string, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { storage: StorageState };
      const file = state.storage.files.stored[fileId];

      if (!file) {
        throw new Error(`File ${fileId} not found`);
      }

      // This would integrate with FileSystemService
      // For now, return mock buffer
      const content = Buffer.from('mock file content');

      return { fileId, content, file };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'File retrieval failed',
      );
    }
  },
);

/**
 * Delete file
 */
export const deleteFile = createAsyncThunk(
  'storage/deleteFile',
  async (fileId: string, { rejectWithValue }) => {
    try {
      // This would integrate with FileSystemService
      return { fileId, success: true };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'File deletion failed',
      );
    }
  },
);

/**
 * Create backup
 */
export const createBackup = createAsyncThunk(
  'storage/createBackup',
  async (description: string | undefined, { rejectWithValue }) => {
    try {
      // This would integrate with DatabaseService
      const backup: BackupMetadata = {
        id: `backup-${Date.now()}`,
        timestamp: Date.now(),
        version: '1.0.0',
        size: 1024 * 1024, // 1MB mock size
        checksum: 'mock-backup-checksum',
        encrypted: true,
        compressed: true,
        tables: ['documents', 'annotations', 'collections', 'settings'],
        fileCount: 10,
        description,
      };

      return backup;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Backup creation failed',
      );
    }
  },
);

/**
 * List available backups
 */
export const listBackups = createAsyncThunk(
  'storage/listBackups',
  async (_, { rejectWithValue }) => {
    try {
      // This would integrate with DatabaseService
      const backups: BackupMetadata[] = [];
      return backups;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Backup listing failed',
      );
    }
  },
);

/**
 * Get storage statistics
 */
export const getStorageStats = createAsyncThunk(
  'storage/getStats',
  async (_, { rejectWithValue }) => {
    try {
      // This would integrate with FileSystemService
      return {
        totalFiles: 0,
        totalSize: 0,
        availableSpace: 1024 * 1024 * 1024, // 1GB mock
      };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error
          ? error.message
          : 'Storage stats retrieval failed',
      );
    }
  },
);

// Storage slice
const storageSlice = createSlice({
  name: 'storage',
  initialState,
  reducers: {
    // Database actions
    setDatabaseConfig: (
      state,
      action: PayloadAction<Partial<DatabaseConfig>>,
    ) => {
      state.database.config = { ...state.database.config, ...action.payload };
    },

    setDatabaseStatus: (state, action: PayloadAction<DatabaseStatus>) => {
      state.database.status = action.payload;
    },

    setSchemaVersion: (state, action: PayloadAction<number>) => {
      state.database.schemaVersion = action.payload;
    },

    setMigrationInProgress: (state, action: PayloadAction<boolean>) => {
      state.database.migrationInProgress = action.payload;
    },

    // File storage actions
    setFileStorageConfig: (
      state,
      action: PayloadAction<Partial<FileStorageConfig>>,
    ) => {
      state.fileStorage.config = {
        ...state.fileStorage.config,
        ...action.payload,
      };
    },

    addStoredFile: (state, action: PayloadAction<StoredFile>) => {
      state.files.stored[action.payload.id] = action.payload;
      state.fileStorage.totalFiles += 1;
      state.fileStorage.totalSize += action.payload.size;
    },

    removeStoredFile: (state, action: PayloadAction<string>) => {
      const file = state.files.stored[action.payload];
      if (file) {
        delete state.files.stored[action.payload];
        state.fileStorage.totalFiles -= 1;
        state.fileStorage.totalSize -= file.size;

        // Remove from recently accessed
        state.files.recentlyAccessed = state.files.recentlyAccessed.filter(
          id => id !== action.payload,
        );
      }
    },

    updateFileLastAccessed: (state, action: PayloadAction<string>) => {
      const file = state.files.stored[action.payload];
      if (file) {
        file.lastAccessed = Date.now();

        // Update recently accessed list
        state.files.recentlyAccessed = [
          action.payload,
          ...state.files.recentlyAccessed.filter(id => id !== action.payload),
        ].slice(0, 10); // Keep only last 10
      }
    },

    // Upload queue actions
    addToUploadQueue: (state, action: PayloadAction<string>) => {
      if (!state.files.uploadQueue.includes(action.payload)) {
        state.files.uploadQueue.push(action.payload);
      }
    },

    removeFromUploadQueue: (state, action: PayloadAction<string>) => {
      state.files.uploadQueue = state.files.uploadQueue.filter(
        path => path !== action.payload,
      );
    },

    clearUploadQueue: state => {
      state.files.uploadQueue = [];
    },

    // Backup actions
    setAutoBackupEnabled: (state, action: PayloadAction<boolean>) => {
      state.backups.autoBackupEnabled = action.payload;
    },

    addBackup: (state, action: PayloadAction<BackupMetadata>) => {
      state.backups.available.push(action.payload);
      state.backups.lastBackup = action.payload;
      state.database.lastBackup = action.payload.timestamp;
    },

    removeBackup: (state, action: PayloadAction<string>) => {
      state.backups.available = state.backups.available.filter(
        backup => backup.id !== action.payload,
      );
    },

    // Error handling
    clearError: state => {
      state.error = null;
    },

    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
  },
  extraReducers: builder => {
    // Connect database
    builder
      .addCase(connectDatabase.pending, state => {
        state.loading.databaseConnection = true;
        state.error = null;
      })
      .addCase(connectDatabase.fulfilled, (state, action) => {
        state.loading.databaseConnection = false;
        state.database.config = action.payload.config;
        state.database.status = action.payload.status;
        state.database.schemaVersion = action.payload.schemaVersion;
      })
      .addCase(connectDatabase.rejected, (state, action) => {
        state.loading.databaseConnection = false;
        state.error = action.payload as string;
      });

    // Disconnect database
    builder.addCase(disconnectDatabase.fulfilled, (state, action) => {
      state.database.status = action.payload.status;
    });

    // Store file
    builder
      .addCase(storeFile.pending, state => {
        state.loading.fileOperation = true;
        state.error = null;
      })
      .addCase(storeFile.fulfilled, (state, action) => {
        state.loading.fileOperation = false;
        state.files.stored[action.payload.id] = action.payload;
        state.fileStorage.totalFiles += 1;
        state.fileStorage.totalSize += action.payload.size;
      })
      .addCase(storeFile.rejected, (state, action) => {
        state.loading.fileOperation = false;
        state.error = action.payload as string;
      });

    // Retrieve file
    builder
      .addCase(retrieveFile.pending, state => {
        state.loading.fileOperation = true;
        state.error = null;
      })
      .addCase(retrieveFile.fulfilled, (state, action) => {
        state.loading.fileOperation = false;
        // Update last accessed
        const file = state.files.stored[action.payload.fileId];
        if (file) {
          file.lastAccessed = Date.now();
          state.files.recentlyAccessed = [
            action.payload.fileId,
            ...state.files.recentlyAccessed.filter(
              id => id !== action.payload.fileId,
            ),
          ].slice(0, 10);
        }
      })
      .addCase(retrieveFile.rejected, (state, action) => {
        state.loading.fileOperation = false;
        state.error = action.payload as string;
      });

    // Delete file
    builder.addCase(deleteFile.fulfilled, (state, action) => {
      const file = state.files.stored[action.payload.fileId];
      if (file) {
        delete state.files.stored[action.payload.fileId];
        state.fileStorage.totalFiles -= 1;
        state.fileStorage.totalSize -= file.size;
        state.files.recentlyAccessed = state.files.recentlyAccessed.filter(
          id => id !== action.payload.fileId,
        );
      }
    });

    // Create backup
    builder
      .addCase(createBackup.pending, state => {
        state.loading.backup = true;
        state.backups.inProgress = true;
        state.error = null;
      })
      .addCase(createBackup.fulfilled, (state, action) => {
        state.loading.backup = false;
        state.backups.inProgress = false;
        state.backups.available.push(action.payload);
        state.backups.lastBackup = action.payload;
        state.database.lastBackup = action.payload.timestamp;
      })
      .addCase(createBackup.rejected, (state, action) => {
        state.loading.backup = false;
        state.backups.inProgress = false;
        state.error = action.payload as string;
      });

    // Get storage stats
    builder.addCase(getStorageStats.fulfilled, (state, action) => {
      state.fileStorage.totalFiles = action.payload.totalFiles;
      state.fileStorage.totalSize = action.payload.totalSize;
      state.fileStorage.availableSpace = action.payload.availableSpace;
    });
  },
});

// Export actions
export const {
  setDatabaseConfig,
  setDatabaseStatus,
  setSchemaVersion,
  setMigrationInProgress,
  setFileStorageConfig,
  addStoredFile,
  removeStoredFile,
  updateFileLastAccessed,
  addToUploadQueue,
  removeFromUploadQueue,
  clearUploadQueue,
  setAutoBackupEnabled,
  addBackup,
  removeBackup,
  clearError,
  setError,
} = storageSlice.actions;

// Export reducer
export default storageSlice.reducer;

// Selectors
export const selectDatabaseStatus = (state: { storage: StorageState }) =>
  state.storage.database.status;
export const selectDatabaseConfig = (state: { storage: StorageState }) =>
  state.storage.database.config;
export const selectStoredFiles = (state: { storage: StorageState }) =>
  state.storage.files.stored;
export const selectRecentlyAccessedFiles = (state: { storage: StorageState }) =>
  state.storage.files.recentlyAccessed;
export const selectFileStorageStats = (state: { storage: StorageState }) =>
  state.storage.fileStorage;
export const selectBackups = (state: { storage: StorageState }) =>
  state.storage.backups;
export const selectStorageError = (state: { storage: StorageState }) =>
  state.storage.error;
export const selectStorageLoading = (state: { storage: StorageState }) =>
  state.storage.loading;
