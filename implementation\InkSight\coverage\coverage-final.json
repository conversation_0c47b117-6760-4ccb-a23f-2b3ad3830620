{"C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\annotation\\AnnotationManager.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\annotation\\AnnotationManager.tsx", "statementMap": {"0": {"start": {"line": 42, "column": 60}, "end": {"line": 363, "column": 1}}, "1": {"start": {"line": 46, "column": 19}, "end": {"line": 46, "column": 45}}, "2": {"start": {"line": 47, "column": 16}, "end": {"line": 47, "column": 47}}, "3": {"start": {"line": 49, "column": 22}, "end": {"line": 49, "column": 70}}, "4": {"start": {"line": 50, "column": 29}, "end": {"line": 50, "column": 66}}, "5": {"start": {"line": 51, "column": 20}, "end": {"line": 51, "column": 58}}, "6": {"start": {"line": 52, "column": 20}, "end": {"line": 52, "column": 48}}, "7": {"start": {"line": 54, "column": 46}, "end": {"line": 54, "column": 61}}, "8": {"start": {"line": 55, "column": 40}, "end": {"line": 55, "column": 77}}, "9": {"start": {"line": 56, "column": 38}, "end": {"line": 56, "column": 77}}, "10": {"start": {"line": 59, "column": 2}, "end": {"line": 63, "column": 29}}, "11": {"start": {"line": 60, "column": 4}, "end": {"line": 62, "column": 5}}, "12": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 55}}, "13": {"start": {"line": 66, "column": 30}, "end": {"line": 68, "column": 3}}, "14": {"start": {"line": 67, "column": 18}, "end": {"line": 67, "column": 72}}, "15": {"start": {"line": 71, "column": 33}, "end": {"line": 76, "column": 3}}, "16": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 53}}, "17": {"start": {"line": 79, "column": 33}, "end": {"line": 84, "column": 3}}, "18": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 53}}, "19": {"start": {"line": 87, "column": 25}, "end": {"line": 90, "column": 8}}, "20": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 25}}, "21": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 28}}, "22": {"start": {"line": 93, "column": 32}, "end": {"line": 96, "column": 8}}, "23": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": 29}}, "24": {"start": {"line": 95, "column": 4}, "end": {"line": 95, "column": 25}}, "25": {"start": {"line": 99, "column": 27}, "end": {"line": 102, "column": 16}}, "26": {"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": 44}}, "27": {"start": {"line": 101, "column": 4}, "end": {"line": 101, "column": 42}}, "28": {"start": {"line": 105, "column": 28}, "end": {"line": 120, "column": 3}}, "29": {"start": {"line": 106, "column": 4}, "end": {"line": 119, "column": 5}}, "30": {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 21}}, "31": {"start": {"line": 110, "column": 8}, "end": {"line": 110, "column": 20}}, "32": {"start": {"line": 112, "column": 8}, "end": {"line": 112, "column": 20}}, "33": {"start": {"line": 114, "column": 8}, "end": {"line": 114, "column": 20}}, "34": {"start": {"line": 116, "column": 8}, "end": {"line": 116, "column": 19}}, "35": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 20}}, "36": {"start": {"line": 123, "column": 31}, "end": {"line": 133, "column": 3}}, "37": {"start": {"line": 124, "column": 4}, "end": {"line": 129, "column": 5}}, "38": {"start": {"line": 125, "column": 19}, "end": {"line": 125, "column": 47}}, "39": {"start": {"line": 126, "column": 6}, "end": {"line": 128, "column": 23}}, "40": {"start": {"line": 130, "column": 4}, "end": {"line": 132, "column": 42}}, "41": {"start": {"line": 136, "column": 31}, "end": {"line": 223, "column": 3}}, "42": {"start": {"line": 137, "column": 4}, "end": {"line": 222, "column": 23}}, "43": {"start": {"line": 149, "column": 21}, "end": {"line": 149, "column": 49}}, "44": {"start": {"line": 197, "column": 29}, "end": {"line": 197, "column": 67}}, "45": {"start": {"line": 212, "column": 27}, "end": {"line": 212, "column": 58}}, "46": {"start": {"line": 226, "column": 30}, "end": {"line": 266, "column": 3}}, "47": {"start": {"line": 227, "column": 4}, "end": {"line": 265, "column": 11}}, "48": {"start": {"line": 234, "column": 8}, "end": {"line": 263, "column": 27}}, "49": {"start": {"line": 246, "column": 25}, "end": {"line": 246, "column": 70}}, "50": {"start": {"line": 268, "column": 2}, "end": {"line": 362, "column": 4}}, "51": {"start": {"line": 344, "column": 36}, "end": {"line": 344, "column": 43}}, "52": {"start": {"line": 365, "column": 15}, "end": {"line": 490, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 42, "column": 60}, "end": {"line": 42, "column": 61}}, "loc": {"start": {"line": 45, "column": 6}, "end": {"line": 363, "column": 1}}, "line": 45}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 59, "column": 12}, "end": {"line": 59, "column": 13}}, "loc": {"start": {"line": 59, "column": 18}, "end": {"line": 63, "column": 3}}, "line": 59}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 5}}, "loc": {"start": {"line": 67, "column": 18}, "end": {"line": 67, "column": 72}}, "line": 67}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 5}}, "loc": {"start": {"line": 72, "column": 32}, "end": {"line": 74, "column": 5}}, "line": 72}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 5}}, "loc": {"start": {"line": 80, "column": 36}, "end": {"line": 82, "column": 5}}, "line": 80}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 87, "column": 37}, "end": {"line": 87, "column": 38}}, "loc": {"start": {"line": 87, "column": 63}, "end": {"line": 90, "column": 3}}, "line": 87}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 93, "column": 44}, "end": {"line": 93, "column": 45}}, "loc": {"start": {"line": 93, "column": 50}, "end": {"line": 96, "column": 3}}, "line": 93}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 99, "column": 39}, "end": {"line": 99, "column": 40}}, "loc": {"start": {"line": 99, "column": 45}, "end": {"line": 102, "column": 3}}, "line": 99}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 105, "column": 28}, "end": {"line": 105, "column": 29}}, "loc": {"start": {"line": 105, "column": 54}, "end": {"line": 120, "column": 3}}, "line": 105}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 123, "column": 31}, "end": {"line": 123, "column": 32}}, "loc": {"start": {"line": 123, "column": 59}, "end": {"line": 133, "column": 3}}, "line": 123}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 136, "column": 31}, "end": {"line": 136, "column": 32}}, "loc": {"start": {"line": 137, "column": 4}, "end": {"line": 222, "column": 23}}, "line": 137}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 149, "column": 15}, "end": {"line": 149, "column": 16}}, "loc": {"start": {"line": 149, "column": 21}, "end": {"line": 149, "column": 49}}, "line": 149}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 197, "column": 23}, "end": {"line": 197, "column": 24}}, "loc": {"start": {"line": 197, "column": 29}, "end": {"line": 197, "column": 67}}, "line": 197}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 212, "column": 21}, "end": {"line": 212, "column": 22}}, "loc": {"start": {"line": 212, "column": 27}, "end": {"line": 212, "column": 58}}, "line": 212}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 226, "column": 30}, "end": {"line": 226, "column": 31}}, "loc": {"start": {"line": 227, "column": 4}, "end": {"line": 265, "column": 11}}, "line": 227}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 233, "column": 12}, "end": {"line": 233, "column": 13}}, "loc": {"start": {"line": 234, "column": 8}, "end": {"line": 263, "column": 27}}, "line": 234}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 246, "column": 19}, "end": {"line": 246, "column": 20}}, "loc": {"start": {"line": 246, "column": 25}, "end": {"line": 246, "column": 70}}, "line": 246}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 344, "column": 28}, "end": {"line": 344, "column": 29}}, "loc": {"start": {"line": 344, "column": 36}, "end": {"line": 344, "column": 43}}, "line": 344}}, "branchMap": {"0": {"loc": {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 44, "column": 11}, "end": {"line": 44, "column": 16}}], "line": 44}, "1": {"loc": {"start": {"line": 47, "column": 16}, "end": {"line": 47, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 47, "column": 25}, "end": {"line": 47, "column": 34}}, {"start": {"line": 47, "column": 37}, "end": {"line": 47, "column": 47}}], "line": 47}, "2": {"loc": {"start": {"line": 60, "column": 4}, "end": {"line": 62, "column": 5}}, "type": "if", "locations": [{"start": {"line": 60, "column": 4}, "end": {"line": 62, "column": 5}}, {"start": {}, "end": {}}], "line": 60}, "3": {"loc": {"start": {"line": 67, "column": 18}, "end": {"line": 67, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 18}, "end": {"line": 67, "column": 38}}, {"start": {"line": 67, "column": 42}, "end": {"line": 67, "column": 72}}], "line": 67}, "4": {"loc": {"start": {"line": 106, "column": 4}, "end": {"line": 119, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 107, "column": 6}, "end": {"line": 108, "column": 21}}, {"start": {"line": 109, "column": 6}, "end": {"line": 110, "column": 20}}, {"start": {"line": 111, "column": 6}, "end": {"line": 112, "column": 20}}, {"start": {"line": 113, "column": 6}, "end": {"line": 114, "column": 20}}, {"start": {"line": 115, "column": 6}, "end": {"line": 116, "column": 19}}, {"start": {"line": 117, "column": 6}, "end": {"line": 118, "column": 20}}], "line": 106}, "5": {"loc": {"start": {"line": 124, "column": 4}, "end": {"line": 129, "column": 5}}, "type": "if", "locations": [{"start": {"line": 124, "column": 4}, "end": {"line": 129, "column": 5}}, {"start": {}, "end": {}}], "line": 124}, "6": {"loc": {"start": {"line": 126, "column": 13}, "end": {"line": 128, "column": 22}}, "type": "cond-expr", "locations": [{"start": {"line": 127, "column": 10}, "end": {"line": 127, "column": 48}}, {"start": {"line": 128, "column": 10}, "end": {"line": 128, "column": 22}}], "line": 126}, "7": {"loc": {"start": {"line": 130, "column": 11}, "end": {"line": 132, "column": 41}}, "type": "cond-expr", "locations": [{"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": 66}}, {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 41}}], "line": 130}, "8": {"loc": {"start": {"line": 144, "column": 8}, "end": {"line": 147, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 144, "column": 8}, "end": {"line": 144, "column": 42}}, {"start": {"line": 144, "column": 46}, "end": {"line": 147, "column": 9}}], "line": 144}, "9": {"loc": {"start": {"line": 164, "column": 9}, "end": {"line": 171, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 164, "column": 9}, "end": {"line": 164, "column": 47}}, {"start": {"line": 165, "column": 10}, "end": {"line": 170, "column": 12}}], "line": 164}, "10": {"loc": {"start": {"line": 194, "column": 11}, "end": {"line": 208, "column": 11}}, "type": "binary-expr", "locations": [{"start": {"line": 194, "column": 11}, "end": {"line": 194, "column": 44}}, {"start": {"line": 195, "column": 12}, "end": {"line": 207, "column": 31}}], "line": 194}, "11": {"loc": {"start": {"line": 240, "column": 16}, "end": {"line": 242, "column": 40}}, "type": "cond-expr", "locations": [{"start": {"line": 241, "column": 20}, "end": {"line": 241, "column": 40}}, {"start": {"line": 242, "column": 20}, "end": {"line": 242, "column": 40}}], "line": 240}, "12": {"loc": {"start": {"line": 253, "column": 18}, "end": {"line": 255, "column": 44}}, "type": "cond-expr", "locations": [{"start": {"line": 254, "column": 22}, "end": {"line": 254, "column": 44}}, {"start": {"line": 255, "column": 22}, "end": {"line": 255, "column": 44}}], "line": 253}, "13": {"loc": {"start": {"line": 259, "column": 13}, "end": {"line": 261, "column": 60}}, "type": "cond-expr", "locations": [{"start": {"line": 260, "column": 16}, "end": {"line": 260, "column": 21}}, {"start": {"line": 261, "column": 16}, "end": {"line": 261, "column": 60}}], "line": 259}, "14": {"loc": {"start": {"line": 310, "column": 11}, "end": {"line": 349, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 311, "column": 12}, "end": {"line": 320, "column": 19}}, {"start": {"line": 321, "column": 14}, "end": {"line": 349, "column": 11}}], "line": 310}, "15": {"loc": {"start": {"line": 321, "column": 14}, "end": {"line": 349, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 322, "column": 12}, "end": {"line": 339, "column": 19}}, {"start": {"line": 341, "column": 12}, "end": {"line": 348, "column": 14}}], "line": 321}, "16": {"loc": {"start": {"line": 359, "column": 22}, "end": {"line": 359, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 359, "column": 22}, "end": {"line": 359, "column": 33}}, {"start": {"line": 359, "column": 37}, "end": {"line": 359, "column": 46}}], "line": 359}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0, 0, 0, 0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\annotation\\ColorPicker.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\annotation\\ColorPicker.tsx", "statementMap": {"0": {"start": {"line": 21, "column": 48}, "end": {"line": 115, "column": 1}}, "1": {"start": {"line": 30, "column": 16}, "end": {"line": 30, "column": 47}}, "2": {"start": {"line": 33, "column": 23}, "end": {"line": 42, "column": 3}}, "3": {"start": {"line": 45, "column": 28}, "end": {"line": 56, "column": 3}}, "4": {"start": {"line": 46, "column": 4}, "end": {"line": 55, "column": 5}}, "5": {"start": {"line": 48, "column": 8}, "end": {"line": 48, "column": 59}}, "6": {"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 59}}, "7": {"start": {"line": 52, "column": 8}, "end": {"line": 52, "column": 59}}, "8": {"start": {"line": 54, "column": 8}, "end": {"line": 54, "column": 59}}, "9": {"start": {"line": 58, "column": 25}, "end": {"line": 58, "column": 44}}, "10": {"start": {"line": 61, "column": 28}, "end": {"line": 82, "column": 3}}, "11": {"start": {"line": 62, "column": 22}, "end": {"line": 64, "column": 5}}, "12": {"start": {"line": 66, "column": 4}, "end": {"line": 73, "column": 5}}, "13": {"start": {"line": 67, "column": 6}, "end": {"line": 72, "column": 8}}, "14": {"start": {"line": 75, "column": 4}, "end": {"line": 81, "column": 6}}, "15": {"start": {"line": 84, "column": 2}, "end": {"line": 114, "column": 4}}, "16": {"start": {"line": 94, "column": 10}, "end": {"line": 110, "column": 12}}, "17": {"start": {"line": 105, "column": 27}, "end": {"line": 105, "column": 47}}, "18": {"start": {"line": 117, "column": 15}, "end": {"line": 148, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 21, "column": 48}, "end": {"line": 21, "column": 49}}, "loc": {"start": {"line": 29, "column": 6}, "end": {"line": 115, "column": 1}}, "line": 29}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 45, "column": 28}, "end": {"line": 45, "column": 29}}, "loc": {"start": {"line": 45, "column": 34}, "end": {"line": 56, "column": 3}}, "line": 45}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 61, "column": 28}, "end": {"line": 61, "column": 29}}, "loc": {"start": {"line": 61, "column": 34}, "end": {"line": 82, "column": 3}}, "line": 61}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 93, "column": 26}, "end": {"line": 93, "column": 27}}, "loc": {"start": {"line": 94, "column": 10}, "end": {"line": 110, "column": 12}}, "line": 94}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 105, "column": 21}, "end": {"line": 105, "column": 22}}, "loc": {"start": {"line": 105, "column": 27}, "end": {"line": 105, "column": 47}}, "line": 105}}, "branchMap": {"0": {"loc": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 24, "column": 11}, "end": {"line": 24, "column": 16}}], "line": 24}, "1": {"loc": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 24}}, "type": "default-arg", "locations": [{"start": {"line": 25, "column": 10}, "end": {"line": 25, "column": 24}}], "line": 25}, "2": {"loc": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 18}}, "type": "default-arg", "locations": [{"start": {"line": 26, "column": 14}, "end": {"line": 26, "column": 18}}], "line": 26}, "3": {"loc": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 17}}, "type": "default-arg", "locations": [{"start": {"line": 27, "column": 9}, "end": {"line": 27, "column": 17}}], "line": 27}, "4": {"loc": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 17}}, "type": "default-arg", "locations": [{"start": {"line": 28, "column": 11}, "end": {"line": 28, "column": 17}}], "line": 28}, "5": {"loc": {"start": {"line": 30, "column": 16}, "end": {"line": 30, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 30, "column": 25}, "end": {"line": 30, "column": 34}}, {"start": {"line": 30, "column": 37}, "end": {"line": 30, "column": 47}}], "line": 30}, "6": {"loc": {"start": {"line": 46, "column": 4}, "end": {"line": 55, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 47, "column": 6}, "end": {"line": 48, "column": 59}}, {"start": {"line": 49, "column": 6}, "end": {"line": 50, "column": 59}}, {"start": {"line": 51, "column": 6}, "end": {"line": 52, "column": 59}}, {"start": {"line": 53, "column": 6}, "end": {"line": 54, "column": 59}}], "line": 46}, "7": {"loc": {"start": {"line": 63, "column": 11}, "end": {"line": 63, "column": 61}}, "type": "cond-expr", "locations": [{"start": {"line": 63, "column": 30}, "end": {"line": 63, "column": 31}}, {"start": {"line": 63, "column": 34}, "end": {"line": 63, "column": 61}}], "line": 63}, "8": {"loc": {"start": {"line": 63, "column": 34}, "end": {"line": 63, "column": 61}}, "type": "cond-expr", "locations": [{"start": {"line": 63, "column": 54}, "end": {"line": 63, "column": 56}}, {"start": {"line": 63, "column": 59}, "end": {"line": 63, "column": 61}}], "line": 63}, "9": {"loc": {"start": {"line": 66, "column": 4}, "end": {"line": 73, "column": 5}}, "type": "if", "locations": [{"start": {"line": 66, "column": 4}, "end": {"line": 73, "column": 5}}, {"start": {}, "end": {}}], "line": 66}, "10": {"loc": {"start": {"line": 80, "column": 16}, "end": {"line": 80, "column": 51}}, "type": "cond-expr", "locations": [{"start": {"line": 80, "column": 36}, "end": {"line": 80, "column": 39}}, {"start": {"line": 80, "column": 42}, "end": {"line": 80, "column": 51}}], "line": 80}, "11": {"loc": {"start": {"line": 86, "column": 7}, "end": {"line": 90, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 86, "column": 7}, "end": {"line": 86, "column": 16}}, {"start": {"line": 87, "column": 8}, "end": {"line": 89, "column": 15}}], "line": 86}, "12": {"loc": {"start": {"line": 100, "column": 14}, "end": {"line": 103, "column": 15}}, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 14}, "end": {"line": 100, "column": 37}}, {"start": {"line": 100, "column": 41}, "end": {"line": 103, "column": 15}}], "line": 100}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0, 0], "6": [0, 0, 0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\annotation\\HighlightSelector.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\annotation\\HighlightSelector.tsx", "statementMap": {"0": {"start": {"line": 42, "column": 60}, "end": {"line": 265, "column": 1}}, "1": {"start": {"line": 49, "column": 19}, "end": {"line": 49, "column": 45}}, "2": {"start": {"line": 50, "column": 16}, "end": {"line": 50, "column": 47}}, "3": {"start": {"line": 51, "column": 25}, "end": {"line": 51, "column": 58}}, "4": {"start": {"line": 52, "column": 26}, "end": {"line": 52, "column": 60}}, "5": {"start": {"line": 55, "column": 40}, "end": {"line": 55, "column": 55}}, "6": {"start": {"line": 56, "column": 46}, "end": {"line": 59, "column": 17}}, "7": {"start": {"line": 60, "column": 42}, "end": {"line": 63, "column": 17}}, "8": {"start": {"line": 64, "column": 48}, "end": {"line": 64, "column": 63}}, "9": {"start": {"line": 67, "column": 26}, "end": {"line": 67, "column": 55}}, "10": {"start": {"line": 70, "column": 31}, "end": {"line": 79, "column": 3}}, "11": {"start": {"line": 72, "column": 31}, "end": {"line": 72, "column": 48}}, "12": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 27}}, "13": {"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": 48}}, "14": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 28}}, "15": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 27}}, "16": {"start": {"line": 82, "column": 30}, "end": {"line": 89, "column": 3}}, "17": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 31}}, "18": {"start": {"line": 84, "column": 24}, "end": {"line": 84, "column": 31}}, "19": {"start": {"line": 85, "column": 31}, "end": {"line": 85, "column": 48}}, "20": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 46}}, "21": {"start": {"line": 92, "column": 29}, "end": {"line": 116, "column": 75}}, "22": {"start": {"line": 93, "column": 4}, "end": {"line": 96, "column": 5}}, "23": {"start": {"line": 94, "column": 6}, "end": {"line": 94, "column": 28}}, "24": {"start": {"line": 95, "column": 6}, "end": {"line": 95, "column": 13}}, "25": {"start": {"line": 98, "column": 4}, "end": {"line": 98, "column": 26}}, "26": {"start": {"line": 103, "column": 37}, "end": {"line": 111, "column": 5}}, "27": {"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 44}}, "28": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 32}}, "29": {"start": {"line": 115, "column": 4}, "end": {"line": 115, "column": 29}}, "30": {"start": {"line": 119, "column": 23}, "end": {"line": 125, "column": 4}}, "31": {"start": {"line": 120, "column": 40}, "end": {"line": 120, "column": 44}}, "32": {"start": {"line": 121, "column": 39}, "end": {"line": 121, "column": 43}}, "33": {"start": {"line": 128, "column": 28}, "end": {"line": 164, "column": 3}}, "34": {"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 41}}, "35": {"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": 32}}, "36": {"start": {"line": 133, "column": 6}, "end": {"line": 161, "column": 7}}, "37": {"start": {"line": 135, "column": 36}, "end": {"line": 153, "column": 9}}, "38": {"start": {"line": 155, "column": 8}, "end": {"line": 155, "column": 56}}, "39": {"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 43}}, "40": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": 32}}, "41": {"start": {"line": 160, "column": 8}, "end": {"line": 160, "column": 30}}, "42": {"start": {"line": 167, "column": 32}, "end": {"line": 172, "column": 16}}, "43": {"start": {"line": 168, "column": 4}, "end": {"line": 168, "column": 30}}, "44": {"start": {"line": 169, "column": 4}, "end": {"line": 169, "column": 39}}, "45": {"start": {"line": 170, "column": 4}, "end": {"line": 170, "column": 28}}, "46": {"start": {"line": 171, "column": 4}, "end": {"line": 171, "column": 26}}, "47": {"start": {"line": 175, "column": 33}, "end": {"line": 198, "column": 3}}, "48": {"start": {"line": 176, "column": 4}, "end": {"line": 176, "column": 70}}, "49": {"start": {"line": 176, "column": 58}, "end": {"line": 176, "column": 70}}, "50": {"start": {"line": 178, "column": 17}, "end": {"line": 178, "column": 59}}, "51": {"start": {"line": 179, "column": 16}, "end": {"line": 179, "column": 58}}, "52": {"start": {"line": 180, "column": 18}, "end": {"line": 180, "column": 61}}, "53": {"start": {"line": 181, "column": 19}, "end": {"line": 181, "column": 62}}, "54": {"start": {"line": 183, "column": 4}, "end": {"line": 197, "column": 6}}, "55": {"start": {"line": 201, "column": 28}, "end": {"line": 257, "column": 3}}, "56": {"start": {"line": 202, "column": 4}, "end": {"line": 256, "column": 12}}, "57": {"start": {"line": 223, "column": 14}, "end": {"line": 232, "column": 16}}, "58": {"start": {"line": 230, "column": 31}, "end": {"line": 230, "column": 55}}, "59": {"start": {"line": 259, "column": 2}, "end": {"line": 264, "column": 4}}, "60": {"start": {"line": 267, "column": 15}, "end": {"line": 335, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 42, "column": 60}, "end": {"line": 42, "column": 61}}, "loc": {"start": {"line": 48, "column": 6}, "end": {"line": 265, "column": 1}}, "line": 48}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 5}}, "loc": {"start": {"line": 71, "column": 38}, "end": {"line": 77, "column": 5}}, "line": 71}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 5}}, "loc": {"start": {"line": 83, "column": 38}, "end": {"line": 87, "column": 5}}, "line": 83}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 92, "column": 41}, "end": {"line": 92, "column": 42}}, "loc": {"start": {"line": 92, "column": 47}, "end": {"line": 116, "column": 3}}, "line": 92}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 120, "column": 34}, "end": {"line": 120, "column": 35}}, "loc": {"start": {"line": 120, "column": 40}, "end": {"line": 120, "column": 44}}, "line": 120}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 121, "column": 33}, "end": {"line": 121, "column": 34}}, "loc": {"start": {"line": 121, "column": 39}, "end": {"line": 121, "column": 43}}, "line": 121}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": 5}}, "loc": {"start": {"line": 129, "column": 31}, "end": {"line": 162, "column": 5}}, "line": 129}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 167, "column": 44}, "end": {"line": 167, "column": 45}}, "loc": {"start": {"line": 167, "column": 50}, "end": {"line": 172, "column": 3}}, "line": 167}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 175, "column": 33}, "end": {"line": 175, "column": 34}}, "loc": {"start": {"line": 175, "column": 39}, "end": {"line": 198, "column": 3}}, "line": 175}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 201, "column": 28}, "end": {"line": 201, "column": 29}}, "loc": {"start": {"line": 202, "column": 4}, "end": {"line": 256, "column": 12}}, "line": 202}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 222, "column": 33}, "end": {"line": 222, "column": 34}}, "loc": {"start": {"line": 223, "column": 14}, "end": {"line": 232, "column": 16}}, "line": 223}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 230, "column": 25}, "end": {"line": 230, "column": 26}}, "loc": {"start": {"line": 230, "column": 31}, "end": {"line": 230, "column": 55}}, "line": 230}}, "branchMap": {"0": {"loc": {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 44, "column": 11}, "end": {"line": 44, "column": 16}}], "line": 44}, "1": {"loc": {"start": {"line": 50, "column": 16}, "end": {"line": 50, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 50, "column": 25}, "end": {"line": 50, "column": 34}}, {"start": {"line": 50, "column": 37}, "end": {"line": 50, "column": 47}}], "line": 50}, "2": {"loc": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 31}}, "type": "if", "locations": [{"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 31}}, {"start": {}, "end": {}}], "line": 84}, "3": {"loc": {"start": {"line": 93, "column": 4}, "end": {"line": 96, "column": 5}}, "type": "if", "locations": [{"start": {"line": 93, "column": 4}, "end": {"line": 96, "column": 5}}, {"start": {}, "end": {}}], "line": 93}, "4": {"loc": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 20}}, {"start": {"line": 93, "column": 24}, "end": {"line": 93, "column": 39}}, {"start": {"line": 93, "column": 43}, "end": {"line": 93, "column": 56}}], "line": 93}, "5": {"loc": {"start": {"line": 133, "column": 6}, "end": {"line": 161, "column": 7}}, "type": "if", "locations": [{"start": {"line": 133, "column": 6}, "end": {"line": 161, "column": 7}}, {"start": {}, "end": {}}], "line": 133}, "6": {"loc": {"start": {"line": 143, "column": 17}, "end": {"line": 143, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 143, "column": 17}, "end": {"line": 143, "column": 34}}, {"start": {"line": 143, "column": 38}, "end": {"line": 143, "column": 39}}], "line": 143}, "7": {"loc": {"start": {"line": 144, "column": 17}, "end": {"line": 144, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 144, "column": 17}, "end": {"line": 144, "column": 34}}, {"start": {"line": 144, "column": 38}, "end": {"line": 144, "column": 39}}], "line": 144}, "8": {"loc": {"start": {"line": 146, "column": 17}, "end": {"line": 146, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 146, "column": 17}, "end": {"line": 146, "column": 32}}, {"start": {"line": 146, "column": 36}, "end": {"line": 146, "column": 37}}], "line": 146}, "9": {"loc": {"start": {"line": 146, "column": 42}, "end": {"line": 146, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 146, "column": 42}, "end": {"line": 146, "column": 59}}, {"start": {"line": 146, "column": 63}, "end": {"line": 146, "column": 64}}], "line": 146}, "10": {"loc": {"start": {"line": 149, "column": 17}, "end": {"line": 149, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 149, "column": 17}, "end": {"line": 149, "column": 32}}, {"start": {"line": 149, "column": 36}, "end": {"line": 149, "column": 37}}], "line": 149}, "11": {"loc": {"start": {"line": 149, "column": 42}, "end": {"line": 149, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 149, "column": 42}, "end": {"line": 149, "column": 59}}, {"start": {"line": 149, "column": 63}, "end": {"line": 149, "column": 64}}], "line": 149}, "12": {"loc": {"start": {"line": 176, "column": 4}, "end": {"line": 176, "column": 70}}, "type": "if", "locations": [{"start": {"line": 176, "column": 4}, "end": {"line": 176, "column": 70}}, {"start": {}, "end": {}}], "line": 176}, "13": {"loc": {"start": {"line": 176, "column": 8}, "end": {"line": 176, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 176, "column": 8}, "end": {"line": 176, "column": 20}}, {"start": {"line": 176, "column": 24}, "end": {"line": 176, "column": 39}}, {"start": {"line": 176, "column": 43}, "end": {"line": 176, "column": 56}}], "line": 176}, "14": {"loc": {"start": {"line": 228, "column": 18}, "end": {"line": 228, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 228, "column": 18}, "end": {"line": 228, "column": 42}}, {"start": {"line": 228, "column": 46}, "end": {"line": 228, "column": 66}}], "line": 228}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0, 0], "14": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\annotation\\NoteEditor.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\annotation\\NoteEditor.tsx", "statementMap": {"0": {"start": {"line": 43, "column": 46}, "end": {"line": 403, "column": 1}}, "1": {"start": {"line": 51, "column": 19}, "end": {"line": 51, "column": 45}}, "2": {"start": {"line": 52, "column": 16}, "end": {"line": 52, "column": 47}}, "3": {"start": {"line": 53, "column": 26}, "end": {"line": 53, "column": 60}}, "4": {"start": {"line": 57, "column": 28}, "end": {"line": 57, "column": 40}}, "5": {"start": {"line": 58, "column": 32}, "end": {"line": 58, "column": 44}}, "6": {"start": {"line": 59, "column": 26}, "end": {"line": 59, "column": 38}}, "7": {"start": {"line": 60, "column": 34}, "end": {"line": 60, "column": 46}}, "8": {"start": {"line": 61, "column": 36}, "end": {"line": 61, "column": 51}}, "9": {"start": {"line": 62, "column": 34}, "end": {"line": 62, "column": 49}}, "10": {"start": {"line": 65, "column": 2}, "end": {"line": 80, "column": 30}}, "11": {"start": {"line": 66, "column": 4}, "end": {"line": 79, "column": 5}}, "12": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": 41}}, "13": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 45}}, "14": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": 51}}, "15": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 47}}, "16": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 52}}, "17": {"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": 19}}, "18": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 21}}, "19": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 18}}, "20": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 22}}, "21": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": 26}}, "22": {"start": {"line": 83, "column": 21}, "end": {"line": 155, "column": 4}}, "23": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 32}}, "24": {"start": {"line": 84, "column": 25}, "end": {"line": 84, "column": 32}}, "25": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": 22}}, "26": {"start": {"line": 88, "column": 4}, "end": {"line": 142, "column": 5}}, "27": {"start": {"line": 89, "column": 24}, "end": {"line": 89, "column": 59}}, "28": {"start": {"line": 90, "column": 6}, "end": {"line": 93, "column": 7}}, "29": {"start": {"line": 92, "column": 8}, "end": {"line": 92, "column": 15}}, "30": {"start": {"line": 95, "column": 23}, "end": {"line": 106, "column": 7}}, "31": {"start": {"line": 101, "column": 26}, "end": {"line": 101, "column": 36}}, "32": {"start": {"line": 108, "column": 6}, "end": {"line": 135, "column": 7}}, "33": {"start": {"line": 110, "column": 8}, "end": {"line": 115, "column": 10}}, "34": {"start": {"line": 118, "column": 31}, "end": {"line": 131, "column": 9}}, "35": {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 57}}, "36": {"start": {"line": 134, "column": 8}, "end": {"line": 134, "column": 43}}, "37": {"start": {"line": 137, "column": 6}, "end": {"line": 137, "column": 16}}, "38": {"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": 51}}, "39": {"start": {"line": 141, "column": 6}, "end": {"line": 141, "column": 25}}, "40": {"start": {"line": 158, "column": 23}, "end": {"line": 163, "column": 39}}, "41": {"start": {"line": 159, "column": 4}, "end": {"line": 161, "column": 5}}, "42": {"start": {"line": 160, "column": 6}, "end": {"line": 160, "column": 41}}, "43": {"start": {"line": 162, "column": 4}, "end": {"line": 162, "column": 14}}, "44": {"start": {"line": 166, "column": 26}, "end": {"line": 170, "column": 3}}, "45": {"start": {"line": 168, "column": 6}, "end": {"line": 168, "column": 68}}, "46": {"start": {"line": 169, "column": 4}, "end": {"line": 169, "column": 41}}, "47": {"start": {"line": 172, "column": 2}, "end": {"line": 402, "column": 4}}, "48": {"start": {"line": 356, "column": 29}, "end": {"line": 356, "column": 53}}, "49": {"start": {"line": 405, "column": 15}, "end": {"line": 505, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 43, "column": 46}, "end": {"line": 43, "column": 47}}, "loc": {"start": {"line": 50, "column": 6}, "end": {"line": 403, "column": 1}}, "line": 50}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 65, "column": 12}, "end": {"line": 65, "column": 13}}, "loc": {"start": {"line": 65, "column": 18}, "end": {"line": 80, "column": 3}}, "line": 65}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 83, "column": 33}, "end": {"line": 83, "column": 34}}, "loc": {"start": {"line": 83, "column": 45}, "end": {"line": 143, "column": 3}}, "line": 83}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 101, "column": 19}, "end": {"line": 101, "column": 20}}, "loc": {"start": {"line": 101, "column": 26}, "end": {"line": 101, "column": 36}}, "line": 101}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 158, "column": 35}, "end": {"line": 158, "column": 36}}, "loc": {"start": {"line": 158, "column": 41}, "end": {"line": 163, "column": 3}}, "line": 158}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 166, "column": 26}, "end": {"line": 166, "column": 27}}, "loc": {"start": {"line": 166, "column": 32}, "end": {"line": 170, "column": 3}}, "line": 166}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 356, "column": 23}, "end": {"line": 356, "column": 24}}, "loc": {"start": {"line": 356, "column": 29}, "end": {"line": 356, "column": 53}}, "line": 356}}, "branchMap": {"0": {"loc": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 47, "column": 11}, "end": {"line": 47, "column": 16}}], "line": 47}, "1": {"loc": {"start": {"line": 52, "column": 16}, "end": {"line": 52, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 52, "column": 25}, "end": {"line": 52, "column": 34}}, {"start": {"line": 52, "column": 37}, "end": {"line": 52, "column": 47}}], "line": 52}, "2": {"loc": {"start": {"line": 66, "column": 4}, "end": {"line": 79, "column": 5}}, "type": "if", "locations": [{"start": {"line": 66, "column": 4}, "end": {"line": 79, "column": 5}}, {"start": {"line": 72, "column": 11}, "end": {"line": 79, "column": 5}}], "line": 66}, "3": {"loc": {"start": {"line": 67, "column": 15}, "end": {"line": 67, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 15}, "end": {"line": 67, "column": 33}}, {"start": {"line": 67, "column": 37}, "end": {"line": 67, "column": 39}}], "line": 67}, "4": {"loc": {"start": {"line": 68, "column": 17}, "end": {"line": 68, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 68, "column": 17}, "end": {"line": 68, "column": 37}}, {"start": {"line": 68, "column": 41}, "end": {"line": 68, "column": 43}}], "line": 68}, "5": {"loc": {"start": {"line": 69, "column": 14}, "end": {"line": 69, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 69, "column": 14}, "end": {"line": 69, "column": 43}}, {"start": {"line": 69, "column": 47}, "end": {"line": 69, "column": 49}}], "line": 69}, "6": {"loc": {"start": {"line": 70, "column": 18}, "end": {"line": 70, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 70, "column": 18}, "end": {"line": 70, "column": 39}}, {"start": {"line": 70, "column": 43}, "end": {"line": 70, "column": 45}}], "line": 70}, "7": {"loc": {"start": {"line": 71, "column": 19}, "end": {"line": 71, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 71, "column": 19}, "end": {"line": 71, "column": 41}}, {"start": {"line": 71, "column": 45}, "end": {"line": 71, "column": 50}}], "line": 71}, "8": {"loc": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 32}}, "type": "if", "locations": [{"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 32}}, {"start": {}, "end": {}}], "line": 84}, "9": {"loc": {"start": {"line": 89, "column": 24}, "end": {"line": 89, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 89, "column": 24}, "end": {"line": 89, "column": 40}}, {"start": {"line": 89, "column": 44}, "end": {"line": 89, "column": 59}}], "line": 89}, "10": {"loc": {"start": {"line": 90, "column": 6}, "end": {"line": 93, "column": 7}}, "type": "if", "locations": [{"start": {"line": 90, "column": 6}, "end": {"line": 93, "column": 7}}, {"start": {}, "end": {}}], "line": 90}, "11": {"loc": {"start": {"line": 90, "column": 10}, "end": {"line": 90, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 90, "column": 10}, "end": {"line": 90, "column": 20}}, {"start": {"line": 90, "column": 24}, "end": {"line": 90, "column": 37}}], "line": 90}, "12": {"loc": {"start": {"line": 96, "column": 15}, "end": {"line": 96, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 96, "column": 15}, "end": {"line": 96, "column": 27}}, {"start": {"line": 96, "column": 31}, "end": {"line": 96, "column": 40}}], "line": 96}, "13": {"loc": {"start": {"line": 98, "column": 14}, "end": {"line": 103, "column": 21}}, "type": "cond-expr", "locations": [{"start": {"line": 99, "column": 12}, "end": {"line": 102, "column": 30}}, {"start": {"line": 103, "column": 12}, "end": {"line": 103, "column": 21}}], "line": 98}, "14": {"loc": {"start": {"line": 104, "column": 18}, "end": {"line": 104, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 104, "column": 18}, "end": {"line": 104, "column": 33}}, {"start": {"line": 104, "column": 37}, "end": {"line": 104, "column": 46}}], "line": 104}, "15": {"loc": {"start": {"line": 108, "column": 6}, "end": {"line": 135, "column": 7}}, "type": "if", "locations": [{"start": {"line": 108, "column": 6}, "end": {"line": 135, "column": 7}}, {"start": {"line": 116, "column": 13}, "end": {"line": 135, "column": 7}}], "line": 108}, "16": {"loc": {"start": {"line": 159, "column": 4}, "end": {"line": 161, "column": 5}}, "type": "if", "locations": [{"start": {"line": 159, "column": 4}, "end": {"line": 161, "column": 5}}, {"start": {}, "end": {}}], "line": 159}, "17": {"loc": {"start": {"line": 168, "column": 6}, "end": {"line": 168, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 168, "column": 6}, "end": {"line": 168, "column": 22}}, {"start": {"line": 168, "column": 26}, "end": {"line": 168, "column": 41}}, {"start": {"line": 168, "column": 45}, "end": {"line": 168, "column": 68}}], "line": 168}, "18": {"loc": {"start": {"line": 169, "column": 11}, "end": {"line": 169, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 169, "column": 11}, "end": {"line": 169, "column": 34}}, {"start": {"line": 169, "column": 38}, "end": {"line": 169, "column": 40}}], "line": 169}, "19": {"loc": {"start": {"line": 181, "column": 18}, "end": {"line": 181, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 181, "column": 42}, "end": {"line": 181, "column": 51}}, {"start": {"line": 181, "column": 54}, "end": {"line": 181, "column": 62}}], "line": 181}, "20": {"loc": {"start": {"line": 198, "column": 13}, "end": {"line": 198, "column": 52}}, "type": "cond-expr", "locations": [{"start": {"line": 198, "column": 28}, "end": {"line": 198, "column": 39}}, {"start": {"line": 198, "column": 42}, "end": {"line": 198, "column": 52}}], "line": 198}, "21": {"loc": {"start": {"line": 205, "column": 14}, "end": {"line": 205, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 205, "column": 15}, "end": {"line": 205, "column": 30}}, {"start": {"line": 205, "column": 34}, "end": {"line": 205, "column": 42}}, {"start": {"line": 205, "column": 47}, "end": {"line": 205, "column": 68}}], "line": 205}, "22": {"loc": {"start": {"line": 207, "column": 22}, "end": {"line": 207, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 207, "column": 22}, "end": {"line": 207, "column": 37}}, {"start": {"line": 207, "column": 41}, "end": {"line": 207, "column": 49}}], "line": 207}, "23": {"loc": {"start": {"line": 213, "column": 16}, "end": {"line": 215, "column": 17}}, "type": "binary-expr", "locations": [{"start": {"line": 213, "column": 17}, "end": {"line": 213, "column": 32}}, {"start": {"line": 213, "column": 36}, "end": {"line": 213, "column": 44}}, {"start": {"line": 213, "column": 49}, "end": {"line": 215, "column": 17}}], "line": 213}, "24": {"loc": {"start": {"line": 218, "column": 15}, "end": {"line": 218, "column": 46}}, "type": "cond-expr", "locations": [{"start": {"line": 218, "column": 26}, "end": {"line": 218, "column": 37}}, {"start": {"line": 218, "column": 40}, "end": {"line": 218, "column": 46}}], "line": 218}, "25": {"loc": {"start": {"line": 225, "column": 11}, "end": {"line": 246, "column": 11}}, "type": "binary-expr", "locations": [{"start": {"line": 225, "column": 11}, "end": {"line": 225, "column": 28}}, {"start": {"line": 226, "column": 12}, "end": {"line": 245, "column": 19}}], "line": 225}, "26": {"loc": {"start": {"line": 364, "column": 37}, "end": {"line": 366, "column": 37}}, "type": "cond-expr", "locations": [{"start": {"line": 365, "column": 24}, "end": {"line": 365, "column": 44}}, {"start": {"line": 366, "column": 24}, "end": {"line": 366, "column": 37}}], "line": 364}, "27": {"loc": {"start": {"line": 370, "column": 17}, "end": {"line": 379, "column": 17}}, "type": "binary-expr", "locations": [{"start": {"line": 370, "column": 17}, "end": {"line": 370, "column": 26}}, {"start": {"line": 371, "column": 18}, "end": {"line": 378, "column": 25}}], "line": 370}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0, 0], "22": [0, 0], "23": [0, 0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\annotation\\index.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\annotation\\index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\common\\Button.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\common\\Button.tsx", "statementMap": {"0": {"start": {"line": 24, "column": 38}, "end": {"line": 116, "column": 1}}, "1": {"start": {"line": 34, "column": 16}, "end": {"line": 34, "column": 47}}, "2": {"start": {"line": 36, "column": 25}, "end": {"line": 83, "column": 3}}, "3": {"start": {"line": 37, "column": 33}, "end": {"line": 41, "column": 5}}, "4": {"start": {"line": 44, "column": 54}, "end": {"line": 60, "column": 5}}, "5": {"start": {"line": 63, "column": 60}, "end": {"line": 76, "column": 5}}, "6": {"start": {"line": 78, "column": 4}, "end": {"line": 82, "column": 6}}, "7": {"start": {"line": 85, "column": 23}, "end": {"line": 104, "column": 3}}, "8": {"start": {"line": 86, "column": 33}, "end": {"line": 91, "column": 5}}, "9": {"start": {"line": 94, "column": 61}, "end": {"line": 98, "column": 5}}, "10": {"start": {"line": 100, "column": 4}, "end": {"line": 103, "column": 6}}, "11": {"start": {"line": 106, "column": 2}, "end": {"line": 115, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 24, "column": 38}, "end": {"line": 24, "column": 39}}, "loc": {"start": {"line": 33, "column": 6}, "end": {"line": 116, "column": 1}}, "line": 33}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 36, "column": 25}, "end": {"line": 36, "column": 26}}, "loc": {"start": {"line": 36, "column": 42}, "end": {"line": 83, "column": 3}}, "line": 36}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 85, "column": 23}, "end": {"line": 85, "column": 24}}, "loc": {"start": {"line": 85, "column": 40}, "end": {"line": 104, "column": 3}}, "line": 85}}, "branchMap": {"0": {"loc": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 27, "column": 12}, "end": {"line": 27, "column": 20}}], "line": 27}, "1": {"loc": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 17}}, "type": "default-arg", "locations": [{"start": {"line": 28, "column": 9}, "end": {"line": 28, "column": 17}}], "line": 28}, "2": {"loc": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 18}}, "type": "default-arg", "locations": [{"start": {"line": 29, "column": 13}, "end": {"line": 29, "column": 18}}], "line": 29}, "3": {"loc": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 30, "column": 11}, "end": {"line": 30, "column": 16}}], "line": 30}, "4": {"loc": {"start": {"line": 34, "column": 16}, "end": {"line": 34, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 34, "column": 25}, "end": {"line": 34, "column": 34}}, {"start": {"line": 34, "column": 37}, "end": {"line": 34, "column": 47}}], "line": 34}, "5": {"loc": {"start": {"line": 65, "column": 25}, "end": {"line": 65, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 65, "column": 36}, "end": {"line": 65, "column": 56}}, {"start": {"line": 65, "column": 59}, "end": {"line": 65, "column": 79}}], "line": 65}, "6": {"loc": {"start": {"line": 71, "column": 21}, "end": {"line": 71, "column": 75}}, "type": "cond-expr", "locations": [{"start": {"line": 71, "column": 32}, "end": {"line": 71, "column": 52}}, {"start": {"line": 71, "column": 55}, "end": {"line": 71, "column": 75}}], "line": 71}, "7": {"loc": {"start": {"line": 95, "column": 14}, "end": {"line": 95, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 95, "column": 25}, "end": {"line": 95, "column": 54}}, {"start": {"line": 95, "column": 57}, "end": {"line": 95, "column": 79}}], "line": 95}, "8": {"loc": {"start": {"line": 96, "column": 16}, "end": {"line": 96, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 96, "column": 27}, "end": {"line": 96, "column": 56}}, {"start": {"line": 96, "column": 59}, "end": {"line": 96, "column": 79}}], "line": 96}, "9": {"loc": {"start": {"line": 97, "column": 12}, "end": {"line": 97, "column": 75}}, "type": "cond-expr", "locations": [{"start": {"line": 97, "column": 23}, "end": {"line": 97, "column": 52}}, {"start": {"line": 97, "column": 55}, "end": {"line": 97, "column": 75}}], "line": 97}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\common\\Card.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\common\\Card.tsx", "statementMap": {"0": {"start": {"line": 19, "column": 34}, "end": {"line": 55, "column": 1}}, "1": {"start": {"line": 25, "column": 16}, "end": {"line": 25, "column": 47}}, "2": {"start": {"line": 27, "column": 23}, "end": {"line": 52, "column": 3}}, "3": {"start": {"line": 28, "column": 33}, "end": {"line": 31, "column": 5}}, "4": {"start": {"line": 33, "column": 58}, "end": {"line": 46, "column": 5}}, "5": {"start": {"line": 48, "column": 4}, "end": {"line": 51, "column": 6}}, "6": {"start": {"line": 54, "column": 2}, "end": {"line": 54, "column": 65}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 19, "column": 34}, "end": {"line": 19, "column": 35}}, "loc": {"start": {"line": 24, "column": 6}, "end": {"line": 55, "column": 1}}, "line": 24}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 27, "column": 23}, "end": {"line": 27, "column": 24}}, "loc": {"start": {"line": 27, "column": 40}, "end": {"line": 52, "column": 3}}, "line": 27}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 22}}, "type": "default-arg", "locations": [{"start": {"line": 21, "column": 12}, "end": {"line": 21, "column": 22}}], "line": 21}, "1": {"loc": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 22, "column": 11}, "end": {"line": 22, "column": 16}}], "line": 22}, "2": {"loc": {"start": {"line": 25, "column": 16}, "end": {"line": 25, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 25, "column": 25}, "end": {"line": 25, "column": 34}}, {"start": {"line": 25, "column": 37}, "end": {"line": 25, "column": 47}}], "line": 25}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0], "1": [0], "2": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\common\\ErrorBoundary.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\common\\ErrorBoundary.tsx", "statementMap": {"0": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 17}}, "1": {"start": {"line": 31, "column": 4}, "end": {"line": 35, "column": 6}}, "2": {"start": {"line": 40, "column": 4}, "end": {"line": 43, "column": 6}}, "3": {"start": {"line": 48, "column": 4}, "end": {"line": 52, "column": 7}}, "4": {"start": {"line": 55, "column": 21}, "end": {"line": 65, "column": 5}}, "5": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 39}}, "6": {"start": {"line": 70, "column": 4}, "end": {"line": 72, "column": 7}}, "7": {"start": {"line": 75, "column": 4}, "end": {"line": 77, "column": 5}}, "8": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 43}}, "9": {"start": {"line": 80, "column": 16}, "end": {"line": 88, "column": 3}}, "10": {"start": {"line": 81, "column": 4}, "end": {"line": 85, "column": 7}}, "11": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": 73}}, "12": {"start": {"line": 90, "column": 22}, "end": {"line": 102, "column": 3}}, "13": {"start": {"line": 91, "column": 4}, "end": {"line": 101, "column": 5}}, "14": {"start": {"line": 94, "column": 6}, "end": {"line": 97, "column": 9}}, "15": {"start": {"line": 105, "column": 4}, "end": {"line": 151, "column": 5}}, "16": {"start": {"line": 107, "column": 6}, "end": {"line": 109, "column": 7}}, "17": {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 35}}, "18": {"start": {"line": 112, "column": 6}, "end": {"line": 150, "column": 8}}, "19": {"start": {"line": 153, "column": 4}, "end": {"line": 153, "column": 31}}, "20": {"start": {"line": 157, "column": 15}, "end": {"line": 242, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 3}}, "loc": {"start": {"line": 29, "column": 28}, "end": {"line": 36, "column": 3}}, "line": 29}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 3}}, "loc": {"start": {"line": 38, "column": 64}, "end": {"line": 44, "column": 3}}, "line": 38}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 3}}, "loc": {"start": {"line": 46, "column": 62}, "end": {"line": 78, "column": 3}}, "line": 46}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 80, "column": 16}, "end": {"line": 80, "column": 17}}, "loc": {"start": {"line": 80, "column": 22}, "end": {"line": 88, "column": 3}}, "line": 80}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 90, "column": 22}, "end": {"line": 90, "column": 23}}, "loc": {"start": {"line": 90, "column": 28}, "end": {"line": 102, "column": 3}}, "line": 90}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 104, "column": 2}, "end": {"line": 104, "column": 3}}, "loc": {"start": {"line": 104, "column": 11}, "end": {"line": 154, "column": 3}}, "line": 104}}, "branchMap": {"0": {"loc": {"start": {"line": 75, "column": 4}, "end": {"line": 77, "column": 5}}, "type": "if", "locations": [{"start": {"line": 75, "column": 4}, "end": {"line": 77, "column": 5}}, {"start": {}, "end": {}}], "line": 75}, "1": {"loc": {"start": {"line": 91, "column": 4}, "end": {"line": 101, "column": 5}}, "type": "if", "locations": [{"start": {"line": 91, "column": 4}, "end": {"line": 101, "column": 5}}, {"start": {}, "end": {}}], "line": 91}, "2": {"loc": {"start": {"line": 105, "column": 4}, "end": {"line": 151, "column": 5}}, "type": "if", "locations": [{"start": {"line": 105, "column": 4}, "end": {"line": 151, "column": 5}}, {"start": {}, "end": {}}], "line": 105}, "3": {"loc": {"start": {"line": 107, "column": 6}, "end": {"line": 109, "column": 7}}, "type": "if", "locations": [{"start": {"line": 107, "column": 6}, "end": {"line": 109, "column": 7}}, {"start": {}, "end": {}}], "line": 107}, "4": {"loc": {"start": {"line": 121, "column": 13}, "end": {"line": 131, "column": 13}}, "type": "binary-expr", "locations": [{"start": {"line": 121, "column": 13}, "end": {"line": 121, "column": 20}}, {"start": {"line": 121, "column": 24}, "end": {"line": 121, "column": 40}}, {"start": {"line": 122, "column": 14}, "end": {"line": 130, "column": 21}}], "line": 121}, "5": {"loc": {"start": {"line": 125, "column": 17}, "end": {"line": 129, "column": 17}}, "type": "binary-expr", "locations": [{"start": {"line": 125, "column": 17}, "end": {"line": 125, "column": 39}}, {"start": {"line": 126, "column": 18}, "end": {"line": 128, "column": 25}}], "line": 125}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0, 0], "5": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\common\\index.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\common\\index.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\reading\\DocumentViewer.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\reading\\DocumentViewer.tsx", "statementMap": {"0": {"start": {"line": 24, "column": 33}, "end": {"line": 24, "column": 57}}, "1": {"start": {"line": 26, "column": 61}, "end": {"line": 260, "column": 1}}, "2": {"start": {"line": 34, "column": 40}, "end": {"line": 36, "column": 3}}, "3": {"start": {"line": 37, "column": 36}, "end": {"line": 37, "column": 50}}, "4": {"start": {"line": 38, "column": 28}, "end": {"line": 38, "column": 57}}, "5": {"start": {"line": 40, "column": 24}, "end": {"line": 40, "column": 48}}, "6": {"start": {"line": 41, "column": 25}, "end": {"line": 41, "column": 57}}, "7": {"start": {"line": 42, "column": 27}, "end": {"line": 42, "column": 51}}, "8": {"start": {"line": 44, "column": 23}, "end": {"line": 73, "column": 56}}, "9": {"start": {"line": 45, "column": 4}, "end": {"line": 72, "column": 5}}, "10": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 25}}, "11": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 21}}, "12": {"start": {"line": 49, "column": 20}, "end": {"line": 49, "column": 73}}, "13": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 28}}, "14": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 48}}, "15": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 50}}, "16": {"start": {"line": 57, "column": 6}, "end": {"line": 64, "column": 7}}, "17": {"start": {"line": 58, "column": 8}, "end": {"line": 63, "column": 16}}, "18": {"start": {"line": 59, "column": 10}, "end": {"line": 62, "column": 13}}, "19": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 70}}, "20": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 29}}, "21": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": 52}}, "22": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 26}}, "23": {"start": {"line": 76, "column": 2}, "end": {"line": 78, "column": 21}}, "24": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 19}}, "25": {"start": {"line": 81, "column": 2}, "end": {"line": 83, "column": 35}}, "26": {"start": {"line": 82, "column": 4}, "end": {"line": 82, "column": 33}}, "27": {"start": {"line": 86, "column": 2}, "end": {"line": 90, "column": 23}}, "28": {"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, "29": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 23}}, "30": {"start": {"line": 92, "column": 23}, "end": {"line": 156, "column": 3}}, "31": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 31}}, "32": {"start": {"line": 99, "column": 24}, "end": {"line": 99, "column": 31}}, "33": {"start": {"line": 101, "column": 45}, "end": {"line": 101, "column": 62}}, "34": {"start": {"line": 102, "column": 29}, "end": {"line": 102, "column": 44}}, "35": {"start": {"line": 104, "column": 8}, "end": {"line": 106, "column": 13}}, "36": {"start": {"line": 109, "column": 30}, "end": {"line": 111, "column": 7}}, "37": {"start": {"line": 114, "column": 43}, "end": {"line": 121, "column": 7}}, "38": {"start": {"line": 124, "column": 33}, "end": {"line": 124, "column": 77}}, "39": {"start": {"line": 125, "column": 26}, "end": {"line": 127, "column": 7}}, "40": {"start": {"line": 129, "column": 47}, "end": {"line": 136, "column": 7}}, "41": {"start": {"line": 139, "column": 6}, "end": {"line": 146, "column": 8}}, "42": {"start": {"line": 140, "column": 8}, "end": {"line": 145, "column": 16}}, "43": {"start": {"line": 149, "column": 6}, "end": {"line": 149, "column": 42}}, "44": {"start": {"line": 150, "column": 6}, "end": {"line": 150, "column": 62}}, "45": {"start": {"line": 153, "column": 6}, "end": {"line": 153, "column": 44}}, "46": {"start": {"line": 158, "column": 32}, "end": {"line": 226, "column": 3}}, "47": {"start": {"line": 159, "column": 4}, "end": {"line": 159, "column": 34}}, "48": {"start": {"line": 159, "column": 22}, "end": {"line": 159, "column": 34}}, "49": {"start": {"line": 161, "column": 34}, "end": {"line": 161, "column": 45}}, "50": {"start": {"line": 162, "column": 28}, "end": {"line": 169, "column": 5}}, "51": {"start": {"line": 171, "column": 25}, "end": {"line": 177, "column": 5}}, "52": {"start": {"line": 179, "column": 4}, "end": {"line": 225, "column": 5}}, "53": {"start": {"line": 182, "column": 8}, "end": {"line": 195, "column": 10}}, "54": {"start": {"line": 198, "column": 8}, "end": {"line": 206, "column": 10}}, "55": {"start": {"line": 209, "column": 8}, "end": {"line": 215, "column": 10}}, "56": {"start": {"line": 218, "column": 8}, "end": {"line": 224, "column": 10}}, "57": {"start": {"line": 228, "column": 2}, "end": {"line": 235, "column": 3}}, "58": {"start": {"line": 229, "column": 4}, "end": {"line": 234, "column": 6}}, "59": {"start": {"line": 237, "column": 2}, "end": {"line": 244, "column": 3}}, "60": {"start": {"line": 238, "column": 4}, "end": {"line": 243, "column": 6}}, "61": {"start": {"line": 246, "column": 2}, "end": {"line": 259, "column": 4}}, "62": {"start": {"line": 262, "column": 15}, "end": {"line": 316, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 26, "column": 61}, "end": {"line": 26, "column": 62}}, "loc": {"start": {"line": 33, "column": 6}, "end": {"line": 260, "column": 1}}, "line": 33}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 44, "column": 35}, "end": {"line": 44, "column": 36}}, "loc": {"start": {"line": 44, "column": 47}, "end": {"line": 73, "column": 3}}, "line": 44}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 58, "column": 19}, "end": {"line": 58, "column": 20}}, "loc": {"start": {"line": 58, "column": 25}, "end": {"line": 63, "column": 9}}, "line": 58}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 76, "column": 12}, "end": {"line": 76, "column": 13}}, "loc": {"start": {"line": 76, "column": 18}, "end": {"line": 78, "column": 3}}, "line": 76}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 81, "column": 12}, "end": {"line": 81, "column": 13}}, "loc": {"start": {"line": 81, "column": 18}, "end": {"line": 83, "column": 3}}, "line": 81}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 86, "column": 12}, "end": {"line": 86, "column": 13}}, "loc": {"start": {"line": 86, "column": 18}, "end": {"line": 90, "column": 3}}, "line": 86}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 5}}, "loc": {"start": {"line": 98, "column": 10}, "end": {"line": 154, "column": 5}}, "line": 98}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 139, "column": 21}, "end": {"line": 139, "column": 22}}, "loc": {"start": {"line": 140, "column": 8}, "end": {"line": 145, "column": 16}}, "line": 140}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 158, "column": 32}, "end": {"line": 158, "column": 33}}, "loc": {"start": {"line": 158, "column": 38}, "end": {"line": 226, "column": 3}}, "line": 158}}, "branchMap": {"0": {"loc": {"start": {"line": 57, "column": 6}, "end": {"line": 64, "column": 7}}, "type": "if", "locations": [{"start": {"line": 57, "column": 6}, "end": {"line": 64, "column": 7}}, {"start": {}, "end": {}}], "line": 57}, "1": {"loc": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 70}}, "type": "cond-expr", "locations": [{"start": {"line": 67, "column": 31}, "end": {"line": 67, "column": 42}}, {"start": {"line": 67, "column": 45}, "end": {"line": 67, "column": 70}}], "line": 67}, "2": {"loc": {"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, "type": "if", "locations": [{"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, {"start": {}, "end": {}}], "line": 87}, "3": {"loc": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 31}}, "type": "if", "locations": [{"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 31}}, {"start": {}, "end": {}}], "line": 99}, "4": {"loc": {"start": {"line": 104, "column": 8}, "end": {"line": 106, "column": 13}}, "type": "cond-expr", "locations": [{"start": {"line": 105, "column": 12}, "end": {"line": 105, "column": 72}}, {"start": {"line": 106, "column": 12}, "end": {"line": 106, "column": 13}}], "line": 104}, "5": {"loc": {"start": {"line": 140, "column": 8}, "end": {"line": 145, "column": 16}}, "type": "cond-expr", "locations": [{"start": {"line": 141, "column": 12}, "end": {"line": 144, "column": 13}}, {"start": {"line": 145, "column": 12}, "end": {"line": 145, "column": 16}}], "line": 140}, "6": {"loc": {"start": {"line": 159, "column": 4}, "end": {"line": 159, "column": 34}}, "type": "if", "locations": [{"start": {"line": 159, "column": 4}, "end": {"line": 159, "column": 34}}, {"start": {}, "end": {}}], "line": 159}, "7": {"loc": {"start": {"line": 179, "column": 4}, "end": {"line": 225, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 180, "column": 6}, "end": {"line": 180, "column": 31}}, {"start": {"line": 181, "column": 6}, "end": {"line": 195, "column": 10}}, {"start": {"line": 197, "column": 6}, "end": {"line": 206, "column": 10}}, {"start": {"line": 208, "column": 6}, "end": {"line": 215, "column": 10}}, {"start": {"line": 217, "column": 6}, "end": {"line": 224, "column": 10}}], "line": 179}, "8": {"loc": {"start": {"line": 184, "column": 13}, "end": {"line": 193, "column": 13}}, "type": "cond-expr", "locations": [{"start": {"line": 185, "column": 14}, "end": {"line": 188, "column": 21}}, {"start": {"line": 190, "column": 14}, "end": {"line": 192, "column": 21}}], "line": 184}, "9": {"loc": {"start": {"line": 202, "column": 15}, "end": {"line": 203, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 202, "column": 15}, "end": {"line": 202, "column": 27}}, {"start": {"line": 203, "column": 16}, "end": {"line": 203, "column": 72}}], "line": 202}, "10": {"loc": {"start": {"line": 228, "column": 2}, "end": {"line": 235, "column": 3}}, "type": "if", "locations": [{"start": {"line": 228, "column": 2}, "end": {"line": 235, "column": 3}}, {"start": {}, "end": {}}], "line": 228}, "11": {"loc": {"start": {"line": 237, "column": 2}, "end": {"line": 244, "column": 3}}, "type": "if", "locations": [{"start": {"line": 237, "column": 2}, "end": {"line": 244, "column": 3}}, {"start": {}, "end": {}}], "line": 237}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0, 0, 0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\reading\\NavigationControls.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\reading\\NavigationControls.tsx", "statementMap": {"0": {"start": {"line": 18, "column": 69}, "end": {"line": 163, "column": 1}}, "1": {"start": {"line": 25, "column": 42}, "end": {"line": 25, "column": 57}}, "2": {"start": {"line": 26, "column": 36}, "end": {"line": 26, "column": 48}}, "3": {"start": {"line": 28, "column": 29}, "end": {"line": 32, "column": 3}}, "4": {"start": {"line": 29, "column": 4}, "end": {"line": 31, "column": 5}}, "5": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 52}}, "6": {"start": {"line": 34, "column": 25}, "end": {"line": 38, "column": 3}}, "7": {"start": {"line": 35, "column": 4}, "end": {"line": 37, "column": 5}}, "8": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 52}}, "9": {"start": {"line": 40, "column": 25}, "end": {"line": 59, "column": 3}}, "10": {"start": {"line": 41, "column": 23}, "end": {"line": 41, "column": 46}}, "11": {"start": {"line": 43, "column": 4}, "end": {"line": 46, "column": 5}}, "12": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 70}}, "13": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 13}}, "14": {"start": {"line": 48, "column": 4}, "end": {"line": 54, "column": 5}}, "15": {"start": {"line": 49, "column": 6}, "end": {"line": 52, "column": 8}}, "16": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 13}}, "17": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 29}}, "18": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 27}}, "19": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 21}}, "20": {"start": {"line": 61, "column": 23}, "end": {"line": 64, "column": 3}}, "21": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 57}}, "22": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 26}}, "23": {"start": {"line": 66, "column": 2}, "end": {"line": 162, "column": 4}}, "24": {"start": {"line": 127, "column": 30}, "end": {"line": 127, "column": 52}}, "25": {"start": {"line": 146, "column": 31}, "end": {"line": 146, "column": 53}}, "26": {"start": {"line": 165, "column": 15}, "end": {"line": 284, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 18, "column": 69}, "end": {"line": 18, "column": 70}}, "loc": {"start": {"line": 24, "column": 6}, "end": {"line": 163, "column": 1}}, "line": 24}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 28, "column": 29}, "end": {"line": 28, "column": 30}}, "loc": {"start": {"line": 28, "column": 35}, "end": {"line": 32, "column": 3}}, "line": 28}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 34, "column": 25}, "end": {"line": 34, "column": 26}}, "loc": {"start": {"line": 34, "column": 31}, "end": {"line": 38, "column": 3}}, "line": 34}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 40, "column": 25}, "end": {"line": 40, "column": 26}}, "loc": {"start": {"line": 40, "column": 31}, "end": {"line": 59, "column": 3}}, "line": 40}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 61, "column": 23}, "end": {"line": 61, "column": 24}}, "loc": {"start": {"line": 61, "column": 29}, "end": {"line": 64, "column": 3}}, "line": 61}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 127, "column": 24}, "end": {"line": 127, "column": 25}}, "loc": {"start": {"line": 127, "column": 30}, "end": {"line": 127, "column": 52}}, "line": 127}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 146, "column": 25}, "end": {"line": 146, "column": 26}}, "loc": {"start": {"line": 146, "column": 31}, "end": {"line": 146, "column": 53}}, "line": 146}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 18}}, "type": "default-arg", "locations": [{"start": {"line": 23, "column": 13}, "end": {"line": 23, "column": 18}}], "line": 23}, "1": {"loc": {"start": {"line": 29, "column": 4}, "end": {"line": 31, "column": 5}}, "type": "if", "locations": [{"start": {"line": 29, "column": 4}, "end": {"line": 31, "column": 5}}, {"start": {}, "end": {}}], "line": 29}, "2": {"loc": {"start": {"line": 29, "column": 8}, "end": {"line": 29, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 29, "column": 8}, "end": {"line": 29, "column": 37}}, {"start": {"line": 29, "column": 41}, "end": {"line": 29, "column": 50}}], "line": 29}, "3": {"loc": {"start": {"line": 35, "column": 4}, "end": {"line": 37, "column": 5}}, "type": "if", "locations": [{"start": {"line": 35, "column": 4}, "end": {"line": 37, "column": 5}}, {"start": {}, "end": {}}], "line": 35}, "4": {"loc": {"start": {"line": 35, "column": 8}, "end": {"line": 35, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 35, "column": 8}, "end": {"line": 35, "column": 33}}, {"start": {"line": 35, "column": 37}, "end": {"line": 35, "column": 46}}], "line": 35}, "5": {"loc": {"start": {"line": 43, "column": 4}, "end": {"line": 46, "column": 5}}, "type": "if", "locations": [{"start": {"line": 43, "column": 4}, "end": {"line": 46, "column": 5}}, {"start": {}, "end": {}}], "line": 43}, "6": {"loc": {"start": {"line": 48, "column": 4}, "end": {"line": 54, "column": 5}}, "type": "if", "locations": [{"start": {"line": 48, "column": 4}, "end": {"line": 54, "column": 5}}, {"start": {}, "end": {}}], "line": 48}, "7": {"loc": {"start": {"line": 48, "column": 8}, "end": {"line": 48, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 48, "column": 8}, "end": {"line": 48, "column": 22}}, {"start": {"line": 48, "column": 26}, "end": {"line": 48, "column": 65}}], "line": 48}, "8": {"loc": {"start": {"line": 72, "column": 10}, "end": {"line": 72, "column": 79}}, "type": "binary-expr", "locations": [{"start": {"line": 72, "column": 11}, "end": {"line": 72, "column": 41}}, {"start": {"line": 72, "column": 45}, "end": {"line": 72, "column": 53}}, {"start": {"line": 72, "column": 58}, "end": {"line": 72, "column": 79}}], "line": 72}, "9": {"loc": {"start": {"line": 75, "column": 18}, "end": {"line": 75, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 75, "column": 18}, "end": {"line": 75, "column": 48}}, {"start": {"line": 75, "column": 52}, "end": {"line": 75, "column": 60}}], "line": 75}, "10": {"loc": {"start": {"line": 80, "column": 12}, "end": {"line": 80, "column": 79}}, "type": "binary-expr", "locations": [{"start": {"line": 80, "column": 13}, "end": {"line": 80, "column": 43}}, {"start": {"line": 80, "column": 47}, "end": {"line": 80, "column": 55}}, {"start": {"line": 80, "column": 60}, "end": {"line": 80, "column": 79}}], "line": 80}, "11": {"loc": {"start": {"line": 96, "column": 9}, "end": {"line": 100, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 96, "column": 9}, "end": {"line": 96, "column": 39}}, {"start": {"line": 97, "column": 10}, "end": {"line": 99, "column": 17}}], "line": 96}, "12": {"loc": {"start": {"line": 107, "column": 10}, "end": {"line": 107, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 107, "column": 11}, "end": {"line": 107, "column": 37}}, {"start": {"line": 107, "column": 41}, "end": {"line": 107, "column": 49}}, {"start": {"line": 107, "column": 54}, "end": {"line": 107, "column": 75}}], "line": 107}, "13": {"loc": {"start": {"line": 110, "column": 18}, "end": {"line": 110, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 110, "column": 18}, "end": {"line": 110, "column": 44}}, {"start": {"line": 110, "column": 48}, "end": {"line": 110, "column": 56}}], "line": 110}, "14": {"loc": {"start": {"line": 115, "column": 12}, "end": {"line": 115, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 115, "column": 13}, "end": {"line": 115, "column": 39}}, {"start": {"line": 115, "column": 43}, "end": {"line": 115, "column": 51}}, {"start": {"line": 115, "column": 56}, "end": {"line": 115, "column": 75}}], "line": 115}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0, 0], "9": [0, 0], "10": [0, 0, 0], "11": [0, 0], "12": [0, 0, 0], "13": [0, 0], "14": [0, 0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\reading\\ProgressTracker.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\reading\\ProgressTracker.tsx", "statementMap": {"0": {"start": {"line": 10, "column": 63}, "end": {"line": 151, "column": 1}}, "1": {"start": {"line": 16, "column": 28}, "end": {"line": 27, "column": 3}}, "2": {"start": {"line": 17, "column": 4}, "end": {"line": 26, "column": 5}}, "3": {"start": {"line": 18, "column": 6}, "end": {"line": 18, "column": 27}}, "4": {"start": {"line": 19, "column": 11}, "end": {"line": 26, "column": 5}}, "5": {"start": {"line": 20, "column": 22}, "end": {"line": 20, "column": 46}}, "6": {"start": {"line": 21, "column": 6}, "end": {"line": 21, "column": 27}}, "7": {"start": {"line": 23, "column": 20}, "end": {"line": 23, "column": 46}}, "8": {"start": {"line": 24, "column": 22}, "end": {"line": 24, "column": 55}}, "9": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 37}}, "10": {"start": {"line": 29, "column": 21}, "end": {"line": 47, "column": 3}}, "11": {"start": {"line": 30, "column": 16}, "end": {"line": 30, "column": 26}}, "12": {"start": {"line": 31, "column": 19}, "end": {"line": 31, "column": 49}}, "13": {"start": {"line": 32, "column": 21}, "end": {"line": 32, "column": 53}}, "14": {"start": {"line": 33, "column": 22}, "end": {"line": 33, "column": 59}}, "15": {"start": {"line": 34, "column": 21}, "end": {"line": 34, "column": 63}}, "16": {"start": {"line": 36, "column": 4}, "end": {"line": 46, "column": 5}}, "17": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 24}}, "18": {"start": {"line": 38, "column": 11}, "end": {"line": 46, "column": 5}}, "19": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 32}}, "20": {"start": {"line": 40, "column": 11}, "end": {"line": 46, "column": 5}}, "21": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 33}}, "22": {"start": {"line": 42, "column": 11}, "end": {"line": 46, "column": 5}}, "23": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 32}}, "24": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 39}}, "25": {"start": {"line": 49, "column": 33}, "end": {"line": 72, "column": 3}}, "26": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 34}}, "27": {"start": {"line": 52, "column": 27}, "end": {"line": 52, "column": 34}}, "28": {"start": {"line": 54, "column": 26}, "end": {"line": 54, "column": 43}}, "29": {"start": {"line": 55, "column": 29}, "end": {"line": 55, "column": 32}}, "30": {"start": {"line": 56, "column": 23}, "end": {"line": 59, "column": 5}}, "31": {"start": {"line": 61, "column": 31}, "end": {"line": 63, "column": 5}}, "32": {"start": {"line": 65, "column": 24}, "end": {"line": 69, "column": 5}}, "33": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 34}}, "34": {"start": {"line": 74, "column": 2}, "end": {"line": 90, "column": 3}}, "35": {"start": {"line": 75, "column": 4}, "end": {"line": 89, "column": 6}}, "36": {"start": {"line": 92, "column": 2}, "end": {"line": 150, "column": 4}}, "37": {"start": {"line": 153, "column": 15}, "end": {"line": 236, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 10, "column": 63}, "end": {"line": 10, "column": 64}}, "loc": {"start": {"line": 15, "column": 6}, "end": {"line": 151, "column": 1}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 28}, "end": {"line": 16, "column": 29}}, "loc": {"start": {"line": 16, "column": 57}, "end": {"line": 27, "column": 3}}, "line": 16}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 29, "column": 21}, "end": {"line": 29, "column": 22}}, "loc": {"start": {"line": 29, "column": 45}, "end": {"line": 47, "column": 3}}, "line": 29}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 49, "column": 33}, "end": {"line": 49, "column": 34}}, "loc": {"start": {"line": 51, "column": 8}, "end": {"line": 72, "column": 3}}, "line": 51}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 13, "column": 16}, "end": {"line": 13, "column": 20}}], "line": 13}, "1": {"loc": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 17}}, "type": "default-arg", "locations": [{"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 17}}], "line": 14}, "2": {"loc": {"start": {"line": 17, "column": 4}, "end": {"line": 26, "column": 5}}, "type": "if", "locations": [{"start": {"line": 17, "column": 4}, "end": {"line": 26, "column": 5}}, {"start": {"line": 19, "column": 11}, "end": {"line": 26, "column": 5}}], "line": 17}, "3": {"loc": {"start": {"line": 19, "column": 11}, "end": {"line": 26, "column": 5}}, "type": "if", "locations": [{"start": {"line": 19, "column": 11}, "end": {"line": 26, "column": 5}}, {"start": {"line": 22, "column": 11}, "end": {"line": 26, "column": 5}}], "line": 19}, "4": {"loc": {"start": {"line": 36, "column": 4}, "end": {"line": 46, "column": 5}}, "type": "if", "locations": [{"start": {"line": 36, "column": 4}, "end": {"line": 46, "column": 5}}, {"start": {"line": 38, "column": 11}, "end": {"line": 46, "column": 5}}], "line": 36}, "5": {"loc": {"start": {"line": 38, "column": 11}, "end": {"line": 46, "column": 5}}, "type": "if", "locations": [{"start": {"line": 38, "column": 11}, "end": {"line": 46, "column": 5}}, {"start": {"line": 40, "column": 11}, "end": {"line": 46, "column": 5}}], "line": 38}, "6": {"loc": {"start": {"line": 40, "column": 11}, "end": {"line": 46, "column": 5}}, "type": "if", "locations": [{"start": {"line": 40, "column": 11}, "end": {"line": 46, "column": 5}}, {"start": {"line": 42, "column": 11}, "end": {"line": 46, "column": 5}}], "line": 40}, "7": {"loc": {"start": {"line": 42, "column": 11}, "end": {"line": 46, "column": 5}}, "type": "if", "locations": [{"start": {"line": 42, "column": 11}, "end": {"line": 46, "column": 5}}, {"start": {"line": 44, "column": 11}, "end": {"line": 46, "column": 5}}], "line": 42}, "8": {"loc": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 34}}, "type": "if", "locations": [{"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 34}}, {"start": {}, "end": {}}], "line": 52}, "9": {"loc": {"start": {"line": 74, "column": 2}, "end": {"line": 90, "column": 3}}, "type": "if", "locations": [{"start": {"line": 74, "column": 2}, "end": {"line": 90, "column": 3}}, {"start": {}, "end": {}}], "line": 74}, "10": {"loc": {"start": {"line": 114, "column": 7}, "end": {"line": 148, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 114, "column": 7}, "end": {"line": 114, "column": 18}}, {"start": {"line": 115, "column": 8}, "end": {"line": 147, "column": 15}}], "line": 114}, "11": {"loc": {"start": {"line": 138, "column": 11}, "end": {"line": 146, "column": 11}}, "type": "binary-expr", "locations": [{"start": {"line": 138, "column": 11}, "end": {"line": 138, "column": 30}}, {"start": {"line": 139, "column": 12}, "end": {"line": 145, "column": 19}}], "line": 138}, "12": {"loc": {"start": {"line": 142, "column": 17}, "end": {"line": 142, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 142, "column": 17}, "end": {"line": 142, "column": 52}}, {"start": {"line": 142, "column": 56}, "end": {"line": 142, "column": 57}}], "line": 142}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\reading\\TextSelection.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\reading\\TextSelection.tsx", "statementMap": {"0": {"start": {"line": 19, "column": 25}, "end": {"line": 26, "column": 1}}, "1": {"start": {"line": 28, "column": 68}, "end": {"line": 250, "column": 1}}, "2": {"start": {"line": 36, "column": 40}, "end": {"line": 36, "column": 55}}, "3": {"start": {"line": 37, "column": 44}, "end": {"line": 37, "column": 59}}, "4": {"start": {"line": 38, "column": 34}, "end": {"line": 38, "column": 46}}, "5": {"start": {"line": 39, "column": 48}, "end": {"line": 39, "column": 63}}, "6": {"start": {"line": 41, "column": 21}, "end": {"line": 52, "column": 3}}, "7": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 39}}, "8": {"start": {"line": 42, "column": 32}, "end": {"line": 42, "column": 39}}, "9": {"start": {"line": 44, "column": 4}, "end": {"line": 51, "column": 5}}, "10": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 56}}, "11": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 39}}, "12": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 56}}, "13": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 28}}, "14": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 50}}, "15": {"start": {"line": 54, "column": 26}, "end": {"line": 60, "column": 3}}, "16": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 39}}, "17": {"start": {"line": 55, "column": 32}, "end": {"line": 55, "column": 39}}, "18": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 36}}, "19": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 30}}, "20": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 26}}, "21": {"start": {"line": 62, "column": 24}, "end": {"line": 73, "column": 3}}, "22": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 39}}, "23": {"start": {"line": 63, "column": 32}, "end": {"line": 63, "column": 39}}, "24": {"start": {"line": 65, "column": 4}, "end": {"line": 72, "column": 5}}, "25": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 43}}, "26": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": 22}}, "27": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 30}}, "28": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": 28}}, "29": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 50}}, "30": {"start": {"line": 75, "column": 31}, "end": {"line": 78, "column": 3}}, "31": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 33}}, "32": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 26}}, "33": {"start": {"line": 80, "column": 22}, "end": {"line": 84, "column": 3}}, "34": {"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, "35": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 27}}, "36": {"start": {"line": 86, "column": 24}, "end": {"line": 89, "column": 3}}, "37": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": 26}}, "38": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 27}}, "39": {"start": {"line": 91, "column": 26}, "end": {"line": 94, "column": 3}}, "40": {"start": {"line": 92, "column": 4}, "end": {"line": 92, "column": 26}}, "41": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 29}}, "42": {"start": {"line": 96, "column": 2}, "end": {"line": 98, "column": 3}}, "43": {"start": {"line": 97, "column": 4}, "end": {"line": 97, "column": 16}}, "44": {"start": {"line": 100, "column": 2}, "end": {"line": 249, "column": 4}}, "45": {"start": {"line": 119, "column": 30}, "end": {"line": 119, "column": 51}}, "46": {"start": {"line": 169, "column": 30}, "end": {"line": 169, "column": 55}}, "47": {"start": {"line": 177, "column": 16}, "end": {"line": 186, "column": 35}}, "48": {"start": {"line": 183, "column": 33}, "end": {"line": 183, "column": 67}}, "49": {"start": {"line": 192, "column": 29}, "end": {"line": 192, "column": 54}}, "50": {"start": {"line": 205, "column": 30}, "end": {"line": 205, "column": 53}}, "51": {"start": {"line": 229, "column": 31}, "end": {"line": 229, "column": 54}}, "52": {"start": {"line": 252, "column": 15}, "end": {"line": 435, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 28, "column": 68}, "end": {"line": 28, "column": 69}}, "loc": {"start": {"line": 35, "column": 6}, "end": {"line": 250, "column": 1}}, "line": 35}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 41, "column": 21}, "end": {"line": 41, "column": 22}}, "loc": {"start": {"line": 41, "column": 33}, "end": {"line": 52, "column": 3}}, "line": 41}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 54, "column": 26}, "end": {"line": 54, "column": 27}}, "loc": {"start": {"line": 54, "column": 45}, "end": {"line": 60, "column": 3}}, "line": 54}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 62, "column": 24}, "end": {"line": 62, "column": 25}}, "loc": {"start": {"line": 62, "column": 30}, "end": {"line": 73, "column": 3}}, "line": 62}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 75, "column": 31}, "end": {"line": 75, "column": 32}}, "loc": {"start": {"line": 75, "column": 37}, "end": {"line": 78, "column": 3}}, "line": 75}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 80, "column": 22}, "end": {"line": 80, "column": 23}}, "loc": {"start": {"line": 80, "column": 28}, "end": {"line": 84, "column": 3}}, "line": 80}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 86, "column": 24}, "end": {"line": 86, "column": 25}}, "loc": {"start": {"line": 86, "column": 30}, "end": {"line": 89, "column": 3}}, "line": 86}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 91, "column": 26}, "end": {"line": 91, "column": 27}}, "loc": {"start": {"line": 91, "column": 32}, "end": {"line": 94, "column": 3}}, "line": 91}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 119, "column": 24}, "end": {"line": 119, "column": 25}}, "loc": {"start": {"line": 119, "column": 30}, "end": {"line": 119, "column": 51}}, "line": 119}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 169, "column": 24}, "end": {"line": 169, "column": 25}}, "loc": {"start": {"line": 169, "column": 30}, "end": {"line": 169, "column": 55}}, "line": 169}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 176, "column": 36}, "end": {"line": 176, "column": 37}}, "loc": {"start": {"line": 177, "column": 16}, "end": {"line": 186, "column": 35}}, "line": 177}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 183, "column": 27}, "end": {"line": 183, "column": 28}}, "loc": {"start": {"line": 183, "column": 33}, "end": {"line": 183, "column": 67}}, "line": 183}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 192, "column": 23}, "end": {"line": 192, "column": 24}}, "loc": {"start": {"line": 192, "column": 29}, "end": {"line": 192, "column": 54}}, "line": 192}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 205, "column": 24}, "end": {"line": 205, "column": 25}}, "loc": {"start": {"line": 205, "column": 30}, "end": {"line": 205, "column": 53}}, "line": 205}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 229, "column": 25}, "end": {"line": 229, "column": 26}}, "loc": {"start": {"line": 229, "column": 31}, "end": {"line": 229, "column": 54}}, "line": 229}}, "branchMap": {"0": {"loc": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 18}}, "type": "default-arg", "locations": [{"start": {"line": 34, "column": 13}, "end": {"line": 34, "column": 18}}], "line": 34}, "1": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 39}}, "type": "if", "locations": [{"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 39}}, {"start": {}, "end": {}}], "line": 42}, "2": {"loc": {"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 18}}, {"start": {"line": 42, "column": 22}, "end": {"line": 42, "column": 30}}], "line": 42}, "3": {"loc": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 39}}, "type": "if", "locations": [{"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 39}}, {"start": {}, "end": {}}], "line": 55}, "4": {"loc": {"start": {"line": 55, "column": 8}, "end": {"line": 55, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 55, "column": 8}, "end": {"line": 55, "column": 18}}, {"start": {"line": 55, "column": 22}, "end": {"line": 55, "column": 30}}], "line": 55}, "5": {"loc": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 39}}, "type": "if", "locations": [{"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 39}}, {"start": {}, "end": {}}], "line": 63}, "6": {"loc": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 18}}, {"start": {"line": 63, "column": 22}, "end": {"line": 63, "column": 30}}], "line": 63}, "7": {"loc": {"start": {"line": 65, "column": 4}, "end": {"line": 72, "column": 5}}, "type": "if", "locations": [{"start": {"line": 65, "column": 4}, "end": {"line": 72, "column": 5}}, {"start": {"line": 70, "column": 11}, "end": {"line": 72, "column": 5}}], "line": 65}, "8": {"loc": {"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, "type": "if", "locations": [{"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, {"start": {}, "end": {}}], "line": 81}, "9": {"loc": {"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": 17}}, {"start": {"line": 81, "column": 21}, "end": {"line": 81, "column": 30}}], "line": 81}, "10": {"loc": {"start": {"line": 96, "column": 2}, "end": {"line": 98, "column": 3}}, "type": "if", "locations": [{"start": {"line": 96, "column": 2}, "end": {"line": 98, "column": 3}}, {"start": {}, "end": {}}], "line": 96}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\reading\\ZoomControls.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\components\\reading\\ZoomControls.tsx", "statementMap": {"0": {"start": {"line": 10, "column": 57}, "end": {"line": 188, "column": 1}}, "1": {"start": {"line": 19, "column": 23}, "end": {"line": 28, "column": 3}}, "2": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 25}}, "3": {"start": {"line": 20, "column": 18}, "end": {"line": 20, "column": 25}}, "4": {"start": {"line": 22, "column": 21}, "end": {"line": 22, "column": 58}}, "5": {"start": {"line": 23, "column": 4}, "end": {"line": 27, "column": 7}}, "6": {"start": {"line": 30, "column": 24}, "end": {"line": 39, "column": 3}}, "7": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 25}}, "8": {"start": {"line": 31, "column": 18}, "end": {"line": 31, "column": 25}}, "9": {"start": {"line": 33, "column": 21}, "end": {"line": 33, "column": 58}}, "10": {"start": {"line": 34, "column": 4}, "end": {"line": 38, "column": 7}}, "11": {"start": {"line": 41, "column": 26}, "end": {"line": 43, "column": 3}}, "12": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 41}}, "13": {"start": {"line": 45, "column": 26}, "end": {"line": 56, "column": 3}}, "14": {"start": {"line": 46, "column": 4}, "end": {"line": 55, "column": 5}}, "15": {"start": {"line": 48, "column": 8}, "end": {"line": 48, "column": 27}}, "16": {"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 28}}, "17": {"start": {"line": 52, "column": 8}, "end": {"line": 52, "column": 26}}, "18": {"start": {"line": 54, "column": 8}, "end": {"line": 54, "column": 24}}, "19": {"start": {"line": 58, "column": 2}, "end": {"line": 187, "column": 4}}, "20": {"start": {"line": 190, "column": 15}, "end": {"line": 268, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 10, "column": 57}, "end": {"line": 10, "column": 58}}, "loc": {"start": {"line": 18, "column": 6}, "end": {"line": 188, "column": 1}}, "line": 18}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 19, "column": 23}, "end": {"line": 19, "column": 24}}, "loc": {"start": {"line": 19, "column": 29}, "end": {"line": 28, "column": 3}}, "line": 19}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 30, "column": 24}, "end": {"line": 30, "column": 25}}, "loc": {"start": {"line": 30, "column": 30}, "end": {"line": 39, "column": 3}}, "line": 30}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 41, "column": 26}, "end": {"line": 41, "column": 27}}, "loc": {"start": {"line": 41, "column": 53}, "end": {"line": 43, "column": 3}}, "line": 41}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 45, "column": 26}, "end": {"line": 45, "column": 27}}, "loc": {"start": {"line": 45, "column": 55}, "end": {"line": 56, "column": 3}}, "line": 45}}, "branchMap": {"0": {"loc": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 18}}, "type": "default-arg", "locations": [{"start": {"line": 17, "column": 13}, "end": {"line": 17, "column": 18}}], "line": 17}, "1": {"loc": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 25}}, "type": "if", "locations": [{"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 25}}, {"start": {}, "end": {}}], "line": 20}, "2": {"loc": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 25}}, "type": "if", "locations": [{"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 25}}, {"start": {}, "end": {}}], "line": 31}, "3": {"loc": {"start": {"line": 46, "column": 4}, "end": {"line": 55, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 47, "column": 6}, "end": {"line": 48, "column": 27}}, {"start": {"line": 49, "column": 6}, "end": {"line": 50, "column": 28}}, {"start": {"line": 51, "column": 6}, "end": {"line": 52, "column": 26}}, {"start": {"line": 53, "column": 6}, "end": {"line": 54, "column": 24}}], "line": 46}, "4": {"loc": {"start": {"line": 64, "column": 10}, "end": {"line": 64, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 64, "column": 11}, "end": {"line": 64, "column": 19}}, {"start": {"line": 64, "column": 23}, "end": {"line": 64, "column": 45}}, {"start": {"line": 64, "column": 50}, "end": {"line": 64, "column": 71}}], "line": 64}, "5": {"loc": {"start": {"line": 67, "column": 18}, "end": {"line": 67, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 18}, "end": {"line": 67, "column": 26}}, {"start": {"line": 67, "column": 30}, "end": {"line": 67, "column": 52}}], "line": 67}, "6": {"loc": {"start": {"line": 72, "column": 12}, "end": {"line": 72, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 72, "column": 13}, "end": {"line": 72, "column": 21}}, {"start": {"line": 72, "column": 25}, "end": {"line": 72, "column": 47}}, {"start": {"line": 72, "column": 52}, "end": {"line": 72, "column": 71}}], "line": 72}, "7": {"loc": {"start": {"line": 89, "column": 10}, "end": {"line": 89, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 89, "column": 11}, "end": {"line": 89, "column": 19}}, {"start": {"line": 89, "column": 23}, "end": {"line": 89, "column": 45}}, {"start": {"line": 89, "column": 50}, "end": {"line": 89, "column": 71}}], "line": 89}, "8": {"loc": {"start": {"line": 92, "column": 18}, "end": {"line": 92, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 92, "column": 18}, "end": {"line": 92, "column": 26}}, {"start": {"line": 92, "column": 30}, "end": {"line": 92, "column": 52}}], "line": 92}, "9": {"loc": {"start": {"line": 97, "column": 12}, "end": {"line": 97, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 97, "column": 13}, "end": {"line": 97, "column": 21}}, {"start": {"line": 97, "column": 25}, "end": {"line": 97, "column": 47}}, {"start": {"line": 97, "column": 52}, "end": {"line": 97, "column": 71}}], "line": 97}, "10": {"loc": {"start": {"line": 109, "column": 12}, "end": {"line": 109, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 109, "column": 12}, "end": {"line": 109, "column": 41}}, {"start": {"line": 109, "column": 45}, "end": {"line": 109, "column": 67}}], "line": 109}, "11": {"loc": {"start": {"line": 110, "column": 12}, "end": {"line": 110, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 110, "column": 12}, "end": {"line": 110, "column": 20}}, {"start": {"line": 110, "column": 24}, "end": {"line": 110, "column": 45}}], "line": 110}, "12": {"loc": {"start": {"line": 118, "column": 14}, "end": {"line": 118, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 118, "column": 14}, "end": {"line": 118, "column": 43}}, {"start": {"line": 118, "column": 47}, "end": {"line": 118, "column": 73}}], "line": 118}, "13": {"loc": {"start": {"line": 119, "column": 14}, "end": {"line": 119, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 119, "column": 14}, "end": {"line": 119, "column": 22}}, {"start": {"line": 119, "column": 26}, "end": {"line": 119, "column": 45}}], "line": 119}, "14": {"loc": {"start": {"line": 129, "column": 12}, "end": {"line": 129, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 129, "column": 12}, "end": {"line": 129, "column": 42}}, {"start": {"line": 129, "column": 46}, "end": {"line": 129, "column": 68}}], "line": 129}, "15": {"loc": {"start": {"line": 130, "column": 12}, "end": {"line": 130, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 130, "column": 12}, "end": {"line": 130, "column": 20}}, {"start": {"line": 130, "column": 24}, "end": {"line": 130, "column": 45}}], "line": 130}, "16": {"loc": {"start": {"line": 138, "column": 14}, "end": {"line": 138, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 138, "column": 14}, "end": {"line": 138, "column": 44}}, {"start": {"line": 138, "column": 48}, "end": {"line": 138, "column": 74}}], "line": 138}, "17": {"loc": {"start": {"line": 139, "column": 14}, "end": {"line": 139, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 139, "column": 14}, "end": {"line": 139, "column": 22}}, {"start": {"line": 139, "column": 26}, "end": {"line": 139, "column": 45}}], "line": 139}, "18": {"loc": {"start": {"line": 149, "column": 12}, "end": {"line": 149, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 149, "column": 12}, "end": {"line": 149, "column": 40}}, {"start": {"line": 149, "column": 44}, "end": {"line": 149, "column": 66}}], "line": 149}, "19": {"loc": {"start": {"line": 150, "column": 12}, "end": {"line": 150, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 150, "column": 12}, "end": {"line": 150, "column": 20}}, {"start": {"line": 150, "column": 24}, "end": {"line": 150, "column": 45}}], "line": 150}, "20": {"loc": {"start": {"line": 158, "column": 14}, "end": {"line": 158, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 158, "column": 14}, "end": {"line": 158, "column": 42}}, {"start": {"line": 158, "column": 46}, "end": {"line": 158, "column": 72}}], "line": 158}, "21": {"loc": {"start": {"line": 159, "column": 14}, "end": {"line": 159, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 159, "column": 14}, "end": {"line": 159, "column": 22}}, {"start": {"line": 159, "column": 26}, "end": {"line": 159, "column": 45}}], "line": 159}, "22": {"loc": {"start": {"line": 170, "column": 12}, "end": {"line": 170, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 170, "column": 12}, "end": {"line": 170, "column": 20}}, {"start": {"line": 170, "column": 24}, "end": {"line": 170, "column": 45}}], "line": 170}, "23": {"loc": {"start": {"line": 179, "column": 14}, "end": {"line": 179, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 179, "column": 14}, "end": {"line": 179, "column": 22}}, {"start": {"line": 179, "column": 26}, "end": {"line": 179, "column": 45}}], "line": 179}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0, 0, 0], "4": [0, 0, 0], "5": [0, 0], "6": [0, 0, 0], "7": [0, 0, 0], "8": [0, 0], "9": [0, 0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\navigation\\AppNavigator.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\navigation\\AppNavigator.tsx", "statementMap": {"0": {"start": {"line": 28, "column": 20}, "end": {"line": 28, "column": 31}}, "1": {"start": {"line": 28, "column": 26}, "end": {"line": 28, "column": 31}}, "2": {"start": {"line": 29, "column": 18}, "end": {"line": 29, "column": 29}}, "3": {"start": {"line": 29, "column": 24}, "end": {"line": 29, "column": 29}}, "4": {"start": {"line": 30, "column": 19}, "end": {"line": 30, "column": 30}}, "5": {"start": {"line": 30, "column": 25}, "end": {"line": 30, "column": 30}}, "6": {"start": {"line": 31, "column": 21}, "end": {"line": 31, "column": 32}}, "7": {"start": {"line": 31, "column": 27}, "end": {"line": 31, "column": 32}}, "8": {"start": {"line": 34, "column": 18}, "end": {"line": 34, "column": 60}}, "9": {"start": {"line": 35, "column": 16}, "end": {"line": 35, "column": 60}}, "10": {"start": {"line": 36, "column": 21}, "end": {"line": 36, "column": 66}}, "11": {"start": {"line": 37, "column": 19}, "end": {"line": 37, "column": 62}}, "12": {"start": {"line": 40, "column": 30}, "end": {"line": 60, "column": 1}}, "13": {"start": {"line": 41, "column": 2}, "end": {"line": 59, "column": 4}}, "14": {"start": {"line": 63, "column": 28}, "end": {"line": 83, "column": 1}}, "15": {"start": {"line": 64, "column": 2}, "end": {"line": 82, "column": 4}}, "16": {"start": {"line": 86, "column": 25}, "end": {"line": 134, "column": 1}}, "17": {"start": {"line": 87, "column": 2}, "end": {"line": 133, "column": 4}}, "18": {"start": {"line": 137, "column": 27}, "end": {"line": 172, "column": 1}}, "19": {"start": {"line": 138, "column": 2}, "end": {"line": 171, "column": 4}}, "20": {"start": {"line": 145, "column": 10}, "end": {"line": 156, "column": 12}}, "21": {"start": {"line": 175, "column": 16}, "end": {"line": 183, "column": 1}}, "22": {"start": {"line": 186, "column": 31}, "end": {"line": 192, "column": 1}}, "23": {"start": {"line": 187, "column": 2}, "end": {"line": 191, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 28, "column": 20}, "end": {"line": 28, "column": 21}}, "loc": {"start": {"line": 28, "column": 26}, "end": {"line": 28, "column": 31}}, "line": 28}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 29, "column": 18}, "end": {"line": 29, "column": 19}}, "loc": {"start": {"line": 29, "column": 24}, "end": {"line": 29, "column": 29}}, "line": 29}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 30, "column": 19}, "end": {"line": 30, "column": 20}}, "loc": {"start": {"line": 30, "column": 25}, "end": {"line": 30, "column": 30}}, "line": 30}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 31, "column": 21}, "end": {"line": 31, "column": 22}}, "loc": {"start": {"line": 31, "column": 27}, "end": {"line": 31, "column": 32}}, "line": 31}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 40, "column": 30}, "end": {"line": 40, "column": 31}}, "loc": {"start": {"line": 40, "column": 36}, "end": {"line": 60, "column": 1}}, "line": 40}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 63, "column": 28}, "end": {"line": 63, "column": 29}}, "loc": {"start": {"line": 63, "column": 34}, "end": {"line": 83, "column": 1}}, "line": 63}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 86, "column": 25}, "end": {"line": 86, "column": 26}}, "loc": {"start": {"line": 86, "column": 31}, "end": {"line": 134, "column": 1}}, "line": 86}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 137, "column": 27}, "end": {"line": 137, "column": 28}}, "loc": {"start": {"line": 137, "column": 33}, "end": {"line": 172, "column": 1}}, "line": 137}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 144, "column": 31}, "end": {"line": 144, "column": 32}}, "loc": {"start": {"line": 144, "column": 57}, "end": {"line": 157, "column": 9}}, "line": 144}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 186, "column": 31}, "end": {"line": 186, "column": 32}}, "loc": {"start": {"line": 186, "column": 37}, "end": {"line": 192, "column": 1}}, "line": 186}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\navigation\\types.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\navigation\\types.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\screens\\LibraryScreen.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\screens\\LibraryScreen.tsx", "statementMap": {"0": {"start": {"line": 12, "column": 39}, "end": {"line": 50, "column": 1}}, "1": {"start": {"line": 13, "column": 2}, "end": {"line": 49, "column": 4}}, "2": {"start": {"line": 52, "column": 15}, "end": {"line": 95, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 39}, "end": {"line": 12, "column": 40}}, "loc": {"start": {"line": 12, "column": 72}, "end": {"line": 50, "column": 1}}, "line": 12}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\screens\\NotesScreen.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\screens\\NotesScreen.tsx", "statementMap": {"0": {"start": {"line": 12, "column": 37}, "end": {"line": 49, "column": 1}}, "1": {"start": {"line": 13, "column": 2}, "end": {"line": 48, "column": 4}}, "2": {"start": {"line": 51, "column": 15}, "end": {"line": 94, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 37}, "end": {"line": 12, "column": 38}}, "loc": {"start": {"line": 12, "column": 70}, "end": {"line": 49, "column": 1}}, "line": 12}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\screens\\RecentScreen.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\screens\\RecentScreen.tsx", "statementMap": {"0": {"start": {"line": 12, "column": 38}, "end": {"line": 41, "column": 1}}, "1": {"start": {"line": 13, "column": 2}, "end": {"line": 40, "column": 4}}, "2": {"start": {"line": 43, "column": 15}, "end": {"line": 86, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 38}, "end": {"line": 12, "column": 39}}, "loc": {"start": {"line": 12, "column": 71}, "end": {"line": 41, "column": 1}}, "line": 12}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\screens\\SettingsScreen.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\screens\\SettingsScreen.tsx", "statementMap": {"0": {"start": {"line": 20, "column": 40}, "end": {"line": 156, "column": 1}}, "1": {"start": {"line": 21, "column": 19}, "end": {"line": 21, "column": 35}}, "2": {"start": {"line": 22, "column": 19}, "end": {"line": 22, "column": 58}}, "3": {"start": {"line": 22, "column": 43}, "end": {"line": 22, "column": 57}}, "4": {"start": {"line": 24, "column": 28}, "end": {"line": 33, "column": 3}}, "5": {"start": {"line": 25, "column": 55}, "end": {"line": 29, "column": 5}}, "6": {"start": {"line": 30, "column": 25}, "end": {"line": 30, "column": 55}}, "7": {"start": {"line": 31, "column": 22}, "end": {"line": 31, "column": 64}}, "8": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 34}}, "9": {"start": {"line": 35, "column": 31}, "end": {"line": 39, "column": 3}}, "10": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 72}}, "11": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 35}}, "12": {"start": {"line": 41, "column": 2}, "end": {"line": 155, "column": 4}}, "13": {"start": {"line": 158, "column": 15}, "end": {"line": 206, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 20, "column": 40}, "end": {"line": 20, "column": 41}}, "loc": {"start": {"line": 20, "column": 73}, "end": {"line": 156, "column": 1}}, "line": 20}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 22, "column": 34}, "end": {"line": 22, "column": 35}}, "loc": {"start": {"line": 22, "column": 43}, "end": {"line": 22, "column": 57}}, "line": 22}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 24, "column": 28}, "end": {"line": 24, "column": 29}}, "loc": {"start": {"line": 24, "column": 34}, "end": {"line": 33, "column": 3}}, "line": 24}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 35, "column": 31}, "end": {"line": 35, "column": 32}}, "loc": {"start": {"line": 35, "column": 37}, "end": {"line": 39, "column": 3}}, "line": 35}}, "branchMap": {"0": {"loc": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 72}}, "type": "cond-expr", "locations": [{"start": {"line": 37, "column": 33}, "end": {"line": 37, "column": 35}}, {"start": {"line": 37, "column": 38}, "end": {"line": 37, "column": 72}}], "line": 37}, "1": {"loc": {"start": {"line": 37, "column": 38}, "end": {"line": 37, "column": 72}}, "type": "cond-expr", "locations": [{"start": {"line": 37, "column": 65}, "end": {"line": 37, "column": 67}}, {"start": {"line": 37, "column": 70}, "end": {"line": 37, "column": 72}}], "line": 37}, "2": {"loc": {"start": {"line": 83, "column": 15}, "end": {"line": 83, "column": 57}}, "type": "cond-expr", "locations": [{"start": {"line": 83, "column": 35}, "end": {"line": 83, "column": 44}}, {"start": {"line": 83, "column": 47}, "end": {"line": 83, "column": 57}}], "line": 83}, "3": {"loc": {"start": {"line": 94, "column": 15}, "end": {"line": 94, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 94, "column": 45}, "end": {"line": 94, "column": 54}}, {"start": {"line": 94, "column": 57}, "end": {"line": 94, "column": 67}}], "line": 94}, "4": {"loc": {"start": {"line": 119, "column": 15}, "end": {"line": 119, "column": 63}}, "type": "cond-expr", "locations": [{"start": {"line": 119, "column": 41}, "end": {"line": 119, "column": 50}}, {"start": {"line": 119, "column": 53}, "end": {"line": 119, "column": 63}}], "line": 119}, "5": {"loc": {"start": {"line": 126, "column": 15}, "end": {"line": 126, "column": 60}}, "type": "cond-expr", "locations": [{"start": {"line": 126, "column": 38}, "end": {"line": 126, "column": 47}}, {"start": {"line": 126, "column": 50}, "end": {"line": 126, "column": 60}}], "line": 126}, "6": {"loc": {"start": {"line": 133, "column": 15}, "end": {"line": 133, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 133, "column": 40}, "end": {"line": 133, "column": 49}}, {"start": {"line": 133, "column": 52}, "end": {"line": 133, "column": 62}}], "line": 133}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\screens\\WelcomeScreen.tsx": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\screens\\WelcomeScreen.tsx", "statementMap": {"0": {"start": {"line": 12, "column": 39}, "end": {"line": 36, "column": 1}}, "1": {"start": {"line": 13, "column": 27}, "end": {"line": 15, "column": 3}}, "2": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 36}}, "3": {"start": {"line": 17, "column": 2}, "end": {"line": 35, "column": 4}}, "4": {"start": {"line": 38, "column": 15}, "end": {"line": 89, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 12, "column": 39}, "end": {"line": 12, "column": 40}}, "loc": {"start": {"line": 12, "column": 59}, "end": {"line": 36, "column": 1}}, "line": 12}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 27}, "end": {"line": 13, "column": 28}}, "loc": {"start": {"line": 13, "column": 33}, "end": {"line": 15, "column": 3}}, "line": 13}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\ErrorHandler.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\ErrorHandler.ts", "statementMap": {"0": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 19}}, "1": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 27}}, "2": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 21}}, "3": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 29}}, "4": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 35}}, "5": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 67}}, "6": {"start": {"line": 64, "column": 4}, "end": {"line": 68, "column": 6}}, "7": {"start": {"line": 72, "column": 4}, "end": {"line": 89, "column": 5}}, "8": {"start": {"line": 74, "column": 8}, "end": {"line": 74, "column": 87}}, "9": {"start": {"line": 76, "column": 8}, "end": {"line": 76, "column": 76}}, "10": {"start": {"line": 78, "column": 8}, "end": {"line": 78, "column": 76}}, "11": {"start": {"line": 80, "column": 8}, "end": {"line": 80, "column": 82}}, "12": {"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": 62}}, "13": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 61}}, "14": {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 79}}, "15": {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 65}}, "16": {"start": {"line": 111, "column": 67}, "end": {"line": 111, "column": 76}}, "17": {"start": {"line": 112, "column": 45}, "end": {"line": 112, "column": 54}}, "18": {"start": {"line": 115, "column": 4}, "end": {"line": 122, "column": 6}}, "19": {"start": {"line": 124, "column": 4}, "end": {"line": 124, "column": 35}}, "20": {"start": {"line": 125, "column": 4}, "end": {"line": 125, "column": 35}}, "21": {"start": {"line": 130, "column": 4}, "end": {"line": 130, "column": 49}}, "22": {"start": {"line": 130, "column": 42}, "end": {"line": 130, "column": 49}}, "23": {"start": {"line": 133, "column": 28}, "end": {"line": 133, "column": 64}}, "24": {"start": {"line": 134, "column": 4}, "end": {"line": 148, "column": 6}}, "25": {"start": {"line": 135, "column": 6}, "end": {"line": 143, "column": 8}}, "26": {"start": {"line": 145, "column": 6}, "end": {"line": 147, "column": 7}}, "27": {"start": {"line": 146, "column": 8}, "end": {"line": 146, "column": 31}}, "28": {"start": {"line": 151, "column": 4}, "end": {"line": 168, "column": 5}}, "29": {"start": {"line": 152, "column": 35}, "end": {"line": 152, "column": 64}}, "30": {"start": {"line": 153, "column": 6}, "end": {"line": 167, "column": 9}}, "31": {"start": {"line": 154, "column": 8}, "end": {"line": 162, "column": 10}}, "32": {"start": {"line": 164, "column": 8}, "end": {"line": 166, "column": 9}}, "33": {"start": {"line": 165, "column": 10}, "end": {"line": 165, "column": 47}}, "34": {"start": {"line": 174, "column": 4}, "end": {"line": 189, "column": 7}}, "35": {"start": {"line": 177, "column": 8}, "end": {"line": 186, "column": 9}}, "36": {"start": {"line": 179, "column": 10}, "end": {"line": 182, "column": 12}}, "37": {"start": {"line": 183, "column": 10}, "end": {"line": 183, "column": 22}}, "38": {"start": {"line": 185, "column": 10}, "end": {"line": 185, "column": 23}}, "39": {"start": {"line": 192, "column": 4}, "end": {"line": 207, "column": 7}}, "40": {"start": {"line": 195, "column": 8}, "end": {"line": 204, "column": 9}}, "41": {"start": {"line": 197, "column": 10}, "end": {"line": 200, "column": 12}}, "42": {"start": {"line": 201, "column": 10}, "end": {"line": 201, "column": 22}}, "43": {"start": {"line": 203, "column": 10}, "end": {"line": 203, "column": 23}}, "44": {"start": {"line": 210, "column": 4}, "end": {"line": 225, "column": 7}}, "45": {"start": {"line": 213, "column": 8}, "end": {"line": 222, "column": 9}}, "46": {"start": {"line": 214, "column": 10}, "end": {"line": 217, "column": 12}}, "47": {"start": {"line": 219, "column": 10}, "end": {"line": 219, "column": 22}}, "48": {"start": {"line": 221, "column": 10}, "end": {"line": 221, "column": 23}}, "49": {"start": {"line": 231, "column": 6}, "end": {"line": 231, "column": 71}}, "50": {"start": {"line": 234, "column": 4}, "end": {"line": 234, "column": 28}}, "51": {"start": {"line": 237, "column": 4}, "end": {"line": 237, "column": 39}}, "52": {"start": {"line": 240, "column": 4}, "end": {"line": 249, "column": 5}}, "53": {"start": {"line": 241, "column": 24}, "end": {"line": 241, "column": 60}}, "54": {"start": {"line": 242, "column": 6}, "end": {"line": 248, "column": 7}}, "55": {"start": {"line": 243, "column": 8}, "end": {"line": 246, "column": 11}}, "56": {"start": {"line": 247, "column": 8}, "end": {"line": 247, "column": 15}}, "57": {"start": {"line": 252, "column": 4}, "end": {"line": 254, "column": 5}}, "58": {"start": {"line": 253, "column": 6}, "end": {"line": 253, "column": 32}}, "59": {"start": {"line": 259, "column": 4}, "end": {"line": 265, "column": 6}}, "60": {"start": {"line": 270, "column": 21}, "end": {"line": 270, "column": 53}}, "61": {"start": {"line": 271, "column": 20}, "end": {"line": 276, "column": 5}}, "62": {"start": {"line": 278, "column": 4}, "end": {"line": 287, "column": 5}}, "63": {"start": {"line": 280, "column": 8}, "end": {"line": 280, "column": 69}}, "64": {"start": {"line": 281, "column": 8}, "end": {"line": 281, "column": 14}}, "65": {"start": {"line": 283, "column": 8}, "end": {"line": 283, "column": 69}}, "66": {"start": {"line": 284, "column": 8}, "end": {"line": 284, "column": 14}}, "67": {"start": {"line": 286, "column": 8}, "end": {"line": 286, "column": 61}}, "68": {"start": {"line": 292, "column": 4}, "end": {"line": 303, "column": 5}}, "69": {"start": {"line": 294, "column": 8}, "end": {"line": 294, "column": 29}}, "70": {"start": {"line": 296, "column": 8}, "end": {"line": 296, "column": 30}}, "71": {"start": {"line": 298, "column": 8}, "end": {"line": 298, "column": 30}}, "72": {"start": {"line": 300, "column": 8}, "end": {"line": 300, "column": 30}}, "73": {"start": {"line": 302, "column": 8}, "end": {"line": 302, "column": 30}}, "74": {"start": {"line": 308, "column": 16}, "end": {"line": 308, "column": 48}}, "75": {"start": {"line": 309, "column": 18}, "end": {"line": 309, "column": 48}}, "76": {"start": {"line": 310, "column": 4}, "end": {"line": 310, "column": 41}}, "77": {"start": {"line": 313, "column": 4}, "end": {"line": 319, "column": 5}}, "78": {"start": {"line": 314, "column": 6}, "end": {"line": 318, "column": 9}}, "79": {"start": {"line": 324, "column": 23}, "end": {"line": 324, "column": 62}}, "80": {"start": {"line": 325, "column": 4}, "end": {"line": 325, "column": 61}}, "81": {"start": {"line": 325, "column": 48}, "end": {"line": 325, "column": 61}}, "82": {"start": {"line": 327, "column": 4}, "end": {"line": 349, "column": 5}}, "83": {"start": {"line": 328, "column": 6}, "end": {"line": 348, "column": 7}}, "84": {"start": {"line": 329, "column": 8}, "end": {"line": 332, "column": 10}}, "85": {"start": {"line": 333, "column": 24}, "end": {"line": 333, "column": 47}}, "86": {"start": {"line": 334, "column": 8}, "end": {"line": 340, "column": 9}}, "87": {"start": {"line": 335, "column": 10}, "end": {"line": 338, "column": 12}}, "88": {"start": {"line": 339, "column": 10}, "end": {"line": 339, "column": 22}}, "89": {"start": {"line": 342, "column": 8}, "end": {"line": 347, "column": 10}}, "90": {"start": {"line": 351, "column": 4}, "end": {"line": 351, "column": 17}}, "91": {"start": {"line": 357, "column": 4}, "end": {"line": 360, "column": 7}}, "92": {"start": {"line": 365, "column": 23}, "end": {"line": 365, "column": 67}}, "93": {"start": {"line": 366, "column": 4}, "end": {"line": 366, "column": 30}}, "94": {"start": {"line": 367, "column": 4}, "end": {"line": 367, "column": 55}}, "95": {"start": {"line": 377, "column": 25}, "end": {"line": 377, "column": 56}}, "96": {"start": {"line": 378, "column": 29}, "end": {"line": 378, "column": 64}}, "97": {"start": {"line": 381, "column": 27}, "end": {"line": 384, "column": 19}}, "98": {"start": {"line": 382, "column": 32}, "end": {"line": 382, "column": 48}}, "99": {"start": {"line": 383, "column": 22}, "end": {"line": 383, "column": 39}}, "100": {"start": {"line": 386, "column": 4}, "end": {"line": 394, "column": 6}}, "101": {"start": {"line": 388, "column": 24}, "end": {"line": 388, "column": 35}}, "102": {"start": {"line": 399, "column": 4}, "end": {"line": 399, "column": 29}}, "103": {"start": {"line": 400, "column": 4}, "end": {"line": 400, "column": 64}}, "104": {"start": {"line": 405, "column": 28}, "end": {"line": 405, "column": 53}}, "105": {"start": {"line": 408, "column": 27}, "end": {"line": 409, "column": 33}}, "106": {"start": {"line": 409, "column": 2}, "end": {"line": 409, "column": 33}}, "107": {"start": {"line": 411, "column": 27}, "end": {"line": 418, "column": 77}}, "108": {"start": {"line": 418, "column": 5}, "end": {"line": 418, "column": 77}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 3}}, "loc": {"start": {"line": 57, "column": 4}, "end": {"line": 69, "column": 3}}, "line": 57}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": 3}}, "loc": {"start": {"line": 71, "column": 42}, "end": {"line": 90, "column": 3}}, "line": 71}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 114, "column": 2}, "end": {"line": 114, "column": 3}}, "loc": {"start": {"line": 114, "column": 56}, "end": {"line": 126, "column": 3}}, "line": 114}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 129, "column": 2}, "end": {"line": 129, "column": 3}}, "loc": {"start": {"line": 129, "column": 42}, "end": {"line": 169, "column": 3}}, "line": 129}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 134, "column": 43}, "end": {"line": 134, "column": 44}}, "loc": {"start": {"line": 134, "column": 59}, "end": {"line": 148, "column": 5}}, "line": 134}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 153, "column": 34}, "end": {"line": 153, "column": 35}}, "loc": {"start": {"line": 153, "column": 54}, "end": {"line": 167, "column": 7}}, "line": 153}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 172, "column": 2}, "end": {"line": 172, "column": 3}}, "loc": {"start": {"line": 172, "column": 42}, "end": {"line": 226, "column": 3}}, "line": 172}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 176, "column": 14}, "end": {"line": 176, "column": 15}}, "loc": {"start": {"line": 176, "column": 26}, "end": {"line": 187, "column": 7}}, "line": 176}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 194, "column": 14}, "end": {"line": 194, "column": 15}}, "loc": {"start": {"line": 194, "column": 26}, "end": {"line": 205, "column": 7}}, "line": 194}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 212, "column": 14}, "end": {"line": 212, "column": 15}}, "loc": {"start": {"line": 212, "column": 26}, "end": {"line": 223, "column": 7}}, "line": 212}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 229, "column": 2}, "end": {"line": 229, "column": 3}}, "loc": {"start": {"line": 229, "column": 60}, "end": {"line": 255, "column": 3}}, "line": 229}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 258, "column": 2}, "end": {"line": 258, "column": 3}}, "loc": {"start": {"line": 258, "column": 52}, "end": {"line": 266, "column": 3}}, "line": 258}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 269, "column": 2}, "end": {"line": 269, "column": 3}}, "loc": {"start": {"line": 269, "column": 42}, "end": {"line": 288, "column": 3}}, "line": 269}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 291, "column": 2}, "end": {"line": 291, "column": 3}}, "loc": {"start": {"line": 291, "column": 57}, "end": {"line": 304, "column": 3}}, "line": 291}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 307, "column": 2}, "end": {"line": 307, "column": 3}}, "loc": {"start": {"line": 307, "column": 53}, "end": {"line": 320, "column": 3}}, "line": 307}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 323, "column": 2}, "end": {"line": 323, "column": 3}}, "loc": {"start": {"line": 323, "column": 67}, "end": {"line": 352, "column": 3}}, "line": 323}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 355, "column": 2}, "end": {"line": 355, "column": 3}}, "loc": {"start": {"line": 355, "column": 44}, "end": {"line": 361, "column": 3}}, "line": 355}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 364, "column": 2}, "end": {"line": 364, "column": 3}}, "loc": {"start": {"line": 364, "column": 78}, "end": {"line": 368, "column": 3}}, "line": 364}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 371, "column": 2}, "end": {"line": 371, "column": 3}}, "loc": {"start": {"line": 376, "column": 4}, "end": {"line": 395, "column": 3}}, "line": 376}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 382, "column": 11}, "end": {"line": 382, "column": 12}}, "loc": {"start": {"line": 382, "column": 32}, "end": {"line": 382, "column": 48}}, "line": 382}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 383, "column": 12}, "end": {"line": 383, "column": 13}}, "loc": {"start": {"line": 383, "column": 22}, "end": {"line": 383, "column": 39}}, "line": 383}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 388, "column": 8}, "end": {"line": 388, "column": 9}}, "loc": {"start": {"line": 388, "column": 24}, "end": {"line": 388, "column": 35}}, "line": 388}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 398, "column": 2}, "end": {"line": 398, "column": 3}}, "loc": {"start": {"line": 398, "column": 25}, "end": {"line": 401, "column": 3}}, "line": 398}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 408, "column": 27}, "end": {"line": 408, "column": 28}}, "loc": {"start": {"line": 409, "column": 2}, "end": {"line": 409, "column": 33}}, "line": 409}, "24": {"name": "(anonymous_24)", "decl": {"start": {"line": 411, "column": 27}, "end": {"line": 411, "column": 28}}, "loc": {"start": {"line": 418, "column": 5}, "end": {"line": 418, "column": 77}}, "line": 418}}, "branchMap": {"0": {"loc": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 39}}, "type": "default-arg", "locations": [{"start": {"line": 52, "column": 22}, "end": {"line": 52, "column": 39}}], "line": 52}, "1": {"loc": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 50}}, "type": "default-arg", "locations": [{"start": {"line": 53, "column": 30}, "end": {"line": 53, "column": 50}}], "line": 53}, "2": {"loc": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 39}}, "type": "default-arg", "locations": [{"start": {"line": 54, "column": 37}, "end": {"line": 54, "column": 39}}], "line": 54}, "3": {"loc": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 22}}, "type": "default-arg", "locations": [{"start": {"line": 55, "column": 18}, "end": {"line": 55, "column": 22}}], "line": 55}, "4": {"loc": {"start": {"line": 63, "column": 23}, "end": {"line": 63, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 63, "column": 23}, "end": {"line": 63, "column": 34}}, {"start": {"line": 63, "column": 38}, "end": {"line": 63, "column": 66}}], "line": 63}, "5": {"loc": {"start": {"line": 72, "column": 4}, "end": {"line": 89, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 73, "column": 6}, "end": {"line": 74, "column": 87}}, {"start": {"line": 75, "column": 6}, "end": {"line": 76, "column": 76}}, {"start": {"line": 77, "column": 6}, "end": {"line": 78, "column": 76}}, {"start": {"line": 79, "column": 6}, "end": {"line": 80, "column": 82}}, {"start": {"line": 81, "column": 6}, "end": {"line": 82, "column": 62}}, {"start": {"line": 83, "column": 6}, "end": {"line": 84, "column": 61}}, {"start": {"line": 85, "column": 6}, "end": {"line": 86, "column": 79}}, {"start": {"line": 87, "column": 6}, "end": {"line": 88, "column": 65}}], "line": 72}, "6": {"loc": {"start": {"line": 114, "column": 14}, "end": {"line": 114, "column": 54}}, "type": "default-arg", "locations": [{"start": {"line": 114, "column": 52}, "end": {"line": 114, "column": 54}}], "line": 114}, "7": {"loc": {"start": {"line": 130, "column": 4}, "end": {"line": 130, "column": 49}}, "type": "if", "locations": [{"start": {"line": 130, "column": 4}, "end": {"line": 130, "column": 49}}, {"start": {}, "end": {}}], "line": 130}, "8": {"loc": {"start": {"line": 145, "column": 6}, "end": {"line": 147, "column": 7}}, "type": "if", "locations": [{"start": {"line": 145, "column": 6}, "end": {"line": 147, "column": 7}}, {"start": {}, "end": {}}], "line": 145}, "9": {"loc": {"start": {"line": 151, "column": 4}, "end": {"line": 168, "column": 5}}, "type": "if", "locations": [{"start": {"line": 151, "column": 4}, "end": {"line": 168, "column": 5}}, {"start": {}, "end": {}}], "line": 151}, "10": {"loc": {"start": {"line": 158, "column": 12}, "end": {"line": 158, "column": 65}}, "type": "cond-expr", "locations": [{"start": {"line": 158, "column": 22}, "end": {"line": 158, "column": 44}}, {"start": {"line": 158, "column": 47}, "end": {"line": 158, "column": 65}}], "line": 158}, "11": {"loc": {"start": {"line": 164, "column": 8}, "end": {"line": 166, "column": 9}}, "type": "if", "locations": [{"start": {"line": 164, "column": 8}, "end": {"line": 166, "column": 9}}, {"start": {}, "end": {}}], "line": 164}, "12": {"loc": {"start": {"line": 231, "column": 6}, "end": {"line": 231, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 231, "column": 34}, "end": {"line": 231, "column": 39}}, {"start": {"line": 231, "column": 42}, "end": {"line": 231, "column": 71}}], "line": 231}, "13": {"loc": {"start": {"line": 240, "column": 4}, "end": {"line": 249, "column": 5}}, "type": "if", "locations": [{"start": {"line": 240, "column": 4}, "end": {"line": 249, "column": 5}}, {"start": {}, "end": {}}], "line": 240}, "14": {"loc": {"start": {"line": 240, "column": 8}, "end": {"line": 240, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 240, "column": 8}, "end": {"line": 240, "column": 34}}, {"start": {"line": 240, "column": 38}, "end": {"line": 240, "column": 58}}], "line": 240}, "15": {"loc": {"start": {"line": 242, "column": 6}, "end": {"line": 248, "column": 7}}, "type": "if", "locations": [{"start": {"line": 242, "column": 6}, "end": {"line": 248, "column": 7}}, {"start": {}, "end": {}}], "line": 242}, "16": {"loc": {"start": {"line": 252, "column": 4}, "end": {"line": 254, "column": 5}}, "type": "if", "locations": [{"start": {"line": 252, "column": 4}, "end": {"line": 254, "column": 5}}, {"start": {}, "end": {}}], "line": 252}, "17": {"loc": {"start": {"line": 278, "column": 4}, "end": {"line": 287, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 279, "column": 6}, "end": {"line": 281, "column": 14}}, {"start": {"line": 282, "column": 6}, "end": {"line": 284, "column": 14}}, {"start": {"line": 285, "column": 6}, "end": {"line": 286, "column": 61}}], "line": 278}, "18": {"loc": {"start": {"line": 292, "column": 4}, "end": {"line": 303, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 293, "column": 6}, "end": {"line": 294, "column": 29}}, {"start": {"line": 295, "column": 6}, "end": {"line": 296, "column": 30}}, {"start": {"line": 297, "column": 6}, "end": {"line": 298, "column": 30}}, {"start": {"line": 299, "column": 6}, "end": {"line": 300, "column": 30}}, {"start": {"line": 301, "column": 6}, "end": {"line": 302, "column": 30}}], "line": 292}, "19": {"loc": {"start": {"line": 309, "column": 18}, "end": {"line": 309, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 309, "column": 18}, "end": {"line": 309, "column": 43}}, {"start": {"line": 309, "column": 47}, "end": {"line": 309, "column": 48}}], "line": 309}, "20": {"loc": {"start": {"line": 313, "column": 4}, "end": {"line": 319, "column": 5}}, "type": "if", "locations": [{"start": {"line": 313, "column": 4}, "end": {"line": 319, "column": 5}}, {"start": {}, "end": {}}], "line": 313}, "21": {"loc": {"start": {"line": 325, "column": 4}, "end": {"line": 325, "column": 61}}, "type": "if", "locations": [{"start": {"line": 325, "column": 4}, "end": {"line": 325, "column": 61}}, {"start": {}, "end": {}}], "line": 325}, "22": {"loc": {"start": {"line": 325, "column": 8}, "end": {"line": 325, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 325, "column": 8}, "end": {"line": 325, "column": 19}}, {"start": {"line": 325, "column": 23}, "end": {"line": 325, "column": 46}}], "line": 325}, "23": {"loc": {"start": {"line": 334, "column": 8}, "end": {"line": 340, "column": 9}}, "type": "if", "locations": [{"start": {"line": 334, "column": 8}, "end": {"line": 340, "column": 9}}, {"start": {}, "end": {}}], "line": 334}, "24": {"loc": {"start": {"line": 365, "column": 23}, "end": {"line": 365, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 365, "column": 23}, "end": {"line": 365, "column": 61}}, {"start": {"line": 365, "column": 65}, "end": {"line": 365, "column": 67}}], "line": 365}, "25": {"loc": {"start": {"line": 413, "column": 2}, "end": {"line": 413, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 413, "column": 20}, "end": {"line": 413, "column": 37}}], "line": 413}, "26": {"loc": {"start": {"line": 414, "column": 2}, "end": {"line": 414, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 414, "column": 28}, "end": {"line": 414, "column": 48}}], "line": 414}, "27": {"loc": {"start": {"line": 415, "column": 2}, "end": {"line": 415, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 415, "column": 35}, "end": {"line": 415, "column": 37}}], "line": 415}, "28": {"loc": {"start": {"line": 416, "column": 2}, "end": {"line": 416, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 416, "column": 16}, "end": {"line": 416, "column": 20}}], "line": 416}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0, 0], "5": [0, 0, 0, 0, 0, 0, 0, 0], "6": [0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0, 0], "18": [0, 0, 0, 0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0], "26": [0], "27": [0], "28": [0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\Logger.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\Logger.ts", "statementMap": {"0": {"start": {"line": 41, "column": 37}, "end": {"line": 55, "column": 1}}, "1": {"start": {"line": 60, "column": 33}, "end": {"line": 60, "column": 35}}, "2": {"start": {"line": 61, "column": 26}, "end": {"line": 61, "column": 31}}, "3": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 51}}, "4": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": 46}}, "5": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 35}}, "6": {"start": {"line": 70, "column": 28}, "end": {"line": 70, "column": 35}}, "7": {"start": {"line": 72, "column": 4}, "end": {"line": 82, "column": 5}}, "8": {"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": 7}}, "9": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": 41}}, "10": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": 32}}, "11": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 76}}, "12": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 59}}, "13": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": 54}}, "14": {"start": {"line": 91, "column": 4}, "end": {"line": 91, "column": 53}}, "15": {"start": {"line": 95, "column": 4}, "end": {"line": 95, "column": 53}}, "16": {"start": {"line": 99, "column": 23}, "end": {"line": 99, "column": 35}}, "17": {"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": 66}}, "18": {"start": {"line": 104, "column": 23}, "end": {"line": 104, "column": 35}}, "19": {"start": {"line": 105, "column": 4}, "end": {"line": 105, "column": 66}}, "20": {"start": {"line": 117, "column": 4}, "end": {"line": 117, "column": 45}}, "21": {"start": {"line": 117, "column": 38}, "end": {"line": 117, "column": 45}}, "22": {"start": {"line": 120, "column": 31}, "end": {"line": 129, "column": 5}}, "23": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 33}}, "24": {"start": {"line": 135, "column": 4}, "end": {"line": 137, "column": 5}}, "25": {"start": {"line": 136, "column": 6}, "end": {"line": 136, "column": 28}}, "26": {"start": {"line": 140, "column": 4}, "end": {"line": 142, "column": 5}}, "27": {"start": {"line": 141, "column": 6}, "end": {"line": 141, "column": 34}}, "28": {"start": {"line": 145, "column": 4}, "end": {"line": 147, "column": 5}}, "29": {"start": {"line": 146, "column": 6}, "end": {"line": 146, "column": 25}}, "30": {"start": {"line": 150, "column": 4}, "end": {"line": 152, "column": 5}}, "31": {"start": {"line": 151, "column": 6}, "end": {"line": 151, "column": 38}}, "32": {"start": {"line": 157, "column": 19}, "end": {"line": 157, "column": 57}}, "33": {"start": {"line": 159, "column": 4}, "end": {"line": 173, "column": 5}}, "34": {"start": {"line": 161, "column": 8}, "end": {"line": 161, "column": 42}}, "35": {"start": {"line": 162, "column": 8}, "end": {"line": 162, "column": 14}}, "36": {"start": {"line": 164, "column": 8}, "end": {"line": 164, "column": 41}}, "37": {"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 14}}, "38": {"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 41}}, "39": {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 14}}, "40": {"start": {"line": 171, "column": 8}, "end": {"line": 171, "column": 60}}, "41": {"start": {"line": 172, "column": 8}, "end": {"line": 172, "column": 14}}, "42": {"start": {"line": 178, "column": 4}, "end": {"line": 183, "column": 5}}, "43": {"start": {"line": 179, "column": 26}, "end": {"line": 179, "column": 51}}, "44": {"start": {"line": 180, "column": 6}, "end": {"line": 180, "column": 79}}, "45": {"start": {"line": 182, "column": 6}, "end": {"line": 182, "column": 54}}, "46": {"start": {"line": 188, "column": 4}, "end": {"line": 196, "column": 5}}, "47": {"start": {"line": 189, "column": 25}, "end": {"line": 189, "column": 68}}, "48": {"start": {"line": 190, "column": 6}, "end": {"line": 193, "column": 7}}, "49": {"start": {"line": 191, "column": 33}, "end": {"line": 191, "column": 55}}, "50": {"start": {"line": 192, "column": 8}, "end": {"line": 192, "column": 29}}, "51": {"start": {"line": 195, "column": 6}, "end": {"line": 195, "column": 64}}, "52": {"start": {"line": 203, "column": 4}, "end": {"line": 203, "column": 41}}, "53": {"start": {"line": 211, "column": 23}, "end": {"line": 211, "column": 36}}, "54": {"start": {"line": 213, "column": 4}, "end": {"line": 215, "column": 5}}, "55": {"start": {"line": 214, "column": 6}, "end": {"line": 214, "column": 68}}, "56": {"start": {"line": 214, "column": 48}, "end": {"line": 214, "column": 66}}, "57": {"start": {"line": 217, "column": 4}, "end": {"line": 219, "column": 5}}, "58": {"start": {"line": 218, "column": 6}, "end": {"line": 218, "column": 75}}, "59": {"start": {"line": 218, "column": 48}, "end": {"line": 218, "column": 73}}, "60": {"start": {"line": 221, "column": 4}, "end": {"line": 221, "column": 38}}, "61": {"start": {"line": 226, "column": 4}, "end": {"line": 226, "column": 23}}, "62": {"start": {"line": 227, "column": 4}, "end": {"line": 229, "column": 5}}, "63": {"start": {"line": 228, "column": 6}, "end": {"line": 228, "column": 53}}, "64": {"start": {"line": 230, "column": 4}, "end": {"line": 230, "column": 37}}, "65": {"start": {"line": 235, "column": 4}, "end": {"line": 235, "column": 50}}, "66": {"start": {"line": 240, "column": 4}, "end": {"line": 240, "column": 51}}, "67": {"start": {"line": 241, "column": 4}, "end": {"line": 241, "column": 64}}, "68": {"start": {"line": 246, "column": 4}, "end": {"line": 246, "column": 78}}, "69": {"start": {"line": 251, "column": 4}, "end": {"line": 251, "column": 74}}, "70": {"start": {"line": 256, "column": 4}, "end": {"line": 256, "column": 26}}, "71": {"start": {"line": 266, "column": 24}, "end": {"line": 269, "column": 38}}, "72": {"start": {"line": 267, "column": 6}, "end": {"line": 267, "column": 49}}, "73": {"start": {"line": 268, "column": 6}, "end": {"line": 268, "column": 17}}, "74": {"start": {"line": 271, "column": 27}, "end": {"line": 274, "column": 36}}, "75": {"start": {"line": 272, "column": 6}, "end": {"line": 272, "column": 55}}, "76": {"start": {"line": 273, "column": 6}, "end": {"line": 273, "column": 17}}, "77": {"start": {"line": 276, "column": 4}, "end": {"line": 281, "column": 6}}, "78": {"start": {"line": 286, "column": 22}, "end": {"line": 286, "column": 41}}, "79": {"start": {"line": 289, "column": 19}, "end": {"line": 300, "column": 1}}, "80": {"start": {"line": 291, "column": 4}, "end": {"line": 291, "column": 41}}, "81": {"start": {"line": 293, "column": 4}, "end": {"line": 293, "column": 40}}, "82": {"start": {"line": 295, "column": 4}, "end": {"line": 295, "column": 40}}, "83": {"start": {"line": 297, "column": 4}, "end": {"line": 297, "column": 48}}, "84": {"start": {"line": 299, "column": 4}, "end": {"line": 299, "column": 48}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 3}}, "loc": {"start": {"line": 63, "column": 50}, "end": {"line": 66, "column": 3}}, "line": 63}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 3}}, "loc": {"start": {"line": 69, "column": 36}, "end": {"line": 83, "column": 3}}, "line": 69}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 3}}, "loc": {"start": {"line": 86, "column": 61}, "end": {"line": 88, "column": 3}}, "line": 86}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 90, "column": 2}, "end": {"line": 90, "column": 3}}, "loc": {"start": {"line": 90, "column": 60}, "end": {"line": 92, "column": 3}}, "line": 90}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 94, "column": 2}, "end": {"line": 94, "column": 3}}, "loc": {"start": {"line": 94, "column": 60}, "end": {"line": 96, "column": 3}}, "line": 94}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 98, "column": 2}, "end": {"line": 98, "column": 3}}, "loc": {"start": {"line": 98, "column": 76}, "end": {"line": 101, "column": 3}}, "line": 98}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 3}}, "loc": {"start": {"line": 103, "column": 76}, "end": {"line": 106, "column": 3}}, "line": 103}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 109, "column": 2}, "end": {"line": 109, "column": 3}}, "loc": {"start": {"line": 115, "column": 10}, "end": {"line": 153, "column": 3}}, "line": 115}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 156, "column": 2}, "end": {"line": 156, "column": 3}}, "loc": {"start": {"line": 156, "column": 46}, "end": {"line": 174, "column": 3}}, "line": 156}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 177, "column": 2}, "end": {"line": 177, "column": 3}}, "loc": {"start": {"line": 177, "column": 45}, "end": {"line": 184, "column": 3}}, "line": 177}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 187, "column": 2}, "end": {"line": 187, "column": 3}}, "loc": {"start": {"line": 187, "column": 53}, "end": {"line": 197, "column": 3}}, "line": 187}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 200, "column": 2}, "end": {"line": 200, "column": 3}}, "loc": {"start": {"line": 200, "column": 50}, "end": {"line": 207, "column": 3}}, "line": 200}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 210, "column": 2}, "end": {"line": 210, "column": 3}}, "loc": {"start": {"line": 210, "column": 72}, "end": {"line": 222, "column": 3}}, "line": 210}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 214, "column": 41}, "end": {"line": 214, "column": 42}}, "loc": {"start": {"line": 214, "column": 48}, "end": {"line": 214, "column": 66}}, "line": 214}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 218, "column": 41}, "end": {"line": 218, "column": 42}}, "loc": {"start": {"line": 218, "column": 48}, "end": {"line": 218, "column": 73}}, "line": 218}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 225, "column": 2}, "end": {"line": 225, "column": 3}}, "loc": {"start": {"line": 225, "column": 35}, "end": {"line": 231, "column": 3}}, "line": 225}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 234, "column": 2}, "end": {"line": 234, "column": 3}}, "loc": {"start": {"line": 234, "column": 23}, "end": {"line": 236, "column": 3}}, "line": 234}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 239, "column": 2}, "end": {"line": 239, "column": 3}}, "loc": {"start": {"line": 239, "column": 55}, "end": {"line": 242, "column": 3}}, "line": 239}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 245, "column": 2}, "end": {"line": 245, "column": 3}}, "loc": {"start": {"line": 245, "column": 38}, "end": {"line": 247, "column": 3}}, "line": 245}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 250, "column": 2}, "end": {"line": 250, "column": 3}}, "loc": {"start": {"line": 250, "column": 34}, "end": {"line": 252, "column": 3}}, "line": 250}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 255, "column": 2}, "end": {"line": 255, "column": 3}}, "loc": {"start": {"line": 255, "column": 25}, "end": {"line": 257, "column": 3}}, "line": 255}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 260, "column": 2}, "end": {"line": 260, "column": 3}}, "loc": {"start": {"line": 265, "column": 4}, "end": {"line": 282, "column": 3}}, "line": 265}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 266, "column": 45}, "end": {"line": 266, "column": 46}}, "loc": {"start": {"line": 266, "column": 59}, "end": {"line": 269, "column": 5}}, "line": 266}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 271, "column": 48}, "end": {"line": 271, "column": 49}}, "loc": {"start": {"line": 271, "column": 62}, "end": {"line": 274, "column": 5}}, "line": 271}, "24": {"name": "(anonymous_24)", "decl": {"start": {"line": 290, "column": 9}, "end": {"line": 290, "column": 10}}, "loc": {"start": {"line": 291, "column": 4}, "end": {"line": 291, "column": 41}}, "line": 291}, "25": {"name": "(anonymous_25)", "decl": {"start": {"line": 292, "column": 8}, "end": {"line": 292, "column": 9}}, "loc": {"start": {"line": 293, "column": 4}, "end": {"line": 293, "column": 40}}, "line": 293}, "26": {"name": "(anonymous_26)", "decl": {"start": {"line": 294, "column": 8}, "end": {"line": 294, "column": 9}}, "loc": {"start": {"line": 295, "column": 4}, "end": {"line": 295, "column": 40}}, "line": 295}, "27": {"name": "(anonymous_27)", "decl": {"start": {"line": 296, "column": 9}, "end": {"line": 296, "column": 10}}, "loc": {"start": {"line": 297, "column": 4}, "end": {"line": 297, "column": 48}}, "line": 297}, "28": {"name": "(anonymous_28)", "decl": {"start": {"line": 298, "column": 9}, "end": {"line": 298, "column": 10}}, "loc": {"start": {"line": 299, "column": 4}, "end": {"line": 299, "column": 48}}, "line": 299}}, "branchMap": {"0": {"loc": {"start": {"line": 42, "column": 12}, "end": {"line": 42, "column": 52}}, "type": "cond-expr", "locations": [{"start": {"line": 42, "column": 22}, "end": {"line": 42, "column": 36}}, {"start": {"line": 42, "column": 39}, "end": {"line": 42, "column": 52}}], "line": 42}, "1": {"loc": {"start": {"line": 63, "column": 14}, "end": {"line": 63, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 63, "column": 46}, "end": {"line": 63, "column": 48}}], "line": 63}, "2": {"loc": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 35}}, "type": "if", "locations": [{"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 35}}, {"start": {}, "end": {}}], "line": 70}, "3": {"loc": {"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": 7}}, "type": "if", "locations": [{"start": {"line": 74, "column": 6}, "end": {"line": 76, "column": 7}}, {"start": {}, "end": {}}], "line": 74}, "4": {"loc": {"start": {"line": 86, "column": 25}, "end": {"line": 86, "column": 41}}, "type": "default-arg", "locations": [{"start": {"line": 86, "column": 36}, "end": {"line": 86, "column": 41}}], "line": 86}, "5": {"loc": {"start": {"line": 90, "column": 24}, "end": {"line": 90, "column": 40}}, "type": "default-arg", "locations": [{"start": {"line": 90, "column": 35}, "end": {"line": 90, "column": 40}}], "line": 90}, "6": {"loc": {"start": {"line": 94, "column": 24}, "end": {"line": 94, "column": 40}}, "type": "default-arg", "locations": [{"start": {"line": 94, "column": 35}, "end": {"line": 94, "column": 40}}], "line": 94}, "7": {"loc": {"start": {"line": 98, "column": 25}, "end": {"line": 98, "column": 41}}, "type": "default-arg", "locations": [{"start": {"line": 98, "column": 36}, "end": {"line": 98, "column": 41}}], "line": 98}, "8": {"loc": {"start": {"line": 103, "column": 25}, "end": {"line": 103, "column": 41}}, "type": "default-arg", "locations": [{"start": {"line": 103, "column": 36}, "end": {"line": 103, "column": 41}}], "line": 103}, "9": {"loc": {"start": {"line": 117, "column": 4}, "end": {"line": 117, "column": 45}}, "type": "if", "locations": [{"start": {"line": 117, "column": 4}, "end": {"line": 117, "column": 45}}, {"start": {}, "end": {}}], "line": 117}, "10": {"loc": {"start": {"line": 135, "column": 4}, "end": {"line": 137, "column": 5}}, "type": "if", "locations": [{"start": {"line": 135, "column": 4}, "end": {"line": 137, "column": 5}}, {"start": {}, "end": {}}], "line": 135}, "11": {"loc": {"start": {"line": 140, "column": 4}, "end": {"line": 142, "column": 5}}, "type": "if", "locations": [{"start": {"line": 140, "column": 4}, "end": {"line": 142, "column": 5}}, {"start": {}, "end": {}}], "line": 140}, "12": {"loc": {"start": {"line": 145, "column": 4}, "end": {"line": 147, "column": 5}}, "type": "if", "locations": [{"start": {"line": 145, "column": 4}, "end": {"line": 147, "column": 5}}, {"start": {}, "end": {}}], "line": 145}, "13": {"loc": {"start": {"line": 150, "column": 4}, "end": {"line": 152, "column": 5}}, "type": "if", "locations": [{"start": {"line": 150, "column": 4}, "end": {"line": 152, "column": 5}}, {"start": {}, "end": {}}], "line": 150}, "14": {"loc": {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 32}}, {"start": {"line": 150, "column": 36}, "end": {"line": 150, "column": 68}}], "line": 150}, "15": {"loc": {"start": {"line": 159, "column": 4}, "end": {"line": 173, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 160, "column": 6}, "end": {"line": 162, "column": 14}}, {"start": {"line": 163, "column": 6}, "end": {"line": 165, "column": 14}}, {"start": {"line": 166, "column": 6}, "end": {"line": 168, "column": 14}}, {"start": {"line": 169, "column": 6}, "end": {"line": 169, "column": 26}}, {"start": {"line": 170, "column": 6}, "end": {"line": 172, "column": 14}}], "line": 159}, "16": {"loc": {"start": {"line": 190, "column": 6}, "end": {"line": 193, "column": 7}}, "type": "if", "locations": [{"start": {"line": 190, "column": 6}, "end": {"line": 193, "column": 7}}, {"start": {}, "end": {}}], "line": 190}, "17": {"loc": {"start": {"line": 210, "column": 47}, "end": {"line": 210, "column": 58}}, "type": "default-arg", "locations": [{"start": {"line": 210, "column": 55}, "end": {"line": 210, "column": 58}}], "line": 210}, "18": {"loc": {"start": {"line": 213, "column": 4}, "end": {"line": 215, "column": 5}}, "type": "if", "locations": [{"start": {"line": 213, "column": 4}, "end": {"line": 215, "column": 5}}, {"start": {}, "end": {}}], "line": 213}, "19": {"loc": {"start": {"line": 217, "column": 4}, "end": {"line": 219, "column": 5}}, "type": "if", "locations": [{"start": {"line": 217, "column": 4}, "end": {"line": 219, "column": 5}}, {"start": {}, "end": {}}], "line": 217}, "20": {"loc": {"start": {"line": 227, "column": 4}, "end": {"line": 229, "column": 5}}, "type": "if", "locations": [{"start": {"line": 227, "column": 4}, "end": {"line": 229, "column": 5}}, {"start": {}, "end": {}}], "line": 227}, "21": {"loc": {"start": {"line": 267, "column": 24}, "end": {"line": 267, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 267, "column": 24}, "end": {"line": 267, "column": 38}}, {"start": {"line": 267, "column": 42}, "end": {"line": 267, "column": 43}}], "line": 267}, "22": {"loc": {"start": {"line": 272, "column": 27}, "end": {"line": 272, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 272, "column": 27}, "end": {"line": 272, "column": 44}}, {"start": {"line": 272, "column": 48}, "end": {"line": 272, "column": 49}}], "line": 272}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0}, "b": {"0": [0, 0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0], "5": [0], "6": [0], "7": [0], "8": [0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0, 0, 0, 0], "16": [0, 0], "17": [0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\PerformanceMonitor.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\PerformanceMonitor.ts", "statementMap": {"0": {"start": {"line": 58, "column": 42}, "end": {"line": 71, "column": 1}}, "1": {"start": {"line": 75, "column": 41}, "end": {"line": 75, "column": 43}}, "2": {"start": {"line": 76, "column": 40}, "end": {"line": 76, "column": 42}}, "3": {"start": {"line": 77, "column": 42}, "end": {"line": 77, "column": 44}}, "4": {"start": {"line": 78, "column": 61}, "end": {"line": 78, "column": 70}}, "5": {"start": {"line": 80, "column": 26}, "end": {"line": 80, "column": 31}}, "6": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 51}}, "7": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 59}}, "8": {"start": {"line": 88, "column": 52}, "end": {"line": 88, "column": 59}}, "9": {"start": {"line": 90, "column": 4}, "end": {"line": 92, "column": 7}}, "10": {"start": {"line": 95, "column": 4}, "end": {"line": 97, "column": 5}}, "11": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 35}}, "12": {"start": {"line": 99, "column": 4}, "end": {"line": 99, "column": 30}}, "13": {"start": {"line": 104, "column": 4}, "end": {"line": 106, "column": 13}}, "14": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 32}}, "15": {"start": {"line": 111, "column": 4}, "end": {"line": 147, "column": 5}}, "16": {"start": {"line": 115, "column": 37}, "end": {"line": 121, "column": 7}}, "17": {"start": {"line": 123, "column": 6}, "end": {"line": 123, "column": 42}}, "18": {"start": {"line": 126, "column": 6}, "end": {"line": 128, "column": 7}}, "19": {"start": {"line": 127, "column": 8}, "end": {"line": 127, "column": 35}}, "20": {"start": {"line": 131, "column": 6}, "end": {"line": 139, "column": 7}}, "21": {"start": {"line": 132, "column": 8}, "end": {"line": 136, "column": 10}}, "22": {"start": {"line": 137, "column": 13}, "end": {"line": 139, "column": 7}}, "23": {"start": {"line": 138, "column": 8}, "end": {"line": 138, "column": 77}}, "24": {"start": {"line": 141, "column": 6}, "end": {"line": 146, "column": 8}}, "25": {"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 40}}, "26": {"start": {"line": 156, "column": 30}, "end": {"line": 156, "column": 40}}, "27": {"start": {"line": 158, "column": 15}, "end": {"line": 158, "column": 38}}, "28": {"start": {"line": 159, "column": 38}, "end": {"line": 166, "column": 5}}, "29": {"start": {"line": 168, "column": 4}, "end": {"line": 168, "column": 42}}, "30": {"start": {"line": 169, "column": 4}, "end": {"line": 169, "column": 14}}, "31": {"start": {"line": 174, "column": 4}, "end": {"line": 174, "column": 59}}, "32": {"start": {"line": 174, "column": 47}, "end": {"line": 174, "column": 59}}, "33": {"start": {"line": 176, "column": 19}, "end": {"line": 176, "column": 49}}, "34": {"start": {"line": 177, "column": 4}, "end": {"line": 177, "column": 39}}, "35": {"start": {"line": 178, "column": 4}, "end": {"line": 178, "column": 56}}, "36": {"start": {"line": 180, "column": 4}, "end": {"line": 180, "column": 37}}, "37": {"start": {"line": 181, "column": 4}, "end": {"line": 181, "column": 27}}, "38": {"start": {"line": 184, "column": 4}, "end": {"line": 190, "column": 5}}, "39": {"start": {"line": 185, "column": 6}, "end": {"line": 189, "column": 9}}, "40": {"start": {"line": 192, "column": 4}, "end": {"line": 192, "column": 18}}, "41": {"start": {"line": 202, "column": 15}, "end": {"line": 202, "column": 57}}, "42": {"start": {"line": 203, "column": 4}, "end": {"line": 210, "column": 5}}, "43": {"start": {"line": 204, "column": 21}, "end": {"line": 204, "column": 31}}, "44": {"start": {"line": 205, "column": 6}, "end": {"line": 205, "column": 25}}, "45": {"start": {"line": 206, "column": 6}, "end": {"line": 206, "column": 20}}, "46": {"start": {"line": 208, "column": 6}, "end": {"line": 208, "column": 25}}, "47": {"start": {"line": 209, "column": 6}, "end": {"line": 209, "column": 18}}, "48": {"start": {"line": 220, "column": 15}, "end": {"line": 220, "column": 57}}, "49": {"start": {"line": 221, "column": 4}, "end": {"line": 228, "column": 5}}, "50": {"start": {"line": 222, "column": 21}, "end": {"line": 222, "column": 25}}, "51": {"start": {"line": 223, "column": 6}, "end": {"line": 223, "column": 25}}, "52": {"start": {"line": 224, "column": 6}, "end": {"line": 224, "column": 20}}, "53": {"start": {"line": 226, "column": 6}, "end": {"line": 226, "column": 25}}, "54": {"start": {"line": 227, "column": 6}, "end": {"line": 227, "column": 18}}, "55": {"start": {"line": 238, "column": 4}, "end": {"line": 238, "column": 76}}, "56": {"start": {"line": 238, "column": 69}, "end": {"line": 238, "column": 76}}, "57": {"start": {"line": 240, "column": 39}, "end": {"line": 246, "column": 5}}, "58": {"start": {"line": 248, "column": 4}, "end": {"line": 248, "column": 42}}, "59": {"start": {"line": 251, "column": 4}, "end": {"line": 253, "column": 5}}, "60": {"start": {"line": 252, "column": 6}, "end": {"line": 252, "column": 33}}, "61": {"start": {"line": 256, "column": 4}, "end": {"line": 263, "column": 5}}, "62": {"start": {"line": 257, "column": 6}, "end": {"line": 262, "column": 9}}, "63": {"start": {"line": 268, "column": 4}, "end": {"line": 268, "column": 30}}, "64": {"start": {"line": 271, "column": 4}, "end": {"line": 273, "column": 5}}, "65": {"start": {"line": 272, "column": 6}, "end": {"line": 272, "column": 27}}, "66": {"start": {"line": 275, "column": 4}, "end": {"line": 279, "column": 7}}, "67": {"start": {"line": 284, "column": 4}, "end": {"line": 284, "column": 50}}, "68": {"start": {"line": 289, "column": 4}, "end": {"line": 289, "column": 77}}, "69": {"start": {"line": 304, "column": 22}, "end": {"line": 306, "column": 28}}, "70": {"start": {"line": 305, "column": 19}, "end": {"line": 305, "column": 43}}, "71": {"start": {"line": 306, "column": 16}, "end": {"line": 306, "column": 27}}, "72": {"start": {"line": 308, "column": 27}, "end": {"line": 310, "column": 12}}, "73": {"start": {"line": 309, "column": 11}, "end": {"line": 309, "column": 51}}, "74": {"start": {"line": 312, "column": 24}, "end": {"line": 314, "column": 12}}, "75": {"start": {"line": 313, "column": 11}, "end": {"line": 313, "column": 59}}, "76": {"start": {"line": 317, "column": 6}, "end": {"line": 319, "column": 11}}, "77": {"start": {"line": 318, "column": 39}, "end": {"line": 318, "column": 46}}, "78": {"start": {"line": 322, "column": 6}, "end": {"line": 325, "column": 11}}, "79": {"start": {"line": 323, "column": 48}, "end": {"line": 323, "column": 66}}, "80": {"start": {"line": 327, "column": 4}, "end": {"line": 337, "column": 6}}, "81": {"start": {"line": 342, "column": 4}, "end": {"line": 342, "column": 61}}, "82": {"start": {"line": 342, "column": 36}, "end": {"line": 342, "column": 59}}, "83": {"start": {"line": 347, "column": 4}, "end": {"line": 347, "column": 38}}, "84": {"start": {"line": 352, "column": 4}, "end": {"line": 352, "column": 35}}, "85": {"start": {"line": 357, "column": 4}, "end": {"line": 357, "column": 35}}, "86": {"start": {"line": 362, "column": 4}, "end": {"line": 362, "column": 22}}, "87": {"start": {"line": 363, "column": 4}, "end": {"line": 363, "column": 28}}, "88": {"start": {"line": 364, "column": 4}, "end": {"line": 364, "column": 28}}, "89": {"start": {"line": 365, "column": 4}, "end": {"line": 365, "column": 34}}, "90": {"start": {"line": 366, "column": 4}, "end": {"line": 366, "column": 62}}, "91": {"start": {"line": 371, "column": 4}, "end": {"line": 371, "column": 51}}, "92": {"start": {"line": 372, "column": 4}, "end": {"line": 376, "column": 6}}, "93": {"start": {"line": 381, "column": 4}, "end": {"line": 383, "column": 5}}, "94": {"start": {"line": 382, "column": 6}, "end": {"line": 382, "column": 48}}, "95": {"start": {"line": 384, "column": 4}, "end": {"line": 384, "column": 24}}, "96": {"start": {"line": 385, "column": 4}, "end": {"line": 385, "column": 31}}, "97": {"start": {"line": 386, "column": 4}, "end": {"line": 386, "column": 67}}, "98": {"start": {"line": 391, "column": 34}, "end": {"line": 391, "column": 65}}, "99": {"start": {"line": 394, "column": 27}, "end": {"line": 398, "column": 61}}, "100": {"start": {"line": 398, "column": 5}, "end": {"line": 398, "column": 61}}, "101": {"start": {"line": 400, "column": 25}, "end": {"line": 400, "column": 73}}, "102": {"start": {"line": 400, "column": 41}, "end": {"line": 400, "column": 73}}, "103": {"start": {"line": 402, "column": 28}, "end": {"line": 407, "column": 66}}, "104": {"start": {"line": 407, "column": 5}, "end": {"line": 407, "column": 66}}, "105": {"start": {"line": 409, "column": 23}, "end": {"line": 414, "column": 61}}, "106": {"start": {"line": 414, "column": 5}, "end": {"line": 414, "column": 61}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": 3}}, "loc": {"start": {"line": 82, "column": 55}, "end": {"line": 84, "column": 3}}, "line": 82}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 3}}, "loc": {"start": {"line": 87, "column": 21}, "end": {"line": 100, "column": 3}}, "line": 87}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 3}}, "loc": {"start": {"line": 103, "column": 40}, "end": {"line": 107, "column": 3}}, "line": 103}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 104, "column": 45}, "end": {"line": 104, "column": 46}}, "loc": {"start": {"line": 104, "column": 51}, "end": {"line": 106, "column": 5}}, "line": 104}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 110, "column": 2}, "end": {"line": 110, "column": 3}}, "loc": {"start": {"line": 110, "column": 37}, "end": {"line": 148, "column": 3}}, "line": 110}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 151, "column": 2}, "end": {"line": 151, "column": 3}}, "loc": {"start": {"line": 155, "column": 12}, "end": {"line": 170, "column": 3}}, "line": 155}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 173, "column": 2}, "end": {"line": 173, "column": 3}}, "loc": {"start": {"line": 173, "column": 50}, "end": {"line": 193, "column": 3}}, "line": 173}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 196, "column": 2}, "end": {"line": 196, "column": 3}}, "loc": {"start": {"line": 201, "column": 16}, "end": {"line": 211, "column": 3}}, "line": 201}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 214, "column": 2}, "end": {"line": 214, "column": 3}}, "loc": {"start": {"line": 219, "column": 7}, "end": {"line": 229, "column": 3}}, "line": 219}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 232, "column": 2}, "end": {"line": 232, "column": 3}}, "loc": {"start": {"line": 237, "column": 10}, "end": {"line": 264, "column": 3}}, "line": 237}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 267, "column": 2}, "end": {"line": 267, "column": 3}}, "loc": {"start": {"line": 267, "column": 53}, "end": {"line": 280, "column": 3}}, "line": 267}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 283, "column": 2}, "end": {"line": 283, "column": 3}}, "loc": {"start": {"line": 283, "column": 34}, "end": {"line": 285, "column": 3}}, "line": 283}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 288, "column": 2}, "end": {"line": 288, "column": 3}}, "loc": {"start": {"line": 288, "column": 37}, "end": {"line": 290, "column": 3}}, "line": 288}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 293, "column": 2}, "end": {"line": 293, "column": 3}}, "loc": {"start": {"line": 303, "column": 4}, "end": {"line": 338, "column": 3}}, "line": 303}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 305, "column": 14}, "end": {"line": 305, "column": 15}}, "loc": {"start": {"line": 305, "column": 19}, "end": {"line": 305, "column": 43}}, "line": 305}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 306, "column": 11}, "end": {"line": 306, "column": 12}}, "loc": {"start": {"line": 306, "column": 16}, "end": {"line": 306, "column": 27}}, "line": 306}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 309, "column": 6}, "end": {"line": 309, "column": 7}}, "loc": {"start": {"line": 309, "column": 11}, "end": {"line": 309, "column": 51}}, "line": 309}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 313, "column": 6}, "end": {"line": 313, "column": 7}}, "loc": {"start": {"line": 313, "column": 11}, "end": {"line": 313, "column": 59}}, "line": 313}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 318, "column": 27}, "end": {"line": 318, "column": 28}}, "loc": {"start": {"line": 318, "column": 39}, "end": {"line": 318, "column": 46}}, "line": 318}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 323, "column": 36}, "end": {"line": 323, "column": 37}}, "loc": {"start": {"line": 323, "column": 48}, "end": {"line": 323, "column": 66}}, "line": 323}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 341, "column": 2}, "end": {"line": 341, "column": 3}}, "loc": {"start": {"line": 341, "column": 62}, "end": {"line": 343, "column": 3}}, "line": 341}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 342, "column": 31}, "end": {"line": 342, "column": 32}}, "loc": {"start": {"line": 342, "column": 36}, "end": {"line": 342, "column": 59}}, "line": 342}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 346, "column": 2}, "end": {"line": 346, "column": 3}}, "loc": {"start": {"line": 346, "column": 52}, "end": {"line": 348, "column": 3}}, "line": 346}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 351, "column": 2}, "end": {"line": 351, "column": 3}}, "loc": {"start": {"line": 351, "column": 35}, "end": {"line": 353, "column": 3}}, "line": 351}, "24": {"name": "(anonymous_24)", "decl": {"start": {"line": 356, "column": 2}, "end": {"line": 356, "column": 3}}, "loc": {"start": {"line": 356, "column": 37}, "end": {"line": 358, "column": 3}}, "line": 356}, "25": {"name": "(anonymous_25)", "decl": {"start": {"line": 361, "column": 2}, "end": {"line": 361, "column": 3}}, "loc": {"start": {"line": 361, "column": 23}, "end": {"line": 367, "column": 3}}, "line": 361}, "26": {"name": "(anonymous_26)", "decl": {"start": {"line": 370, "column": 2}, "end": {"line": 370, "column": 3}}, "loc": {"start": {"line": 370, "column": 60}, "end": {"line": 377, "column": 3}}, "line": 370}, "27": {"name": "(anonymous_27)", "decl": {"start": {"line": 380, "column": 2}, "end": {"line": 380, "column": 3}}, "loc": {"start": {"line": 380, "column": 18}, "end": {"line": 387, "column": 3}}, "line": 380}, "28": {"name": "(anonymous_28)", "decl": {"start": {"line": 394, "column": 27}, "end": {"line": 394, "column": 28}}, "loc": {"start": {"line": 398, "column": 5}, "end": {"line": 398, "column": 61}}, "line": 398}, "29": {"name": "(anonymous_29)", "decl": {"start": {"line": 400, "column": 25}, "end": {"line": 400, "column": 26}}, "loc": {"start": {"line": 400, "column": 41}, "end": {"line": 400, "column": 73}}, "line": 400}, "30": {"name": "(anonymous_30)", "decl": {"start": {"line": 402, "column": 28}, "end": {"line": 402, "column": 29}}, "loc": {"start": {"line": 407, "column": 5}, "end": {"line": 407, "column": 66}}, "line": 407}, "31": {"name": "(anonymous_31)", "decl": {"start": {"line": 409, "column": 23}, "end": {"line": 409, "column": 24}}, "loc": {"start": {"line": 414, "column": 5}, "end": {"line": 414, "column": 61}}, "line": 414}}, "branchMap": {"0": {"loc": {"start": {"line": 63, "column": 14}, "end": {"line": 63, "column": 33}}, "type": "cond-expr", "locations": [{"start": {"line": 63, "column": 24}, "end": {"line": 63, "column": 27}}, {"start": {"line": 63, "column": 30}, "end": {"line": 63, "column": 33}}], "line": 63}, "1": {"loc": {"start": {"line": 82, "column": 14}, "end": {"line": 82, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 82, "column": 51}, "end": {"line": 82, "column": 53}}], "line": 82}, "2": {"loc": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 59}}, "type": "if", "locations": [{"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 59}}, {"start": {}, "end": {}}], "line": 88}, "3": {"loc": {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 26}}, {"start": {"line": 88, "column": 30}, "end": {"line": 88, "column": 50}}], "line": 88}, "4": {"loc": {"start": {"line": 95, "column": 4}, "end": {"line": 97, "column": 5}}, "type": "if", "locations": [{"start": {"line": 95, "column": 4}, "end": {"line": 97, "column": 5}}, {"start": {}, "end": {}}], "line": 95}, "5": {"loc": {"start": {"line": 126, "column": 6}, "end": {"line": 128, "column": 7}}, "type": "if", "locations": [{"start": {"line": 126, "column": 6}, "end": {"line": 128, "column": 7}}, {"start": {}, "end": {}}], "line": 126}, "6": {"loc": {"start": {"line": 131, "column": 6}, "end": {"line": 139, "column": 7}}, "type": "if", "locations": [{"start": {"line": 131, "column": 6}, "end": {"line": 139, "column": 7}}, {"start": {"line": 137, "column": 13}, "end": {"line": 139, "column": 7}}], "line": 131}, "7": {"loc": {"start": {"line": 137, "column": 13}, "end": {"line": 139, "column": 7}}, "type": "if", "locations": [{"start": {"line": 137, "column": 13}, "end": {"line": 139, "column": 7}}, {"start": {}, "end": {}}], "line": 137}, "8": {"loc": {"start": {"line": 153, "column": 4}, "end": {"line": 153, "column": 26}}, "type": "default-arg", "locations": [{"start": {"line": 153, "column": 15}, "end": {"line": 153, "column": 26}}], "line": 153}, "9": {"loc": {"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 40}}, "type": "if", "locations": [{"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 40}}, {"start": {}, "end": {}}], "line": 156}, "10": {"loc": {"start": {"line": 174, "column": 4}, "end": {"line": 174, "column": 59}}, "type": "if", "locations": [{"start": {"line": 174, "column": 4}, "end": {"line": 174, "column": 59}}, {"start": {}, "end": {}}], "line": 174}, "11": {"loc": {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 11}}, {"start": {"line": 174, "column": 15}, "end": {"line": 174, "column": 45}}], "line": 174}, "12": {"loc": {"start": {"line": 184, "column": 4}, "end": {"line": 190, "column": 5}}, "type": "if", "locations": [{"start": {"line": 184, "column": 4}, "end": {"line": 190, "column": 5}}, {"start": {}, "end": {}}], "line": 184}, "13": {"loc": {"start": {"line": 199, "column": 4}, "end": {"line": 199, "column": 32}}, "type": "default-arg", "locations": [{"start": {"line": 199, "column": 15}, "end": {"line": 199, "column": 32}}], "line": 199}, "14": {"loc": {"start": {"line": 217, "column": 4}, "end": {"line": 217, "column": 31}}, "type": "default-arg", "locations": [{"start": {"line": 217, "column": 15}, "end": {"line": 217, "column": 31}}], "line": 217}, "15": {"loc": {"start": {"line": 235, "column": 4}, "end": {"line": 235, "column": 18}}, "type": "default-arg", "locations": [{"start": {"line": 235, "column": 17}, "end": {"line": 235, "column": 18}}], "line": 235}, "16": {"loc": {"start": {"line": 236, "column": 4}, "end": {"line": 236, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 236, "column": 20}, "end": {"line": 236, "column": 21}}], "line": 236}, "17": {"loc": {"start": {"line": 238, "column": 4}, "end": {"line": 238, "column": 76}}, "type": "if", "locations": [{"start": {"line": 238, "column": 4}, "end": {"line": 238, "column": 76}}, {"start": {}, "end": {}}], "line": 238}, "18": {"loc": {"start": {"line": 238, "column": 8}, "end": {"line": 238, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 238, "column": 8}, "end": {"line": 238, "column": 43}}, {"start": {"line": 238, "column": 47}, "end": {"line": 238, "column": 67}}], "line": 238}, "19": {"loc": {"start": {"line": 251, "column": 4}, "end": {"line": 253, "column": 5}}, "type": "if", "locations": [{"start": {"line": 251, "column": 4}, "end": {"line": 253, "column": 5}}, {"start": {}, "end": {}}], "line": 251}, "20": {"loc": {"start": {"line": 256, "column": 4}, "end": {"line": 263, "column": 5}}, "type": "if", "locations": [{"start": {"line": 256, "column": 4}, "end": {"line": 263, "column": 5}}, {"start": {}, "end": {}}], "line": 256}, "21": {"loc": {"start": {"line": 271, "column": 4}, "end": {"line": 273, "column": 5}}, "type": "if", "locations": [{"start": {"line": 271, "column": 4}, "end": {"line": 273, "column": 5}}, {"start": {}, "end": {}}], "line": 271}, "22": {"loc": {"start": {"line": 317, "column": 6}, "end": {"line": 319, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 318, "column": 10}, "end": {"line": 318, "column": 69}}, {"start": {"line": 319, "column": 10}, "end": {"line": 319, "column": 11}}], "line": 317}, "23": {"loc": {"start": {"line": 322, "column": 6}, "end": {"line": 325, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 323, "column": 10}, "end": {"line": 324, "column": 35}}, {"start": {"line": 325, "column": 10}, "end": {"line": 325, "column": 11}}], "line": 322}, "24": {"loc": {"start": {"line": 331, "column": 19}, "end": {"line": 331, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 331, "column": 19}, "end": {"line": 331, "column": 68}}, {"start": {"line": 331, "column": 72}, "end": {"line": 331, "column": 76}}], "line": 331}, "25": {"loc": {"start": {"line": 346, "column": 19}, "end": {"line": 346, "column": 29}}, "type": "default-arg", "locations": [{"start": {"line": 346, "column": 27}, "end": {"line": 346, "column": 29}}], "line": 346}, "26": {"loc": {"start": {"line": 381, "column": 4}, "end": {"line": 383, "column": 5}}, "type": "if", "locations": [{"start": {"line": 381, "column": 4}, "end": {"line": 383, "column": 5}}, {"start": {}, "end": {}}], "line": 381}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0}, "b": {"0": [0, 0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0], "14": [0], "15": [0], "16": [0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0], "26": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\index.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\index.ts", "statementMap": {"0": {"start": {"line": 11, "column": 34}, "end": {"line": 29, "column": 1}}, "1": {"start": {"line": 12, "column": 2}, "end": {"line": 28, "column": 3}}, "2": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 30}}, "3": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 58}}, "4": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 36}}, "5": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 61}}, "6": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 52}}, "7": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": 64}}, "8": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 59}}, "9": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 16}}, "10": {"start": {"line": 32, "column": 31}, "end": {"line": 43, "column": 1}}, "11": {"start": {"line": 33, "column": 2}, "end": {"line": 42, "column": 3}}, "12": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 51}}, "13": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 33}}, "14": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 59}}, "15": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 53}}, "16": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 56}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 11, "column": 34}, "end": {"line": 11, "column": 35}}, "loc": {"start": {"line": 11, "column": 61}, "end": {"line": 29, "column": 1}}, "line": 11}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 32, "column": 31}, "end": {"line": 32, "column": 32}}, "loc": {"start": {"line": 32, "column": 43}, "end": {"line": 43, "column": 1}}, "line": 32}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\annotation\\AnnotationService.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\annotation\\AnnotationService.ts", "statementMap": {"0": {"start": {"line": 25, "column": 48}, "end": {"line": 34, "column": 1}}, "1": {"start": {"line": 38, "column": 49}, "end": {"line": 38, "column": 58}}, "2": {"start": {"line": 39, "column": 55}, "end": {"line": 39, "column": 64}}, "3": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 15}}, "4": {"start": {"line": 45, "column": 26}, "end": {"line": 45, "column": 31}}, "5": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 51}}, "6": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 35}}, "7": {"start": {"line": 53, "column": 28}, "end": {"line": 53, "column": 35}}, "8": {"start": {"line": 55, "column": 4}, "end": {"line": 72, "column": 5}}, "9": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 46}}, "10": {"start": {"line": 58, "column": 6}, "end": {"line": 60, "column": 7}}, "11": {"start": {"line": 59, "column": 8}, "end": {"line": 59, "column": 29}}, "12": {"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": 32}}, "13": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 66}}, "14": {"start": {"line": 65, "column": 6}, "end": {"line": 70, "column": 8}}, "15": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 18}}, "16": {"start": {"line": 79, "column": 4}, "end": {"line": 127, "column": 5}}, "17": {"start": {"line": 80, "column": 37}, "end": {"line": 85, "column": 21}}, "18": {"start": {"line": 88, "column": 25}, "end": {"line": 88, "column": 46}}, "19": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 61}}, "20": {"start": {"line": 91, "column": 6}, "end": {"line": 95, "column": 7}}, "21": {"start": {"line": 92, "column": 8}, "end": {"line": 94, "column": 10}}, "22": {"start": {"line": 98, "column": 6}, "end": {"line": 98, "column": 54}}, "23": {"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 54}}, "24": {"start": {"line": 103, "column": 6}, "end": {"line": 103, "column": 48}}, "25": {"start": {"line": 104, "column": 6}, "end": {"line": 104, "column": 70}}, "26": {"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 44}}, "27": {"start": {"line": 110, "column": 6}, "end": {"line": 110, "column": 62}}, "28": {"start": {"line": 112, "column": 6}, "end": {"line": 116, "column": 9}}, "29": {"start": {"line": 118, "column": 6}, "end": {"line": 118, "column": 24}}, "30": {"start": {"line": 120, "column": 6}, "end": {"line": 125, "column": 8}}, "31": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 18}}, "32": {"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 44}}, "33": {"start": {"line": 138, "column": 4}, "end": {"line": 168, "column": 5}}, "34": {"start": {"line": 139, "column": 33}, "end": {"line": 139, "column": 57}}, "35": {"start": {"line": 140, "column": 6}, "end": {"line": 142, "column": 7}}, "36": {"start": {"line": 141, "column": 8}, "end": {"line": 141, "column": 62}}, "37": {"start": {"line": 144, "column": 32}, "end": {"line": 149, "column": 21}}, "38": {"start": {"line": 151, "column": 6}, "end": {"line": 151, "column": 50}}, "39": {"start": {"line": 152, "column": 6}, "end": {"line": 152, "column": 44}}, "40": {"start": {"line": 155, "column": 6}, "end": {"line": 155, "column": 69}}, "41": {"start": {"line": 157, "column": 6}, "end": {"line": 157, "column": 71}}, "42": {"start": {"line": 159, "column": 6}, "end": {"line": 159, "column": 31}}, "43": {"start": {"line": 161, "column": 6}, "end": {"line": 166, "column": 8}}, "44": {"start": {"line": 167, "column": 6}, "end": {"line": 167, "column": 18}}, "45": {"start": {"line": 172, "column": 4}, "end": {"line": 206, "column": 5}}, "46": {"start": {"line": 173, "column": 25}, "end": {"line": 173, "column": 49}}, "47": {"start": {"line": 174, "column": 6}, "end": {"line": 176, "column": 7}}, "48": {"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 21}}, "49": {"start": {"line": 179, "column": 6}, "end": {"line": 179, "column": 34}}, "50": {"start": {"line": 182, "column": 25}, "end": {"line": 182, "column": 46}}, "51": {"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 54}}, "52": {"start": {"line": 185, "column": 25}, "end": {"line": 187, "column": 7}}, "53": {"start": {"line": 186, "column": 24}, "end": {"line": 186, "column": 43}}, "54": {"start": {"line": 188, "column": 6}, "end": {"line": 188, "column": 59}}, "55": {"start": {"line": 190, "column": 6}, "end": {"line": 190, "column": 44}}, "56": {"start": {"line": 193, "column": 6}, "end": {"line": 193, "column": 62}}, "57": {"start": {"line": 195, "column": 6}, "end": {"line": 195, "column": 74}}, "58": {"start": {"line": 197, "column": 6}, "end": {"line": 197, "column": 18}}, "59": {"start": {"line": 199, "column": 6}, "end": {"line": 204, "column": 8}}, "60": {"start": {"line": 205, "column": 6}, "end": {"line": 205, "column": 18}}, "61": {"start": {"line": 211, "column": 4}, "end": {"line": 237, "column": 5}}, "62": {"start": {"line": 212, "column": 28}, "end": {"line": 212, "column": 74}}, "63": {"start": {"line": 213, "column": 26}, "end": {"line": 221, "column": 9}}, "64": {"start": {"line": 214, "column": 19}, "end": {"line": 214, "column": 43}}, "65": {"start": {"line": 216, "column": 52}, "end": {"line": 216, "column": 76}}, "66": {"start": {"line": 220, "column": 12}, "end": {"line": 220, "column": 77}}, "67": {"start": {"line": 223, "column": 6}, "end": {"line": 226, "column": 9}}, "68": {"start": {"line": 228, "column": 6}, "end": {"line": 228, "column": 25}}, "69": {"start": {"line": 230, "column": 6}, "end": {"line": 235, "column": 8}}, "70": {"start": {"line": 236, "column": 6}, "end": {"line": 236, "column": 18}}, "71": {"start": {"line": 243, "column": 4}, "end": {"line": 305, "column": 5}}, "72": {"start": {"line": 244, "column": 20}, "end": {"line": 244, "column": 57}}, "73": {"start": {"line": 247, "column": 6}, "end": {"line": 251, "column": 7}}, "74": {"start": {"line": 248, "column": 8}, "end": {"line": 250, "column": 10}}, "75": {"start": {"line": 249, "column": 24}, "end": {"line": 249, "column": 69}}, "76": {"start": {"line": 253, "column": 6}, "end": {"line": 257, "column": 7}}, "77": {"start": {"line": 254, "column": 8}, "end": {"line": 256, "column": 10}}, "78": {"start": {"line": 255, "column": 24}, "end": {"line": 255, "column": 57}}, "79": {"start": {"line": 259, "column": 6}, "end": {"line": 269, "column": 7}}, "80": {"start": {"line": 260, "column": 22}, "end": {"line": 260, "column": 54}}, "81": {"start": {"line": 261, "column": 8}, "end": {"line": 268, "column": 11}}, "82": {"start": {"line": 262, "column": 29}, "end": {"line": 266, "column": 26}}, "83": {"start": {"line": 267, "column": 10}, "end": {"line": 267, "column": 44}}, "84": {"start": {"line": 271, "column": 6}, "end": {"line": 278, "column": 7}}, "85": {"start": {"line": 272, "column": 26}, "end": {"line": 272, "column": 60}}, "86": {"start": {"line": 273, "column": 24}, "end": {"line": 273, "column": 56}}, "87": {"start": {"line": 274, "column": 8}, "end": {"line": 277, "column": 11}}, "88": {"start": {"line": 275, "column": 33}, "end": {"line": 275, "column": 63}}, "89": {"start": {"line": 276, "column": 10}, "end": {"line": 276, "column": 74}}, "90": {"start": {"line": 281, "column": 6}, "end": {"line": 284, "column": 8}}, "91": {"start": {"line": 283, "column": 10}, "end": {"line": 283, "column": 75}}, "92": {"start": {"line": 287, "column": 21}, "end": {"line": 287, "column": 41}}, "93": {"start": {"line": 288, "column": 20}, "end": {"line": 288, "column": 40}}, "94": {"start": {"line": 289, "column": 25}, "end": {"line": 289, "column": 39}}, "95": {"start": {"line": 290, "column": 31}, "end": {"line": 290, "column": 68}}, "96": {"start": {"line": 292, "column": 6}, "end": {"line": 296, "column": 8}}, "97": {"start": {"line": 298, "column": 6}, "end": {"line": 303, "column": 8}}, "98": {"start": {"line": 304, "column": 6}, "end": {"line": 304, "column": 18}}, "99": {"start": {"line": 311, "column": 4}, "end": {"line": 359, "column": 5}}, "100": {"start": {"line": 312, "column": 26}, "end": {"line": 314, "column": 47}}, "101": {"start": {"line": 316, "column": 21}, "end": {"line": 319, "column": 46}}, "102": {"start": {"line": 317, "column": 8}, "end": {"line": 317, "column": 63}}, "103": {"start": {"line": 318, "column": 8}, "end": {"line": 318, "column": 19}}, "104": {"start": {"line": 321, "column": 25}, "end": {"line": 324, "column": 38}}, "105": {"start": {"line": 322, "column": 8}, "end": {"line": 322, "column": 75}}, "106": {"start": {"line": 323, "column": 8}, "end": {"line": 323, "column": 19}}, "107": {"start": {"line": 326, "column": 20}, "end": {"line": 326, "column": 67}}, "108": {"start": {"line": 326, "column": 41}, "end": {"line": 326, "column": 66}}, "109": {"start": {"line": 327, "column": 21}, "end": {"line": 330, "column": 38}}, "110": {"start": {"line": 328, "column": 8}, "end": {"line": 328, "column": 41}}, "111": {"start": {"line": 329, "column": 8}, "end": {"line": 329, "column": 19}}, "112": {"start": {"line": 333, "column": 8}, "end": {"line": 333, "column": 78}}, "113": {"start": {"line": 333, "column": 58}, "end": {"line": 333, "column": 63}}, "114": {"start": {"line": 335, "column": 26}, "end": {"line": 335, "column": 66}}, "115": {"start": {"line": 335, "column": 47}, "end": {"line": 335, "column": 58}}, "116": {"start": {"line": 337, "column": 6}, "end": {"line": 350, "column": 8}}, "117": {"start": {"line": 352, "column": 6}, "end": {"line": 357, "column": 8}}, "118": {"start": {"line": 358, "column": 6}, "end": {"line": 358, "column": 18}}, "119": {"start": {"line": 367, "column": 4}, "end": {"line": 388, "column": 5}}, "120": {"start": {"line": 368, "column": 26}, "end": {"line": 368, "column": 74}}, "121": {"start": {"line": 371, "column": 25}, "end": {"line": 371, "column": 61}}, "122": {"start": {"line": 373, "column": 6}, "end": {"line": 377, "column": 9}}, "123": {"start": {"line": 379, "column": 6}, "end": {"line": 379, "column": 24}}, "124": {"start": {"line": 381, "column": 6}, "end": {"line": 386, "column": 8}}, "125": {"start": {"line": 387, "column": 6}, "end": {"line": 387, "column": 18}}, "126": {"start": {"line": 395, "column": 4}, "end": {"line": 415, "column": 5}}, "127": {"start": {"line": 397, "column": 45}, "end": {"line": 402, "column": 7}}, "128": {"start": {"line": 404, "column": 6}, "end": {"line": 404, "column": 76}}, "129": {"start": {"line": 406, "column": 6}, "end": {"line": 406, "column": 20}}, "130": {"start": {"line": 408, "column": 6}, "end": {"line": 413, "column": 8}}, "131": {"start": {"line": 414, "column": 6}, "end": {"line": 414, "column": 18}}, "132": {"start": {"line": 421, "column": 4}, "end": {"line": 421, "column": 73}}, "133": {"start": {"line": 422, "column": 4}, "end": {"line": 422, "column": 64}}, "134": {"start": {"line": 430, "column": 4}, "end": {"line": 433, "column": 7}}, "135": {"start": {"line": 434, "column": 4}, "end": {"line": 434, "column": 16}}, "136": {"start": {"line": 442, "column": 4}, "end": {"line": 444, "column": 5}}, "137": {"start": {"line": 443, "column": 6}, "end": {"line": 443, "column": 40}}, "138": {"start": {"line": 445, "column": 4}, "end": {"line": 445, "column": 50}}, "139": {"start": {"line": 452, "column": 22}, "end": {"line": 452, "column": 51}}, "140": {"start": {"line": 453, "column": 4}, "end": {"line": 458, "column": 5}}, "141": {"start": {"line": 454, "column": 20}, "end": {"line": 454, "column": 47}}, "142": {"start": {"line": 455, "column": 6}, "end": {"line": 457, "column": 7}}, "143": {"start": {"line": 456, "column": 8}, "end": {"line": 456, "column": 35}}, "144": {"start": {"line": 463, "column": 35}, "end": {"line": 467, "column": 5}}, "145": {"start": {"line": 469, "column": 22}, "end": {"line": 469, "column": 57}}, "146": {"start": {"line": 470, "column": 4}, "end": {"line": 479, "column": 7}}, "147": {"start": {"line": 471, "column": 6}, "end": {"line": 478, "column": 7}}, "148": {"start": {"line": 472, "column": 8}, "end": {"line": 472, "column": 24}}, "149": {"start": {"line": 474, "column": 8}, "end": {"line": 477, "column": 11}}, "150": {"start": {"line": 483, "column": 4}, "end": {"line": 485, "column": 22}}, "151": {"start": {"line": 489, "column": 4}, "end": {"line": 515, "column": 5}}, "152": {"start": {"line": 490, "column": 19}, "end": {"line": 490, "column": 69}}, "153": {"start": {"line": 491, "column": 6}, "end": {"line": 507, "column": 7}}, "154": {"start": {"line": 492, "column": 52}, "end": {"line": 492, "column": 68}}, "155": {"start": {"line": 494, "column": 8}, "end": {"line": 501, "column": 11}}, "156": {"start": {"line": 495, "column": 42}, "end": {"line": 495, "column": 44}}, "157": {"start": {"line": 496, "column": 10}, "end": {"line": 499, "column": 13}}, "158": {"start": {"line": 497, "column": 12}, "end": {"line": 497, "column": 60}}, "159": {"start": {"line": 498, "column": 12}, "end": {"line": 498, "column": 46}}, "160": {"start": {"line": 500, "column": 10}, "end": {"line": 500, "column": 77}}, "161": {"start": {"line": 503, "column": 8}, "end": {"line": 506, "column": 11}}, "162": {"start": {"line": 509, "column": 6}, "end": {"line": 514, "column": 8}}, "163": {"start": {"line": 519, "column": 4}, "end": {"line": 558, "column": 5}}, "164": {"start": {"line": 520, "column": 50}, "end": {"line": 520, "column": 52}}, "165": {"start": {"line": 522, "column": 6}, "end": {"line": 540, "column": 7}}, "166": {"start": {"line": 526, "column": 28}, "end": {"line": 530, "column": 11}}, "167": {"start": {"line": 527, "column": 21}, "end": {"line": 527, "column": 45}}, "168": {"start": {"line": 529, "column": 54}, "end": {"line": 529, "column": 78}}, "169": {"start": {"line": 532, "column": 8}, "end": {"line": 539, "column": 9}}, "170": {"start": {"line": 533, "column": 10}, "end": {"line": 538, "column": 13}}, "171": {"start": {"line": 542, "column": 6}, "end": {"line": 545, "column": 8}}, "172": {"start": {"line": 547, "column": 6}, "end": {"line": 550, "column": 9}}, "173": {"start": {"line": 552, "column": 6}, "end": {"line": 557, "column": 8}}, "174": {"start": {"line": 562, "column": 4}, "end": {"line": 564, "column": 5}}, "175": {"start": {"line": 563, "column": 6}, "end": {"line": 563, "column": 40}}, "176": {"start": {"line": 566, "column": 4}, "end": {"line": 570, "column": 37}}, "177": {"start": {"line": 567, "column": 6}, "end": {"line": 569, "column": 9}}, "178": {"start": {"line": 568, "column": 8}, "end": {"line": 568, "column": 66}}, "179": {"start": {"line": 575, "column": 4}, "end": {"line": 577, "column": 5}}, "180": {"start": {"line": 576, "column": 6}, "end": {"line": 576, "column": 40}}, "181": {"start": {"line": 578, "column": 4}, "end": {"line": 578, "column": 32}}, "182": {"start": {"line": 579, "column": 4}, "end": {"line": 579, "column": 31}}, "183": {"start": {"line": 580, "column": 4}, "end": {"line": 580, "column": 62}}, "184": {"start": {"line": 585, "column": 33}, "end": {"line": 585, "column": 56}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 3}}, "loc": {"start": {"line": 47, "column": 61}, "end": {"line": 49, "column": 3}}, "line": 47}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": 3}}, "loc": {"start": {"line": 52, "column": 36}, "end": {"line": 73, "column": 3}}, "line": 52}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 76, "column": 2}, "end": {"line": 76, "column": 3}}, "loc": {"start": {"line": 78, "column": 25}, "end": {"line": 128, "column": 3}}, "line": 78}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 130, "column": 2}, "end": {"line": 130, "column": 3}}, "loc": {"start": {"line": 130, "column": 62}, "end": {"line": 132, "column": 3}}, "line": 130}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 134, "column": 2}, "end": {"line": 134, "column": 3}}, "loc": {"start": {"line": 137, "column": 25}, "end": {"line": 169, "column": 3}}, "line": 137}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 171, "column": 2}, "end": {"line": 171, "column": 3}}, "loc": {"start": {"line": 171, "column": 55}, "end": {"line": 207, "column": 3}}, "line": 171}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 186, "column": 8}, "end": {"line": 186, "column": 9}}, "loc": {"start": {"line": 186, "column": 24}, "end": {"line": 186, "column": 43}}, "line": 186}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 210, "column": 2}, "end": {"line": 210, "column": 3}}, "loc": {"start": {"line": 210, "column": 77}, "end": {"line": 238, "column": 3}}, "line": 210}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 214, "column": 13}, "end": {"line": 214, "column": 14}}, "loc": {"start": {"line": 214, "column": 19}, "end": {"line": 214, "column": 43}}, "line": 214}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 216, "column": 10}, "end": {"line": 216, "column": 11}}, "loc": {"start": {"line": 216, "column": 52}, "end": {"line": 216, "column": 76}}, "line": 216}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 219, "column": 10}, "end": {"line": 219, "column": 11}}, "loc": {"start": {"line": 220, "column": 12}, "end": {"line": 220, "column": 77}}, "line": 220}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 240, "column": 2}, "end": {"line": 240, "column": 3}}, "loc": {"start": {"line": 242, "column": 37}, "end": {"line": 306, "column": 3}}, "line": 242}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 249, "column": 10}, "end": {"line": 249, "column": 11}}, "loc": {"start": {"line": 249, "column": 24}, "end": {"line": 249, "column": 69}}, "line": 249}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 255, "column": 10}, "end": {"line": 255, "column": 11}}, "loc": {"start": {"line": 255, "column": 24}, "end": {"line": 255, "column": 57}}, "line": 255}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 261, "column": 33}, "end": {"line": 261, "column": 34}}, "loc": {"start": {"line": 261, "column": 47}, "end": {"line": 268, "column": 9}}, "line": 261}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 274, "column": 33}, "end": {"line": 274, "column": 34}}, "loc": {"start": {"line": 274, "column": 47}, "end": {"line": 277, "column": 9}}, "line": 274}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 282, "column": 8}, "end": {"line": 282, "column": 9}}, "loc": {"start": {"line": 283, "column": 10}, "end": {"line": 283, "column": 75}}, "line": 283}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 308, "column": 2}, "end": {"line": 308, "column": 3}}, "loc": {"start": {"line": 310, "column": 35}, "end": {"line": 360, "column": 3}}, "line": 310}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 316, "column": 40}, "end": {"line": 316, "column": 41}}, "loc": {"start": {"line": 316, "column": 61}, "end": {"line": 319, "column": 7}}, "line": 316}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 321, "column": 44}, "end": {"line": 321, "column": 45}}, "loc": {"start": {"line": 321, "column": 65}, "end": {"line": 324, "column": 7}}, "line": 321}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 326, "column": 36}, "end": {"line": 326, "column": 37}}, "loc": {"start": {"line": 326, "column": 41}, "end": {"line": 326, "column": 66}}, "line": 326}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 327, "column": 34}, "end": {"line": 327, "column": 35}}, "loc": {"start": {"line": 327, "column": 49}, "end": {"line": 330, "column": 7}}, "line": 327}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 333, "column": 40}, "end": {"line": 333, "column": 41}}, "loc": {"start": {"line": 333, "column": 58}, "end": {"line": 333, "column": 63}}, "line": 333}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 335, "column": 42}, "end": {"line": 335, "column": 43}}, "loc": {"start": {"line": 335, "column": 47}, "end": {"line": 335, "column": 58}}, "line": 335}, "24": {"name": "(anonymous_24)", "decl": {"start": {"line": 363, "column": 2}, "end": {"line": 363, "column": 3}}, "loc": {"start": {"line": 366, "column": 21}, "end": {"line": 389, "column": 3}}, "line": 366}, "25": {"name": "(anonymous_25)", "decl": {"start": {"line": 391, "column": 2}, "end": {"line": 391, "column": 3}}, "loc": {"start": {"line": 394, "column": 37}, "end": {"line": 416, "column": 3}}, "line": 394}, "26": {"name": "(anonymous_26)", "decl": {"start": {"line": 419, "column": 2}, "end": {"line": 419, "column": 3}}, "loc": {"start": {"line": 419, "column": 58}, "end": {"line": 423, "column": 3}}, "line": 419}, "27": {"name": "(anonymous_27)", "decl": {"start": {"line": 425, "column": 2}, "end": {"line": 425, "column": 3}}, "loc": {"start": {"line": 428, "column": 22}, "end": {"line": 435, "column": 3}}, "line": 428}, "28": {"name": "(anonymous_28)", "decl": {"start": {"line": 438, "column": 2}, "end": {"line": 438, "column": 3}}, "loc": {"start": {"line": 441, "column": 10}, "end": {"line": 446, "column": 3}}, "line": 441}, "29": {"name": "(anonymous_29)", "decl": {"start": {"line": 448, "column": 2}, "end": {"line": 448, "column": 3}}, "loc": {"start": {"line": 451, "column": 10}, "end": {"line": 459, "column": 3}}, "line": 451}, "30": {"name": "(anonymous_30)", "decl": {"start": {"line": 462, "column": 2}, "end": {"line": 462, "column": 3}}, "loc": {"start": {"line": 462, "column": 77}, "end": {"line": 480, "column": 3}}, "line": 462}, "31": {"name": "(anonymous_31)", "decl": {"start": {"line": 470, "column": 22}, "end": {"line": 470, "column": 23}}, "loc": {"start": {"line": 470, "column": 34}, "end": {"line": 479, "column": 5}}, "line": 470}, "32": {"name": "(anonymous_32)", "decl": {"start": {"line": 482, "column": 2}, "end": {"line": 482, "column": 3}}, "loc": {"start": {"line": 482, "column": 41}, "end": {"line": 486, "column": 3}}, "line": 482}, "33": {"name": "(anonymous_33)", "decl": {"start": {"line": 488, "column": 2}, "end": {"line": 488, "column": 3}}, "loc": {"start": {"line": 488, "column": 60}, "end": {"line": 516, "column": 3}}, "line": 488}, "34": {"name": "(anonymous_34)", "decl": {"start": {"line": 494, "column": 28}, "end": {"line": 494, "column": 29}}, "loc": {"start": {"line": 494, "column": 42}, "end": {"line": 501, "column": 9}}, "line": 494}, "35": {"name": "(anonymous_35)", "decl": {"start": {"line": 496, "column": 41}, "end": {"line": 496, "column": 42}}, "loc": {"start": {"line": 496, "column": 55}, "end": {"line": 499, "column": 11}}, "line": 496}, "36": {"name": "(anonymous_36)", "decl": {"start": {"line": 518, "column": 2}, "end": {"line": 518, "column": 3}}, "loc": {"start": {"line": 518, "column": 58}, "end": {"line": 559, "column": 3}}, "line": 518}, "37": {"name": "(anonymous_37)", "decl": {"start": {"line": 527, "column": 15}, "end": {"line": 527, "column": 16}}, "loc": {"start": {"line": 527, "column": 21}, "end": {"line": 527, "column": 45}}, "line": 527}, "38": {"name": "(anonymous_38)", "decl": {"start": {"line": 529, "column": 12}, "end": {"line": 529, "column": 13}}, "loc": {"start": {"line": 529, "column": 54}, "end": {"line": 529, "column": 78}}, "line": 529}, "39": {"name": "(anonymous_39)", "decl": {"start": {"line": 561, "column": 2}, "end": {"line": 561, "column": 3}}, "loc": {"start": {"line": 561, "column": 32}, "end": {"line": 571, "column": 3}}, "line": 561}, "40": {"name": "(anonymous_40)", "decl": {"start": {"line": 566, "column": 37}, "end": {"line": 566, "column": 38}}, "loc": {"start": {"line": 566, "column": 43}, "end": {"line": 570, "column": 5}}, "line": 566}, "41": {"name": "(anonymous_41)", "decl": {"start": {"line": 567, "column": 44}, "end": {"line": 567, "column": 45}}, "loc": {"start": {"line": 567, "column": 53}, "end": {"line": 569, "column": 7}}, "line": 567}, "42": {"name": "(anonymous_42)", "decl": {"start": {"line": 574, "column": 2}, "end": {"line": 574, "column": 3}}, "loc": {"start": {"line": 574, "column": 18}, "end": {"line": 581, "column": 3}}, "line": 574}}, "branchMap": {"0": {"loc": {"start": {"line": 47, "column": 14}, "end": {"line": 47, "column": 59}}, "type": "default-arg", "locations": [{"start": {"line": 47, "column": 57}, "end": {"line": 47, "column": 59}}], "line": 47}, "1": {"loc": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 35}}, "type": "if", "locations": [{"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 35}}, {"start": {}, "end": {}}], "line": 53}, "2": {"loc": {"start": {"line": 58, "column": 6}, "end": {"line": 60, "column": 7}}, "type": "if", "locations": [{"start": {"line": 58, "column": 6}, "end": {"line": 60, "column": 7}}, {"start": {}, "end": {}}], "line": 58}, "3": {"loc": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 56}}, {"start": {"line": 90, "column": 60}, "end": {"line": 90, "column": 61}}], "line": 90}, "4": {"loc": {"start": {"line": 91, "column": 6}, "end": {"line": 95, "column": 7}}, "type": "if", "locations": [{"start": {"line": 91, "column": 6}, "end": {"line": 95, "column": 7}}, {"start": {}, "end": {}}], "line": 91}, "5": {"loc": {"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 102, "column": 8}, "end": {"line": 102, "column": 48}}, {"start": {"line": 102, "column": 52}, "end": {"line": 102, "column": 54}}], "line": 102}, "6": {"loc": {"start": {"line": 131, "column": 11}, "end": {"line": 131, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 131, "column": 11}, "end": {"line": 131, "column": 35}}, {"start": {"line": 131, "column": 39}, "end": {"line": 131, "column": 43}}], "line": 131}, "7": {"loc": {"start": {"line": 140, "column": 6}, "end": {"line": 142, "column": 7}}, "type": "if", "locations": [{"start": {"line": 140, "column": 6}, "end": {"line": 142, "column": 7}}, {"start": {}, "end": {}}], "line": 140}, "8": {"loc": {"start": {"line": 174, "column": 6}, "end": {"line": 176, "column": 7}}, "type": "if", "locations": [{"start": {"line": 174, "column": 6}, "end": {"line": 176, "column": 7}}, {"start": {}, "end": {}}], "line": 174}, "9": {"loc": {"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 184, "column": 8}, "end": {"line": 184, "column": 48}}, {"start": {"line": 184, "column": 52}, "end": {"line": 184, "column": 54}}], "line": 184}, "10": {"loc": {"start": {"line": 212, "column": 28}, "end": {"line": 212, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 212, "column": 28}, "end": {"line": 212, "column": 68}}, {"start": {"line": 212, "column": 72}, "end": {"line": 212, "column": 74}}], "line": 212}, "11": {"loc": {"start": {"line": 247, "column": 6}, "end": {"line": 251, "column": 7}}, "type": "if", "locations": [{"start": {"line": 247, "column": 6}, "end": {"line": 251, "column": 7}}, {"start": {}, "end": {}}], "line": 247}, "12": {"loc": {"start": {"line": 253, "column": 6}, "end": {"line": 257, "column": 7}}, "type": "if", "locations": [{"start": {"line": 253, "column": 6}, "end": {"line": 257, "column": 7}}, {"start": {}, "end": {}}], "line": 253}, "13": {"loc": {"start": {"line": 259, "column": 6}, "end": {"line": 269, "column": 7}}, "type": "if", "locations": [{"start": {"line": 259, "column": 6}, "end": {"line": 269, "column": 7}}, {"start": {}, "end": {}}], "line": 259}, "14": {"loc": {"start": {"line": 263, "column": 12}, "end": {"line": 265, "column": 18}}, "type": "cond-expr", "locations": [{"start": {"line": 264, "column": 16}, "end": {"line": 264, "column": 49}}, {"start": {"line": 265, "column": 16}, "end": {"line": 265, "column": 18}}], "line": 263}, "15": {"loc": {"start": {"line": 264, "column": 16}, "end": {"line": 264, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 264, "column": 16}, "end": {"line": 264, "column": 43}}, {"start": {"line": 264, "column": 47}, "end": {"line": 264, "column": 49}}], "line": 264}, "16": {"loc": {"start": {"line": 271, "column": 6}, "end": {"line": 278, "column": 7}}, "type": "if", "locations": [{"start": {"line": 271, "column": 6}, "end": {"line": 278, "column": 7}}, {"start": {}, "end": {}}], "line": 271}, "17": {"loc": {"start": {"line": 276, "column": 17}, "end": {"line": 276, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 276, "column": 17}, "end": {"line": 276, "column": 44}}, {"start": {"line": 276, "column": 48}, "end": {"line": 276, "column": 73}}], "line": 276}, "18": {"loc": {"start": {"line": 287, "column": 21}, "end": {"line": 287, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 287, "column": 21}, "end": {"line": 287, "column": 36}}, {"start": {"line": 287, "column": 40}, "end": {"line": 287, "column": 41}}], "line": 287}, "19": {"loc": {"start": {"line": 288, "column": 20}, "end": {"line": 288, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 288, "column": 20}, "end": {"line": 288, "column": 34}}, {"start": {"line": 288, "column": 38}, "end": {"line": 288, "column": 40}}], "line": 288}, "20": {"loc": {"start": {"line": 312, "column": 26}, "end": {"line": 314, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 313, "column": 10}, "end": {"line": 313, "column": 58}}, {"start": {"line": 314, "column": 10}, "end": {"line": 314, "column": 47}}], "line": 312}, "21": {"loc": {"start": {"line": 317, "column": 32}, "end": {"line": 317, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 317, "column": 32}, "end": {"line": 317, "column": 52}}, {"start": {"line": 317, "column": 56}, "end": {"line": 317, "column": 57}}], "line": 317}, "22": {"loc": {"start": {"line": 322, "column": 38}, "end": {"line": 322, "column": 69}}, "type": "binary-expr", "locations": [{"start": {"line": 322, "column": 38}, "end": {"line": 322, "column": 64}}, {"start": {"line": 322, "column": 68}, "end": {"line": 322, "column": 69}}], "line": 322}, "23": {"loc": {"start": {"line": 328, "column": 21}, "end": {"line": 328, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 328, "column": 21}, "end": {"line": 328, "column": 30}}, {"start": {"line": 328, "column": 34}, "end": {"line": 328, "column": 35}}], "line": 328}, "24": {"loc": {"start": {"line": 333, "column": 8}, "end": {"line": 333, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 333, "column": 8}, "end": {"line": 333, "column": 72}}, {"start": {"line": 333, "column": 76}, "end": {"line": 333, "column": 78}}], "line": 333}, "25": {"loc": {"start": {"line": 344, "column": 10}, "end": {"line": 346, "column": 15}}, "type": "cond-expr", "locations": [{"start": {"line": 345, "column": 14}, "end": {"line": 345, "column": 65}}, {"start": {"line": 346, "column": 14}, "end": {"line": 346, "column": 15}}], "line": 344}, "26": {"loc": {"start": {"line": 442, "column": 4}, "end": {"line": 444, "column": 5}}, "type": "if", "locations": [{"start": {"line": 442, "column": 4}, "end": {"line": 444, "column": 5}}, {"start": {}, "end": {}}], "line": 442}, "27": {"loc": {"start": {"line": 453, "column": 4}, "end": {"line": 458, "column": 5}}, "type": "if", "locations": [{"start": {"line": 453, "column": 4}, "end": {"line": 458, "column": 5}}, {"start": {}, "end": {}}], "line": 453}, "28": {"loc": {"start": {"line": 455, "column": 6}, "end": {"line": 457, "column": 7}}, "type": "if", "locations": [{"start": {"line": 455, "column": 6}, "end": {"line": 457, "column": 7}}, {"start": {}, "end": {}}], "line": 455}, "29": {"loc": {"start": {"line": 469, "column": 22}, "end": {"line": 469, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 469, "column": 22}, "end": {"line": 469, "column": 51}}, {"start": {"line": 469, "column": 55}, "end": {"line": 469, "column": 57}}], "line": 469}, "30": {"loc": {"start": {"line": 491, "column": 6}, "end": {"line": 507, "column": 7}}, "type": "if", "locations": [{"start": {"line": 491, "column": 6}, "end": {"line": 507, "column": 7}}, {"start": {}, "end": {}}], "line": 491}, "31": {"loc": {"start": {"line": 532, "column": 8}, "end": {"line": 539, "column": 9}}, "type": "if", "locations": [{"start": {"line": 532, "column": 8}, "end": {"line": 539, "column": 9}}, {"start": {}, "end": {}}], "line": 532}, "32": {"loc": {"start": {"line": 562, "column": 4}, "end": {"line": 564, "column": 5}}, "type": "if", "locations": [{"start": {"line": 562, "column": 4}, "end": {"line": 564, "column": 5}}, {"start": {}, "end": {}}], "line": 562}, "33": {"loc": {"start": {"line": 575, "column": 4}, "end": {"line": 577, "column": 5}}, "type": "if", "locations": [{"start": {"line": 575, "column": 4}, "end": {"line": 577, "column": 5}}, {"start": {}, "end": {}}], "line": 575}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "163": 0, "164": 0, "165": 0, "166": 0, "167": 0, "168": 0, "169": 0, "170": 0, "171": 0, "172": 0, "173": 0, "174": 0, "175": 0, "176": 0, "177": 0, "178": 0, "179": 0, "180": 0, "181": 0, "182": 0, "183": 0, "184": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\document\\DocumentParserService.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\document\\DocumentParserService.ts", "statementMap": {"0": {"start": {"line": 26, "column": 57}, "end": {"line": 26, "column": 66}}, "1": {"start": {"line": 27, "column": 53}, "end": {"line": 27, "column": 62}}, "2": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 29}}, "3": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 33}}, "4": {"start": {"line": 36, "column": 23}, "end": {"line": 36, "column": 39}}, "5": {"start": {"line": 37, "column": 22}, "end": {"line": 37, "column": 37}}, "6": {"start": {"line": 38, "column": 23}, "end": {"line": 38, "column": 39}}, "7": {"start": {"line": 39, "column": 22}, "end": {"line": 39, "column": 37}}, "8": {"start": {"line": 40, "column": 27}, "end": {"line": 40, "column": 47}}, "9": {"start": {"line": 43, "column": 4}, "end": {"line": 45, "column": 7}}, "10": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 43}}, "11": {"start": {"line": 47, "column": 4}, "end": {"line": 49, "column": 7}}, "12": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 42}}, "13": {"start": {"line": 51, "column": 4}, "end": {"line": 53, "column": 7}}, "14": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 43}}, "15": {"start": {"line": 55, "column": 4}, "end": {"line": 57, "column": 7}}, "16": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 42}}, "17": {"start": {"line": 59, "column": 4}, "end": {"line": 61, "column": 7}}, "18": {"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": 47}}, "19": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 70}}, "20": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 64}}, "21": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 59}}, "22": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 64}}, "23": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 57}}, "24": {"start": {"line": 71, "column": 4}, "end": {"line": 74, "column": 6}}, "25": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 67}}, "26": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 57}}, "27": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 61}}, "28": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 59}}, "29": {"start": {"line": 85, "column": 4}, "end": {"line": 109, "column": 5}}, "30": {"start": {"line": 87, "column": 24}, "end": {"line": 87, "column": 69}}, "31": {"start": {"line": 88, "column": 32}, "end": {"line": 88, "column": 68}}, "32": {"start": {"line": 90, "column": 6}, "end": {"line": 92, "column": 7}}, "33": {"start": {"line": 91, "column": 8}, "end": {"line": 91, "column": 33}}, "34": {"start": {"line": 95, "column": 20}, "end": {"line": 95, "column": 45}}, "35": {"start": {"line": 96, "column": 6}, "end": {"line": 103, "column": 7}}, "36": {"start": {"line": 98, "column": 23}, "end": {"line": 98, "column": 65}}, "37": {"start": {"line": 99, "column": 23}, "end": {"line": 99, "column": 59}}, "38": {"start": {"line": 100, "column": 8}, "end": {"line": 102, "column": 9}}, "39": {"start": {"line": 101, "column": 10}, "end": {"line": 101, "column": 24}}, "40": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 18}}, "41": {"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 63}}, "42": {"start": {"line": 108, "column": 6}, "end": {"line": 108, "column": 18}}, "43": {"start": {"line": 113, "column": 25}, "end": {"line": 113, "column": 50}}, "44": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 75}}, "45": {"start": {"line": 118, "column": 57}, "end": {"line": 130, "column": 5}}, "46": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 43}}, "47": {"start": {"line": 137, "column": 19}, "end": {"line": 137, "column": 54}}, "48": {"start": {"line": 138, "column": 16}, "end": {"line": 138, "column": 52}}, "49": {"start": {"line": 141, "column": 55}, "end": {"line": 145, "column": 5}}, "50": {"start": {"line": 147, "column": 4}, "end": {"line": 151, "column": 5}}, "51": {"start": {"line": 148, "column": 6}, "end": {"line": 150, "column": 7}}, "52": {"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": 22}}, "53": {"start": {"line": 153, "column": 4}, "end": {"line": 153, "column": 16}}, "54": {"start": {"line": 160, "column": 45}, "end": {"line": 165, "column": 5}}, "55": {"start": {"line": 167, "column": 4}, "end": {"line": 222, "column": 5}}, "56": {"start": {"line": 169, "column": 21}, "end": {"line": 169, "column": 48}}, "57": {"start": {"line": 170, "column": 6}, "end": {"line": 173, "column": 7}}, "58": {"start": {"line": 171, "column": 8}, "end": {"line": 171, "column": 50}}, "59": {"start": {"line": 172, "column": 8}, "end": {"line": 172, "column": 22}}, "60": {"start": {"line": 176, "column": 20}, "end": {"line": 176, "column": 45}}, "61": {"start": {"line": 177, "column": 6}, "end": {"line": 177, "column": 35}}, "62": {"start": {"line": 180, "column": 26}, "end": {"line": 180, "column": 43}}, "63": {"start": {"line": 181, "column": 6}, "end": {"line": 188, "column": 7}}, "64": {"start": {"line": 182, "column": 8}, "end": {"line": 186, "column": 10}}, "65": {"start": {"line": 187, "column": 8}, "end": {"line": 187, "column": 22}}, "66": {"start": {"line": 191, "column": 21}, "end": {"line": 191, "column": 54}}, "67": {"start": {"line": 192, "column": 6}, "end": {"line": 195, "column": 7}}, "68": {"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": 58}}, "69": {"start": {"line": 194, "column": 8}, "end": {"line": 194, "column": 22}}, "70": {"start": {"line": 197, "column": 6}, "end": {"line": 197, "column": 29}}, "71": {"start": {"line": 200, "column": 21}, "end": {"line": 200, "column": 45}}, "72": {"start": {"line": 201, "column": 6}, "end": {"line": 204, "column": 7}}, "73": {"start": {"line": 202, "column": 8}, "end": {"line": 202, "column": 72}}, "74": {"start": {"line": 203, "column": 8}, "end": {"line": 203, "column": 22}}, "75": {"start": {"line": 207, "column": 22}, "end": {"line": 207, "column": 61}}, "76": {"start": {"line": 208, "column": 6}, "end": {"line": 208, "column": 31}}, "77": {"start": {"line": 210, "column": 6}, "end": {"line": 212, "column": 7}}, "78": {"start": {"line": 211, "column": 8}, "end": {"line": 211, "column": 79}}, "79": {"start": {"line": 214, "column": 6}, "end": {"line": 214, "column": 20}}, "80": {"start": {"line": 216, "column": 6}, "end": {"line": 220, "column": 8}}, "81": {"start": {"line": 221, "column": 6}, "end": {"line": 221, "column": 20}}, "82": {"start": {"line": 237, "column": 4}, "end": {"line": 277, "column": 5}}, "83": {"start": {"line": 239, "column": 25}, "end": {"line": 239, "column": 62}}, "84": {"start": {"line": 240, "column": 6}, "end": {"line": 245, "column": 7}}, "85": {"start": {"line": 241, "column": 8}, "end": {"line": 244, "column": 10}}, "86": {"start": {"line": 247, "column": 21}, "end": {"line": 247, "column": 39}}, "87": {"start": {"line": 248, "column": 21}, "end": {"line": 248, "column": 45}}, "88": {"start": {"line": 250, "column": 6}, "end": {"line": 256, "column": 7}}, "89": {"start": {"line": 251, "column": 8}, "end": {"line": 255, "column": 10}}, "90": {"start": {"line": 259, "column": 21}, "end": {"line": 259, "column": 58}}, "91": {"start": {"line": 260, "column": 6}, "end": {"line": 260, "column": 20}}, "92": {"start": {"line": 262, "column": 6}, "end": {"line": 262, "column": 54}}, "93": {"start": {"line": 264, "column": 6}, "end": {"line": 269, "column": 7}}, "94": {"start": {"line": 265, "column": 8}, "end": {"line": 268, "column": 10}}, "95": {"start": {"line": 271, "column": 6}, "end": {"line": 276, "column": 8}}, "96": {"start": {"line": 284, "column": 4}, "end": {"line": 299, "column": 5}}, "97": {"start": {"line": 285, "column": 21}, "end": {"line": 285, "column": 54}}, "98": {"start": {"line": 286, "column": 6}, "end": {"line": 288, "column": 7}}, "99": {"start": {"line": 287, "column": 8}, "end": {"line": 287, "column": 20}}, "100": {"start": {"line": 290, "column": 21}, "end": {"line": 290, "column": 45}}, "101": {"start": {"line": 291, "column": 6}, "end": {"line": 293, "column": 7}}, "102": {"start": {"line": 292, "column": 8}, "end": {"line": 292, "column": 20}}, "103": {"start": {"line": 295, "column": 6}, "end": {"line": 295, "column": 52}}, "104": {"start": {"line": 297, "column": 6}, "end": {"line": 297, "column": 57}}, "105": {"start": {"line": 298, "column": 6}, "end": {"line": 298, "column": 18}}, "106": {"start": {"line": 306, "column": 4}, "end": {"line": 306, "column": 43}}, "107": {"start": {"line": 313, "column": 4}, "end": {"line": 313, "column": 36}}, "108": {"start": {"line": 320, "column": 4}, "end": {"line": 320, "column": 36}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 3}}, "loc": {"start": {"line": 29, "column": 16}, "end": {"line": 32, "column": 3}}, "line": 29}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 3}}, "loc": {"start": {"line": 34, "column": 36}, "end": {"line": 62, "column": 3}}, "line": 34}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 43, "column": 40}, "end": {"line": 43, "column": 41}}, "loc": {"start": {"line": 43, "column": 50}, "end": {"line": 45, "column": 5}}, "line": 43}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 47, "column": 39}, "end": {"line": 47, "column": 40}}, "loc": {"start": {"line": 47, "column": 49}, "end": {"line": 49, "column": 5}}, "line": 47}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 51, "column": 40}, "end": {"line": 51, "column": 41}}, "loc": {"start": {"line": 51, "column": 50}, "end": {"line": 53, "column": 5}}, "line": 51}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 55, "column": 39}, "end": {"line": 55, "column": 40}}, "loc": {"start": {"line": 55, "column": 49}, "end": {"line": 57, "column": 5}}, "line": 55}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 59, "column": 44}, "end": {"line": 59, "column": 45}}, "loc": {"start": {"line": 59, "column": 54}, "end": {"line": 61, "column": 5}}, "line": 59}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 3}}, "loc": {"start": {"line": 64, "column": 40}, "end": {"line": 79, "column": 3}}, "line": 64}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 84, "column": 2}, "end": {"line": 84, "column": 3}}, "loc": {"start": {"line": 84, "column": 71}, "end": {"line": 110, "column": 3}}, "line": 84}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 112, "column": 2}, "end": {"line": 112, "column": 3}}, "loc": {"start": {"line": 112, "column": 53}, "end": {"line": 115, "column": 3}}, "line": 112}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 117, "column": 2}, "end": {"line": 117, "column": 3}}, "loc": {"start": {"line": 117, "column": 73}, "end": {"line": 133, "column": 3}}, "line": 117}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 135, "column": 2}, "end": {"line": 135, "column": 3}}, "loc": {"start": {"line": 135, "column": 79}, "end": {"line": 154, "column": 3}}, "line": 135}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 159, "column": 2}, "end": {"line": 159, "column": 3}}, "loc": {"start": {"line": 159, "column": 78}, "end": {"line": 223, "column": 3}}, "line": 159}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 228, "column": 2}, "end": {"line": 228, "column": 3}}, "loc": {"start": {"line": 236, "column": 34}, "end": {"line": 278, "column": 3}}, "line": 236}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 283, "column": 2}, "end": {"line": 283, "column": 3}}, "loc": {"start": {"line": 283, "column": 76}, "end": {"line": 300, "column": 3}}, "line": 283}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 305, "column": 2}, "end": {"line": 305, "column": 3}}, "loc": {"start": {"line": 305, "column": 42}, "end": {"line": 307, "column": 3}}, "line": 305}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 312, "column": 2}, "end": {"line": 312, "column": 3}}, "loc": {"start": {"line": 312, "column": 53}, "end": {"line": 314, "column": 3}}, "line": 312}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 319, "column": 2}, "end": {"line": 319, "column": 3}}, "loc": {"start": {"line": 319, "column": 64}, "end": {"line": 321, "column": 3}}, "line": 319}}, "branchMap": {"0": {"loc": {"start": {"line": 90, "column": 6}, "end": {"line": 92, "column": 7}}, "type": "if", "locations": [{"start": {"line": 90, "column": 6}, "end": {"line": 92, "column": 7}}, {"start": {}, "end": {}}], "line": 90}, "1": {"loc": {"start": {"line": 96, "column": 6}, "end": {"line": 103, "column": 7}}, "type": "if", "locations": [{"start": {"line": 96, "column": 6}, "end": {"line": 103, "column": 7}}, {"start": {}, "end": {}}], "line": 96}, "2": {"loc": {"start": {"line": 100, "column": 8}, "end": {"line": 102, "column": 9}}, "type": "if", "locations": [{"start": {"line": 100, "column": 8}, "end": {"line": 102, "column": 9}}, {"start": {}, "end": {}}], "line": 100}, "3": {"loc": {"start": {"line": 114, "column": 11}, "end": {"line": 114, "column": 74}}, "type": "cond-expr", "locations": [{"start": {"line": 114, "column": 33}, "end": {"line": 114, "column": 69}}, {"start": {"line": 114, "column": 72}, "end": {"line": 114, "column": 74}}], "line": 114}, "4": {"loc": {"start": {"line": 132, "column": 11}, "end": {"line": 132, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 132, "column": 11}, "end": {"line": 132, "column": 34}}, {"start": {"line": 132, "column": 38}, "end": {"line": 132, "column": 42}}], "line": 132}, "5": {"loc": {"start": {"line": 148, "column": 6}, "end": {"line": 150, "column": 7}}, "type": "if", "locations": [{"start": {"line": 148, "column": 6}, "end": {"line": 150, "column": 7}}, {"start": {}, "end": {}}], "line": 148}, "6": {"loc": {"start": {"line": 170, "column": 6}, "end": {"line": 173, "column": 7}}, "type": "if", "locations": [{"start": {"line": 170, "column": 6}, "end": {"line": 173, "column": 7}}, {"start": {}, "end": {}}], "line": 170}, "7": {"loc": {"start": {"line": 181, "column": 6}, "end": {"line": 188, "column": 7}}, "type": "if", "locations": [{"start": {"line": 181, "column": 6}, "end": {"line": 188, "column": 7}}, {"start": {}, "end": {}}], "line": 181}, "8": {"loc": {"start": {"line": 192, "column": 6}, "end": {"line": 195, "column": 7}}, "type": "if", "locations": [{"start": {"line": 192, "column": 6}, "end": {"line": 195, "column": 7}}, {"start": {}, "end": {}}], "line": 192}, "9": {"loc": {"start": {"line": 201, "column": 6}, "end": {"line": 204, "column": 7}}, "type": "if", "locations": [{"start": {"line": 201, "column": 6}, "end": {"line": 204, "column": 7}}, {"start": {}, "end": {}}], "line": 201}, "10": {"loc": {"start": {"line": 210, "column": 6}, "end": {"line": 212, "column": 7}}, "type": "if", "locations": [{"start": {"line": 210, "column": 6}, "end": {"line": 212, "column": 7}}, {"start": {}, "end": {}}], "line": 210}, "11": {"loc": {"start": {"line": 218, "column": 10}, "end": {"line": 218, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 218, "column": 35}, "end": {"line": 218, "column": 48}}, {"start": {"line": 218, "column": 51}, "end": {"line": 218, "column": 66}}], "line": 218}, "12": {"loc": {"start": {"line": 230, "column": 4}, "end": {"line": 235, "column": 5}}, "type": "default-arg", "locations": [{"start": {"line": 230, "column": 36}, "end": {"line": 235, "column": 5}}], "line": 230}, "13": {"loc": {"start": {"line": 240, "column": 6}, "end": {"line": 245, "column": 7}}, "type": "if", "locations": [{"start": {"line": 240, "column": 6}, "end": {"line": 245, "column": 7}}, {"start": {}, "end": {}}], "line": 240}, "14": {"loc": {"start": {"line": 250, "column": 6}, "end": {"line": 256, "column": 7}}, "type": "if", "locations": [{"start": {"line": 250, "column": 6}, "end": {"line": 256, "column": 7}}, {"start": {}, "end": {}}], "line": 250}, "15": {"loc": {"start": {"line": 264, "column": 6}, "end": {"line": 269, "column": 7}}, "type": "if", "locations": [{"start": {"line": 264, "column": 6}, "end": {"line": 269, "column": 7}}, {"start": {}, "end": {}}], "line": 264}, "16": {"loc": {"start": {"line": 274, "column": 10}, "end": {"line": 274, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 274, "column": 35}, "end": {"line": 274, "column": 48}}, {"start": {"line": 274, "column": 51}, "end": {"line": 274, "column": 66}}], "line": 274}, "17": {"loc": {"start": {"line": 286, "column": 6}, "end": {"line": 288, "column": 7}}, "type": "if", "locations": [{"start": {"line": 286, "column": 6}, "end": {"line": 288, "column": 7}}, {"start": {}, "end": {}}], "line": 286}, "18": {"loc": {"start": {"line": 291, "column": 6}, "end": {"line": 293, "column": 7}}, "type": "if", "locations": [{"start": {"line": 291, "column": 6}, "end": {"line": 293, "column": 7}}, {"start": {}, "end": {}}], "line": 291}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\document\\parsers\\EPUBParser.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\document\\parsers\\EPUBParser.ts", "statementMap": {"0": {"start": {"line": 24, "column": 39}, "end": {"line": 24, "column": 60}}, "1": {"start": {"line": 27, "column": 22}, "end": {"line": 27, "column": 61}}, "2": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 71}}, "3": {"start": {"line": 32, "column": 4}, "end": {"line": 52, "column": 5}}, "4": {"start": {"line": 34, "column": 21}, "end": {"line": 34, "column": 48}}, "5": {"start": {"line": 35, "column": 6}, "end": {"line": 37, "column": 7}}, "6": {"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": 21}}, "7": {"start": {"line": 40, "column": 25}, "end": {"line": 40, "column": 39}}, "8": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 23}}, "9": {"start": {"line": 44, "column": 26}, "end": {"line": 44, "column": 64}}, "10": {"start": {"line": 45, "column": 26}, "end": {"line": 45, "column": 64}}, "11": {"start": {"line": 46, "column": 23}, "end": {"line": 46, "column": 58}}, "12": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 52}}, "13": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 53}}, "14": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 19}}, "15": {"start": {"line": 64, "column": 4}, "end": {"line": 100, "column": 5}}, "16": {"start": {"line": 65, "column": 25}, "end": {"line": 65, "column": 39}}, "17": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 23}}, "18": {"start": {"line": 68, "column": 23}, "end": {"line": 68, "column": 73}}, "19": {"start": {"line": 69, "column": 37}, "end": {"line": 69, "column": 49}}, "20": {"start": {"line": 73, "column": 6}, "end": {"line": 75, "column": 7}}, "21": {"start": {"line": 74, "column": 8}, "end": {"line": 74, "column": 66}}, "22": {"start": {"line": 78, "column": 6}, "end": {"line": 80, "column": 7}}, "23": {"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": 59}}, "24": {"start": {"line": 82, "column": 45}, "end": {"line": 86, "column": 7}}, "25": {"start": {"line": 88, "column": 6}, "end": {"line": 91, "column": 8}}, "26": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 50}}, "27": {"start": {"line": 94, "column": 6}, "end": {"line": 99, "column": 8}}, "28": {"start": {"line": 104, "column": 4}, "end": {"line": 117, "column": 5}}, "29": {"start": {"line": 105, "column": 25}, "end": {"line": 105, "column": 39}}, "30": {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 23}}, "31": {"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 64}}, "32": {"start": {"line": 109, "column": 6}, "end": {"line": 116, "column": 8}}, "33": {"start": {"line": 124, "column": 18}, "end": {"line": 124, "column": 43}}, "34": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 65}}, "35": {"start": {"line": 129, "column": 15}, "end": {"line": 129, "column": 72}}, "36": {"start": {"line": 133, "column": 4}, "end": {"line": 141, "column": 5}}, "37": {"start": {"line": 134, "column": 23}, "end": {"line": 134, "column": 44}}, "38": {"start": {"line": 135, "column": 6}, "end": {"line": 137, "column": 7}}, "39": {"start": {"line": 136, "column": 8}, "end": {"line": 136, "column": 33}}, "40": {"start": {"line": 140, "column": 6}, "end": {"line": 140, "column": 59}}, "41": {"start": {"line": 143, "column": 4}, "end": {"line": 160, "column": 6}}, "42": {"start": {"line": 164, "column": 20}, "end": {"line": 164, "column": 36}}, "43": {"start": {"line": 165, "column": 4}, "end": {"line": 181, "column": 5}}, "44": {"start": {"line": 166, "column": 6}, "end": {"line": 168, "column": 7}}, "45": {"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 23}}, "46": {"start": {"line": 170, "column": 6}, "end": {"line": 172, "column": 7}}, "47": {"start": {"line": 171, "column": 8}, "end": {"line": 171, "column": 48}}, "48": {"start": {"line": 174, "column": 6}, "end": {"line": 180, "column": 7}}, "49": {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 59}}, "50": {"start": {"line": 182, "column": 4}, "end": {"line": 182, "column": 21}}, "51": {"start": {"line": 188, "column": 4}, "end": {"line": 226, "column": 5}}, "52": {"start": {"line": 189, "column": 25}, "end": {"line": 189, "column": 53}}, "53": {"start": {"line": 190, "column": 42}, "end": {"line": 190, "column": 44}}, "54": {"start": {"line": 192, "column": 29}, "end": {"line": 208, "column": 7}}, "55": {"start": {"line": 193, "column": 41}, "end": {"line": 198, "column": 9}}, "56": {"start": {"line": 200, "column": 8}, "end": {"line": 200, "column": 31}}, "57": {"start": {"line": 203, "column": 8}, "end": {"line": 207, "column": 9}}, "58": {"start": {"line": 204, "column": 10}, "end": {"line": 206, "column": 13}}, "59": {"start": {"line": 205, "column": 12}, "end": {"line": 205, "column": 47}}, "60": {"start": {"line": 210, "column": 6}, "end": {"line": 214, "column": 7}}, "61": {"start": {"line": 211, "column": 8}, "end": {"line": 213, "column": 11}}, "62": {"start": {"line": 212, "column": 10}, "end": {"line": 212, "column": 31}}, "63": {"start": {"line": 216, "column": 6}, "end": {"line": 219, "column": 8}}, "64": {"start": {"line": 221, "column": 6}, "end": {"line": 221, "column": 71}}, "65": {"start": {"line": 222, "column": 6}, "end": {"line": 225, "column": 8}}, "66": {"start": {"line": 233, "column": 4}, "end": {"line": 295, "column": 5}}, "67": {"start": {"line": 234, "column": 20}, "end": {"line": 234, "column": 30}}, "68": {"start": {"line": 235, "column": 21}, "end": {"line": 235, "column": 23}}, "69": {"start": {"line": 236, "column": 42}, "end": {"line": 236, "column": 44}}, "70": {"start": {"line": 240, "column": 26}, "end": {"line": 240, "column": 52}}, "71": {"start": {"line": 242, "column": 6}, "end": {"line": 283, "column": 7}}, "72": {"start": {"line": 242, "column": 19}, "end": {"line": 242, "column": 20}}, "73": {"start": {"line": 243, "column": 26}, "end": {"line": 243, "column": 38}}, "74": {"start": {"line": 245, "column": 8}, "end": {"line": 247, "column": 9}}, "75": {"start": {"line": 246, "column": 10}, "end": {"line": 246, "column": 19}}, "76": {"start": {"line": 249, "column": 8}, "end": {"line": 282, "column": 9}}, "77": {"start": {"line": 250, "column": 22}, "end": {"line": 250, "column": 64}}, "78": {"start": {"line": 251, "column": 30}, "end": {"line": 251, "column": 63}}, "79": {"start": {"line": 253, "column": 10}, "end": {"line": 275, "column": 11}}, "80": {"start": {"line": 254, "column": 30}, "end": {"line": 254, "column": 58}}, "81": {"start": {"line": 256, "column": 45}, "end": {"line": 262, "column": 13}}, "82": {"start": {"line": 264, "column": 12}, "end": {"line": 264, "column": 35}}, "83": {"start": {"line": 265, "column": 12}, "end": {"line": 265, "column": 45}}, "84": {"start": {"line": 268, "column": 12}, "end": {"line": 274, "column": 13}}, "85": {"start": {"line": 272, "column": 14}, "end": {"line": 272, "column": 70}}, "86": {"start": {"line": 273, "column": 14}, "end": {"line": 273, "column": 20}}, "87": {"start": {"line": 277, "column": 10}, "end": {"line": 280, "column": 12}}, "88": {"start": {"line": 281, "column": 10}, "end": {"line": 281, "column": 19}}, "89": {"start": {"line": 285, "column": 6}, "end": {"line": 288, "column": 8}}, "90": {"start": {"line": 290, "column": 6}, "end": {"line": 290, "column": 62}}, "91": {"start": {"line": 291, "column": 6}, "end": {"line": 294, "column": 8}}, "92": {"start": {"line": 299, "column": 4}, "end": {"line": 353, "column": 5}}, "93": {"start": {"line": 301, "column": 6}, "end": {"line": 311, "column": 7}}, "94": {"start": {"line": 305, "column": 24}, "end": {"line": 305, "column": 61}}, "95": {"start": {"line": 306, "column": 8}, "end": {"line": 310, "column": 11}}, "96": {"start": {"line": 307, "column": 10}, "end": {"line": 309, "column": 11}}, "97": {"start": {"line": 308, "column": 12}, "end": {"line": 308, "column": 29}}, "98": {"start": {"line": 314, "column": 24}, "end": {"line": 314, "column": 26}}, "99": {"start": {"line": 318, "column": 21}, "end": {"line": 318, "column": 31}}, "100": {"start": {"line": 320, "column": 6}, "end": {"line": 346, "column": 7}}, "101": {"start": {"line": 327, "column": 8}, "end": {"line": 327, "column": 60}}, "102": {"start": {"line": 328, "column": 13}, "end": {"line": 346, "column": 7}}, "103": {"start": {"line": 333, "column": 8}, "end": {"line": 333, "column": 55}}, "104": {"start": {"line": 334, "column": 13}, "end": {"line": 346, "column": 7}}, "105": {"start": {"line": 339, "column": 8}, "end": {"line": 339, "column": 53}}, "106": {"start": {"line": 342, "column": 27}, "end": {"line": 342, "column": 49}}, "107": {"start": {"line": 343, "column": 8}, "end": {"line": 345, "column": 9}}, "108": {"start": {"line": 344, "column": 10}, "end": {"line": 344, "column": 61}}, "109": {"start": {"line": 349, "column": 6}, "end": {"line": 349, "column": 53}}, "110": {"start": {"line": 351, "column": 6}, "end": {"line": 351, "column": 67}}, "111": {"start": {"line": 352, "column": 6}, "end": {"line": 352, "column": 16}}, "112": {"start": {"line": 357, "column": 4}, "end": {"line": 360, "column": 46}}, "113": {"start": {"line": 360, "column": 22}, "end": {"line": 360, "column": 37}}, "114": {"start": {"line": 367, "column": 21}, "end": {"line": 367, "column": 63}}, "115": {"start": {"line": 368, "column": 22}, "end": {"line": 368, "column": 58}}, "116": {"start": {"line": 369, "column": 4}, "end": {"line": 369, "column": 43}}, "117": {"start": {"line": 373, "column": 21}, "end": {"line": 373, "column": 58}}, "118": {"start": {"line": 374, "column": 25}, "end": {"line": 374, "column": 50}}, "119": {"start": {"line": 375, "column": 4}, "end": {"line": 375, "column": 80}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 3}}, "loc": {"start": {"line": 26, "column": 57}, "end": {"line": 29, "column": 3}}, "line": 26}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 3}}, "loc": {"start": {"line": 31, "column": 61}, "end": {"line": 53, "column": 3}}, "line": 31}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 3}}, "loc": {"start": {"line": 63, "column": 34}, "end": {"line": 101, "column": 3}}, "line": 63}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 3}}, "loc": {"start": {"line": 103, "column": 69}, "end": {"line": 118, "column": 3}}, "line": 103}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 120, "column": 2}, "end": {"line": 120, "column": 3}}, "loc": {"start": {"line": 123, "column": 31}, "end": {"line": 161, "column": 3}}, "line": 123}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 163, "column": 2}, "end": {"line": 163, "column": 3}}, "loc": {"start": {"line": 163, "column": 79}, "end": {"line": 183, "column": 3}}, "line": 163}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 185, "column": 2}, "end": {"line": 185, "column": 3}}, "loc": {"start": {"line": 187, "column": 38}, "end": {"line": 227, "column": 3}}, "line": 187}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 192, "column": 29}, "end": {"line": 192, "column": 30}}, "loc": {"start": {"line": 192, "column": 73}, "end": {"line": 208, "column": 7}}, "line": 192}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 204, "column": 32}, "end": {"line": 204, "column": 33}}, "loc": {"start": {"line": 204, "column": 54}, "end": {"line": 206, "column": 11}}, "line": 204}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 211, "column": 31}, "end": {"line": 211, "column": 32}}, "loc": {"start": {"line": 211, "column": 50}, "end": {"line": 213, "column": 9}}, "line": 211}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 229, "column": 2}, "end": {"line": 229, "column": 3}}, "loc": {"start": {"line": 232, "column": 30}, "end": {"line": 296, "column": 3}}, "line": 232}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 298, "column": 2}, "end": {"line": 298, "column": 3}}, "loc": {"start": {"line": 298, "column": 57}, "end": {"line": 354, "column": 3}}, "line": 298}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 306, "column": 24}, "end": {"line": 306, "column": 25}}, "loc": {"start": {"line": 306, "column": 46}, "end": {"line": 310, "column": 9}}, "line": 306}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 356, "column": 2}, "end": {"line": 356, "column": 3}}, "loc": {"start": {"line": 356, "column": 43}, "end": {"line": 361, "column": 3}}, "line": 356}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 360, "column": 14}, "end": {"line": 360, "column": 15}}, "loc": {"start": {"line": 360, "column": 22}, "end": {"line": 360, "column": 37}}, "line": 360}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 363, "column": 2}, "end": {"line": 363, "column": 3}}, "loc": {"start": {"line": 366, "column": 12}, "end": {"line": 370, "column": 3}}, "line": 366}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 372, "column": 2}, "end": {"line": 372, "column": 3}}, "loc": {"start": {"line": 372, "column": 64}, "end": {"line": 376, "column": 3}}, "line": 372}}, "branchMap": {"0": {"loc": {"start": {"line": 28, "column": 11}, "end": {"line": 28, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 28, "column": 11}, "end": {"line": 28, "column": 31}}, {"start": {"line": 28, "column": 35}, "end": {"line": 28, "column": 70}}], "line": 28}, "1": {"loc": {"start": {"line": 35, "column": 6}, "end": {"line": 37, "column": 7}}, "type": "if", "locations": [{"start": {"line": 35, "column": 6}, "end": {"line": 37, "column": 7}}, {"start": {}, "end": {}}], "line": 35}, "2": {"loc": {"start": {"line": 48, "column": 13}, "end": {"line": 48, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 48, "column": 13}, "end": {"line": 48, "column": 24}}, {"start": {"line": 48, "column": 28}, "end": {"line": 48, "column": 39}}, {"start": {"line": 48, "column": 43}, "end": {"line": 48, "column": 51}}], "line": 48}, "3": {"loc": {"start": {"line": 57, "column": 4}, "end": {"line": 62, "column": 5}}, "type": "default-arg", "locations": [{"start": {"line": 57, "column": 36}, "end": {"line": 62, "column": 5}}], "line": 57}, "4": {"loc": {"start": {"line": 73, "column": 6}, "end": {"line": 75, "column": 7}}, "type": "if", "locations": [{"start": {"line": 73, "column": 6}, "end": {"line": 75, "column": 7}}, {"start": {}, "end": {}}], "line": 73}, "5": {"loc": {"start": {"line": 78, "column": 6}, "end": {"line": 80, "column": 7}}, "type": "if", "locations": [{"start": {"line": 78, "column": 6}, "end": {"line": 80, "column": 7}}, {"start": {}, "end": {}}], "line": 78}, "6": {"loc": {"start": {"line": 97, "column": 10}, "end": {"line": 97, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 97, "column": 35}, "end": {"line": 97, "column": 48}}, {"start": {"line": 97, "column": 51}, "end": {"line": 97, "column": 66}}], "line": 97}, "7": {"loc": {"start": {"line": 112, "column": 10}, "end": {"line": 112, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 112, "column": 35}, "end": {"line": 112, "column": 48}}, {"start": {"line": 112, "column": 51}, "end": {"line": 112, "column": 66}}], "line": 112}, "8": {"loc": {"start": {"line": 115, "column": 8}, "end": {"line": 115, "column": 50}}, "type": "cond-expr", "locations": [{"start": {"line": 115, "column": 33}, "end": {"line": 115, "column": 38}}, {"start": {"line": 115, "column": 41}, "end": {"line": 115, "column": 50}}], "line": 115}, "9": {"loc": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 30}}, {"start": {"line": 126, "column": 35}, "end": {"line": 126, "column": 64}}], "line": 126}, "10": {"loc": {"start": {"line": 135, "column": 6}, "end": {"line": 137, "column": 7}}, "type": "if", "locations": [{"start": {"line": 135, "column": 6}, "end": {"line": 137, "column": 7}}, {"start": {}, "end": {}}], "line": 135}, "11": {"loc": {"start": {"line": 145, "column": 13}, "end": {"line": 145, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 145, "column": 13}, "end": {"line": 145, "column": 27}}, {"start": {"line": 145, "column": 31}, "end": {"line": 145, "column": 73}}], "line": 145}, "12": {"loc": {"start": {"line": 147, "column": 17}, "end": {"line": 147, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 147, "column": 17}, "end": {"line": 147, "column": 35}}, {"start": {"line": 147, "column": 39}, "end": {"line": 147, "column": 48}}], "line": 147}, "13": {"loc": {"start": {"line": 148, "column": 21}, "end": {"line": 148, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 148, "column": 21}, "end": {"line": 148, "column": 37}}, {"start": {"line": 148, "column": 41}, "end": {"line": 148, "column": 50}}], "line": 148}, "14": {"loc": {"start": {"line": 149, "column": 19}, "end": {"line": 149, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 149, "column": 19}, "end": {"line": 149, "column": 39}}, {"start": {"line": 149, "column": 43}, "end": {"line": 149, "column": 52}}], "line": 149}, "15": {"loc": {"start": {"line": 150, "column": 16}, "end": {"line": 150, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 150, "column": 16}, "end": {"line": 150, "column": 33}}, {"start": {"line": 150, "column": 37}, "end": {"line": 150, "column": 41}}], "line": 150}, "16": {"loc": {"start": {"line": 151, "column": 12}, "end": {"line": 151, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 151, "column": 12}, "end": {"line": 151, "column": 31}}, {"start": {"line": 151, "column": 35}, "end": {"line": 151, "column": 44}}], "line": 151}, "17": {"loc": {"start": {"line": 165, "column": 4}, "end": {"line": 181, "column": 5}}, "type": "if", "locations": [{"start": {"line": 165, "column": 4}, "end": {"line": 181, "column": 5}}, {"start": {}, "end": {}}], "line": 165}, "18": {"loc": {"start": {"line": 166, "column": 6}, "end": {"line": 168, "column": 7}}, "type": "if", "locations": [{"start": {"line": 166, "column": 6}, "end": {"line": 168, "column": 7}}, {"start": {}, "end": {}}], "line": 166}, "19": {"loc": {"start": {"line": 170, "column": 6}, "end": {"line": 172, "column": 7}}, "type": "if", "locations": [{"start": {"line": 170, "column": 6}, "end": {"line": 172, "column": 7}}, {"start": {}, "end": {}}], "line": 170}, "20": {"loc": {"start": {"line": 174, "column": 6}, "end": {"line": 180, "column": 7}}, "type": "if", "locations": [{"start": {"line": 174, "column": 6}, "end": {"line": 180, "column": 7}}, {"start": {}, "end": {}}], "line": 174}, "21": {"loc": {"start": {"line": 175, "column": 8}, "end": {"line": 177, "column": 25}}, "type": "binary-expr", "locations": [{"start": {"line": 175, "column": 8}, "end": {"line": 175, "column": 35}}, {"start": {"line": 176, "column": 8}, "end": {"line": 176, "column": 24}}, {"start": {"line": 177, "column": 8}, "end": {"line": 177, "column": 25}}], "line": 175}, "22": {"loc": {"start": {"line": 192, "column": 45}, "end": {"line": 192, "column": 62}}, "type": "default-arg", "locations": [{"start": {"line": 192, "column": 61}, "end": {"line": 192, "column": 62}}], "line": 192}, "23": {"loc": {"start": {"line": 194, "column": 14}, "end": {"line": 194, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 194, "column": 14}, "end": {"line": 194, "column": 21}}, {"start": {"line": 194, "column": 25}, "end": {"line": 194, "column": 53}}], "line": 194}, "24": {"loc": {"start": {"line": 195, "column": 17}, "end": {"line": 195, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 195, "column": 17}, "end": {"line": 195, "column": 27}}, {"start": {"line": 195, "column": 31}, "end": {"line": 195, "column": 63}}], "line": 195}, "25": {"loc": {"start": {"line": 203, "column": 8}, "end": {"line": 207, "column": 9}}, "type": "if", "locations": [{"start": {"line": 203, "column": 8}, "end": {"line": 207, "column": 9}}, {"start": {}, "end": {}}], "line": 203}, "26": {"loc": {"start": {"line": 203, "column": 12}, "end": {"line": 203, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 203, "column": 12}, "end": {"line": 203, "column": 25}}, {"start": {"line": 203, "column": 29}, "end": {"line": 203, "column": 57}}], "line": 203}, "27": {"loc": {"start": {"line": 210, "column": 6}, "end": {"line": 214, "column": 7}}, "type": "if", "locations": [{"start": {"line": 210, "column": 6}, "end": {"line": 214, "column": 7}}, {"start": {}, "end": {}}], "line": 210}, "28": {"loc": {"start": {"line": 210, "column": 10}, "end": {"line": 210, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 210, "column": 10}, "end": {"line": 210, "column": 20}}, {"start": {"line": 210, "column": 24}, "end": {"line": 210, "column": 38}}, {"start": {"line": 210, "column": 42}, "end": {"line": 210, "column": 71}}], "line": 210}, "29": {"loc": {"start": {"line": 240, "column": 26}, "end": {"line": 240, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 240, "column": 26}, "end": {"line": 240, "column": 47}}, {"start": {"line": 240, "column": 51}, "end": {"line": 240, "column": 52}}], "line": 240}, "30": {"loc": {"start": {"line": 245, "column": 8}, "end": {"line": 247, "column": 9}}, "type": "if", "locations": [{"start": {"line": 245, "column": 8}, "end": {"line": 247, "column": 9}}, {"start": {}, "end": {}}], "line": 245}, "31": {"loc": {"start": {"line": 253, "column": 10}, "end": {"line": 275, "column": 11}}, "type": "if", "locations": [{"start": {"line": 253, "column": 10}, "end": {"line": 275, "column": 11}}, {"start": {}, "end": {}}], "line": 253}, "32": {"loc": {"start": {"line": 257, "column": 18}, "end": {"line": 257, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 257, "column": 18}, "end": {"line": 257, "column": 33}}, {"start": {"line": 257, "column": 37}, "end": {"line": 257, "column": 49}}], "line": 257}, "33": {"loc": {"start": {"line": 268, "column": 12}, "end": {"line": 274, "column": 13}}, "type": "if", "locations": [{"start": {"line": 268, "column": 12}, "end": {"line": 274, "column": 13}}, {"start": {}, "end": {}}], "line": 268}, "34": {"loc": {"start": {"line": 269, "column": 14}, "end": {"line": 270, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 269, "column": 14}, "end": {"line": 269, "column": 35}}, {"start": {"line": 270, "column": 14}, "end": {"line": 270, "column": 53}}], "line": 269}, "35": {"loc": {"start": {"line": 301, "column": 6}, "end": {"line": 311, "column": 7}}, "type": "if", "locations": [{"start": {"line": 301, "column": 6}, "end": {"line": 311, "column": 7}}, {"start": {}, "end": {}}], "line": 301}, "36": {"loc": {"start": {"line": 302, "column": 8}, "end": {"line": 303, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 302, "column": 8}, "end": {"line": 302, "column": 33}}, {"start": {"line": 303, "column": 8}, "end": {"line": 303, "column": 50}}], "line": 302}, "37": {"loc": {"start": {"line": 307, "column": 10}, "end": {"line": 309, "column": 11}}, "type": "if", "locations": [{"start": {"line": 307, "column": 10}, "end": {"line": 309, "column": 11}}, {"start": {}, "end": {}}], "line": 307}, "38": {"loc": {"start": {"line": 307, "column": 14}, "end": {"line": 307, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 307, "column": 14}, "end": {"line": 307, "column": 33}}, {"start": {"line": 307, "column": 37}, "end": {"line": 307, "column": 73}}], "line": 307}, "39": {"loc": {"start": {"line": 320, "column": 6}, "end": {"line": 346, "column": 7}}, "type": "if", "locations": [{"start": {"line": 320, "column": 6}, "end": {"line": 346, "column": 7}}, {"start": {"line": 328, "column": 13}, "end": {"line": 346, "column": 7}}], "line": 320}, "40": {"loc": {"start": {"line": 321, "column": 8}, "end": {"line": 325, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 321, "column": 8}, "end": {"line": 321, "column": 14}}, {"start": {"line": 322, "column": 8}, "end": {"line": 322, "column": 34}}, {"start": {"line": 323, "column": 8}, "end": {"line": 323, "column": 24}}, {"start": {"line": 324, "column": 8}, "end": {"line": 324, "column": 19}}, {"start": {"line": 325, "column": 8}, "end": {"line": 325, "column": 36}}], "line": 321}, "41": {"loc": {"start": {"line": 327, "column": 29}, "end": {"line": 327, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 327, "column": 29}, "end": {"line": 327, "column": 52}}, {"start": {"line": 327, "column": 56}, "end": {"line": 327, "column": 58}}], "line": 327}, "42": {"loc": {"start": {"line": 328, "column": 13}, "end": {"line": 346, "column": 7}}, "type": "if", "locations": [{"start": {"line": 328, "column": 13}, "end": {"line": 346, "column": 7}}, {"start": {"line": 334, "column": 13}, "end": {"line": 346, "column": 7}}], "line": 328}, "43": {"loc": {"start": {"line": 329, "column": 8}, "end": {"line": 331, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 329, "column": 8}, "end": {"line": 329, "column": 14}}, {"start": {"line": 330, "column": 8}, "end": {"line": 330, "column": 34}}, {"start": {"line": 331, "column": 8}, "end": {"line": 331, "column": 31}}], "line": 329}, "44": {"loc": {"start": {"line": 333, "column": 29}, "end": {"line": 333, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 333, "column": 29}, "end": {"line": 333, "column": 47}}, {"start": {"line": 333, "column": 51}, "end": {"line": 333, "column": 53}}], "line": 333}, "45": {"loc": {"start": {"line": 334, "column": 13}, "end": {"line": 346, "column": 7}}, "type": "if", "locations": [{"start": {"line": 334, "column": 13}, "end": {"line": 346, "column": 7}}, {"start": {"line": 340, "column": 13}, "end": {"line": 346, "column": 7}}], "line": 334}, "46": {"loc": {"start": {"line": 335, "column": 8}, "end": {"line": 337, "column": 29}}, "type": "binary-expr", "locations": [{"start": {"line": 335, "column": 8}, "end": {"line": 335, "column": 14}}, {"start": {"line": 336, "column": 8}, "end": {"line": 336, "column": 34}}, {"start": {"line": 337, "column": 8}, "end": {"line": 337, "column": 29}}], "line": 335}, "47": {"loc": {"start": {"line": 339, "column": 29}, "end": {"line": 339, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 339, "column": 29}, "end": {"line": 339, "column": 45}}, {"start": {"line": 339, "column": 49}, "end": {"line": 339, "column": 51}}], "line": 339}, "48": {"loc": {"start": {"line": 343, "column": 8}, "end": {"line": 345, "column": 9}}, "type": "if", "locations": [{"start": {"line": 343, "column": 8}, "end": {"line": 345, "column": 9}}, {"start": {}, "end": {}}], "line": 343}, "49": {"loc": {"start": {"line": 343, "column": 12}, "end": {"line": 343, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 343, "column": 12}, "end": {"line": 343, "column": 22}}, {"start": {"line": 343, "column": 26}, "end": {"line": 343, "column": 53}}], "line": 343}, "50": {"loc": {"start": {"line": 344, "column": 31}, "end": {"line": 344, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 344, "column": 31}, "end": {"line": 344, "column": 53}}, {"start": {"line": 344, "column": 57}, "end": {"line": 344, "column": 59}}], "line": 344}, "51": {"loc": {"start": {"line": 373, "column": 21}, "end": {"line": 373, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 373, "column": 21}, "end": {"line": 373, "column": 46}}, {"start": {"line": 373, "column": 50}, "end": {"line": 373, "column": 58}}], "line": 373}, "52": {"loc": {"start": {"line": 375, "column": 11}, "end": {"line": 375, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 375, "column": 33}, "end": {"line": 375, "column": 68}}, {"start": {"line": 375, "column": 71}, "end": {"line": 375, "column": 79}}], "line": 375}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0, 0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0, 0], "22": [0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0, 0], "29": [0, 0], "30": [0, 0], "31": [0, 0], "32": [0, 0], "33": [0, 0], "34": [0, 0], "35": [0, 0], "36": [0, 0], "37": [0, 0], "38": [0, 0], "39": [0, 0], "40": [0, 0, 0, 0, 0], "41": [0, 0], "42": [0, 0], "43": [0, 0, 0], "44": [0, 0], "45": [0, 0], "46": [0, 0, 0], "47": [0, 0], "48": [0, 0], "49": [0, 0], "50": [0, 0], "51": [0, 0], "52": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\document\\parsers\\MarkdownParser.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\document\\parsers\\MarkdownParser.ts", "statementMap": {"0": {"start": {"line": 22, "column": 39}, "end": {"line": 22, "column": 79}}, "1": {"start": {"line": 25, "column": 22}, "end": {"line": 25, "column": 61}}, "2": {"start": {"line": 26, "column": 4}, "end": {"line": 33, "column": 6}}, "3": {"start": {"line": 37, "column": 4}, "end": {"line": 50, "column": 5}}, "4": {"start": {"line": 39, "column": 21}, "end": {"line": 39, "column": 48}}, "5": {"start": {"line": 40, "column": 6}, "end": {"line": 42, "column": 7}}, "6": {"start": {"line": 41, "column": 8}, "end": {"line": 41, "column": 21}}, "7": {"start": {"line": 45, "column": 21}, "end": {"line": 45, "column": 63}}, "8": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 36}}, "9": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 57}}, "10": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 19}}, "11": {"start": {"line": 62, "column": 4}, "end": {"line": 95, "column": 5}}, "12": {"start": {"line": 63, "column": 23}, "end": {"line": 63, "column": 67}}, "13": {"start": {"line": 64, "column": 37}, "end": {"line": 64, "column": 49}}, "14": {"start": {"line": 68, "column": 6}, "end": {"line": 70, "column": 7}}, "15": {"start": {"line": 69, "column": 8}, "end": {"line": 69, "column": 63}}, "16": {"start": {"line": 73, "column": 6}, "end": {"line": 75, "column": 7}}, "17": {"start": {"line": 74, "column": 8}, "end": {"line": 74, "column": 70}}, "18": {"start": {"line": 77, "column": 45}, "end": {"line": 81, "column": 7}}, "19": {"start": {"line": 83, "column": 6}, "end": {"line": 86, "column": 8}}, "20": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 54}}, "21": {"start": {"line": 89, "column": 6}, "end": {"line": 94, "column": 8}}, "22": {"start": {"line": 99, "column": 4}, "end": {"line": 110, "column": 5}}, "23": {"start": {"line": 100, "column": 6}, "end": {"line": 100, "column": 58}}, "24": {"start": {"line": 102, "column": 6}, "end": {"line": 109, "column": 8}}, "25": {"start": {"line": 116, "column": 18}, "end": {"line": 116, "column": 43}}, "26": {"start": {"line": 117, "column": 22}, "end": {"line": 117, "column": 61}}, "27": {"start": {"line": 121, "column": 6}, "end": {"line": 123, "column": 27}}, "28": {"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": 68}}, "29": {"start": {"line": 128, "column": 15}, "end": {"line": 132, "column": 5}}, "30": {"start": {"line": 135, "column": 28}, "end": {"line": 135, "column": 67}}, "31": {"start": {"line": 136, "column": 22}, "end": {"line": 136, "column": 64}}, "32": {"start": {"line": 138, "column": 4}, "end": {"line": 153, "column": 6}}, "33": {"start": {"line": 162, "column": 4}, "end": {"line": 187, "column": 5}}, "34": {"start": {"line": 163, "column": 22}, "end": {"line": 163, "column": 64}}, "35": {"start": {"line": 166, "column": 24}, "end": {"line": 166, "column": 65}}, "36": {"start": {"line": 167, "column": 6}, "end": {"line": 169, "column": 7}}, "37": {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": 55}}, "38": {"start": {"line": 172, "column": 29}, "end": {"line": 172, "column": 75}}, "39": {"start": {"line": 173, "column": 6}, "end": {"line": 175, "column": 7}}, "40": {"start": {"line": 174, "column": 8}, "end": {"line": 174, "column": 51}}, "41": {"start": {"line": 178, "column": 27}, "end": {"line": 178, "column": 55}}, "42": {"start": {"line": 179, "column": 6}, "end": {"line": 181, "column": 7}}, "43": {"start": {"line": 180, "column": 8}, "end": {"line": 180, "column": 49}}, "44": {"start": {"line": 183, "column": 6}, "end": {"line": 183, "column": 16}}, "45": {"start": {"line": 185, "column": 6}, "end": {"line": 185, "column": 60}}, "46": {"start": {"line": 186, "column": 6}, "end": {"line": 186, "column": 16}}, "47": {"start": {"line": 196, "column": 46}, "end": {"line": 196, "column": 48}}, "48": {"start": {"line": 199, "column": 18}, "end": {"line": 199, "column": 34}}, "49": {"start": {"line": 200, "column": 4}, "end": {"line": 206, "column": 5}}, "50": {"start": {"line": 201, "column": 20}, "end": {"line": 201, "column": 49}}, "51": {"start": {"line": 202, "column": 6}, "end": {"line": 205, "column": 7}}, "52": {"start": {"line": 203, "column": 31}, "end": {"line": 203, "column": 36}}, "53": {"start": {"line": 204, "column": 8}, "end": {"line": 204, "column": 72}}, "54": {"start": {"line": 208, "column": 4}, "end": {"line": 216, "column": 6}}, "55": {"start": {"line": 222, "column": 4}, "end": {"line": 232, "column": 5}}, "56": {"start": {"line": 223, "column": 22}, "end": {"line": 223, "column": 59}}, "57": {"start": {"line": 224, "column": 24}, "end": {"line": 224, "column": 57}}, "58": {"start": {"line": 226, "column": 6}, "end": {"line": 228, "column": 8}}, "59": {"start": {"line": 230, "column": 6}, "end": {"line": 230, "column": 64}}, "60": {"start": {"line": 231, "column": 6}, "end": {"line": 231, "column": 30}}, "61": {"start": {"line": 239, "column": 4}, "end": {"line": 259, "column": 5}}, "62": {"start": {"line": 240, "column": 20}, "end": {"line": 240, "column": 57}}, "63": {"start": {"line": 243, "column": 6}, "end": {"line": 245, "column": 7}}, "64": {"start": {"line": 244, "column": 8}, "end": {"line": 244, "column": 62}}, "65": {"start": {"line": 247, "column": 24}, "end": {"line": 247, "column": 57}}, "66": {"start": {"line": 248, "column": 19}, "end": {"line": 248, "column": 47}}, "67": {"start": {"line": 250, "column": 6}, "end": {"line": 253, "column": 8}}, "68": {"start": {"line": 255, "column": 6}, "end": {"line": 255, "column": 66}}, "69": {"start": {"line": 256, "column": 6}, "end": {"line": 258, "column": 8}}, "70": {"start": {"line": 265, "column": 4}, "end": {"line": 298, "column": 5}}, "71": {"start": {"line": 266, "column": 22}, "end": {"line": 266, "column": 59}}, "72": {"start": {"line": 267, "column": 42}, "end": {"line": 267, "column": 44}}, "73": {"start": {"line": 270, "column": 27}, "end": {"line": 270, "column": 48}}, "74": {"start": {"line": 272, "column": 25}, "end": {"line": 272, "column": 26}}, "75": {"start": {"line": 274, "column": 6}, "end": {"line": 286, "column": 7}}, "76": {"start": {"line": 275, "column": 22}, "end": {"line": 275, "column": 41}}, "77": {"start": {"line": 276, "column": 22}, "end": {"line": 276, "column": 37}}, "78": {"start": {"line": 278, "column": 8}, "end": {"line": 283, "column": 11}}, "79": {"start": {"line": 285, "column": 8}, "end": {"line": 285, "column": 23}}, "80": {"start": {"line": 288, "column": 6}, "end": {"line": 291, "column": 8}}, "81": {"start": {"line": 293, "column": 6}, "end": {"line": 293, "column": 66}}, "82": {"start": {"line": 294, "column": 6}, "end": {"line": 297, "column": 8}}, "83": {"start": {"line": 303, "column": 15}, "end": {"line": 303, "column": 66}}, "84": {"start": {"line": 306, "column": 4}, "end": {"line": 317, "column": 34}}, "85": {"start": {"line": 319, "column": 4}, "end": {"line": 319, "column": 23}}, "86": {"start": {"line": 324, "column": 15}, "end": {"line": 324, "column": 23}}, "87": {"start": {"line": 327, "column": 4}, "end": {"line": 327, "column": 55}}, "88": {"start": {"line": 328, "column": 4}, "end": {"line": 328, "column": 54}}, "89": {"start": {"line": 329, "column": 4}, "end": {"line": 329, "column": 53}}, "90": {"start": {"line": 332, "column": 4}, "end": {"line": 332, "column": 65}}, "91": {"start": {"line": 333, "column": 4}, "end": {"line": 333, "column": 53}}, "92": {"start": {"line": 336, "column": 4}, "end": {"line": 336, "column": 75}}, "93": {"start": {"line": 339, "column": 4}, "end": {"line": 339, "column": 39}}, "94": {"start": {"line": 341, "column": 4}, "end": {"line": 341, "column": 16}}, "95": {"start": {"line": 345, "column": 4}, "end": {"line": 348, "column": 28}}, "96": {"start": {"line": 352, "column": 4}, "end": {"line": 355, "column": 46}}, "97": {"start": {"line": 355, "column": 22}, "end": {"line": 355, "column": 37}}, "98": {"start": {"line": 363, "column": 21}, "end": {"line": 363, "column": 63}}, "99": {"start": {"line": 364, "column": 22}, "end": {"line": 364, "column": 58}}, "100": {"start": {"line": 365, "column": 4}, "end": {"line": 365, "column": 48}}, "101": {"start": {"line": 369, "column": 21}, "end": {"line": 369, "column": 58}}, "102": {"start": {"line": 370, "column": 25}, "end": {"line": 370, "column": 50}}, "103": {"start": {"line": 371, "column": 4}, "end": {"line": 371, "column": 80}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 3}}, "loc": {"start": {"line": 24, "column": 57}, "end": {"line": 34, "column": 3}}, "line": 24}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": 3}}, "loc": {"start": {"line": 36, "column": 61}, "end": {"line": 51, "column": 3}}, "line": 36}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 3}}, "loc": {"start": {"line": 61, "column": 34}, "end": {"line": 96, "column": 3}}, "line": 61}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 98, "column": 2}, "end": {"line": 98, "column": 3}}, "loc": {"start": {"line": 98, "column": 69}, "end": {"line": 111, "column": 3}}, "line": 98}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 3}}, "loc": {"start": {"line": 115, "column": 31}, "end": {"line": 154, "column": 3}}, "line": 115}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 156, "column": 2}, "end": {"line": 156, "column": 3}}, "loc": {"start": {"line": 161, "column": 5}, "end": {"line": 188, "column": 3}}, "line": 161}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 190, "column": 2}, "end": {"line": 190, "column": 3}}, "loc": {"start": {"line": 195, "column": 4}, "end": {"line": 217, "column": 3}}, "line": 195}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 219, "column": 2}, "end": {"line": 219, "column": 3}}, "loc": {"start": {"line": 221, "column": 5}, "end": {"line": 233, "column": 3}}, "line": 221}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 235, "column": 2}, "end": {"line": 235, "column": 3}}, "loc": {"start": {"line": 238, "column": 30}, "end": {"line": 260, "column": 3}}, "line": 238}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 262, "column": 2}, "end": {"line": 262, "column": 3}}, "loc": {"start": {"line": 264, "column": 38}, "end": {"line": 299, "column": 3}}, "line": 264}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 301, "column": 2}, "end": {"line": 301, "column": 3}}, "loc": {"start": {"line": 301, "column": 56}, "end": {"line": 320, "column": 3}}, "line": 301}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 322, "column": 2}, "end": {"line": 322, "column": 3}}, "loc": {"start": {"line": 322, "column": 51}, "end": {"line": 342, "column": 3}}, "line": 322}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 344, "column": 2}, "end": {"line": 344, "column": 3}}, "loc": {"start": {"line": 344, "column": 40}, "end": {"line": 349, "column": 3}}, "line": 344}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 351, "column": 2}, "end": {"line": 351, "column": 3}}, "loc": {"start": {"line": 351, "column": 43}, "end": {"line": 356, "column": 3}}, "line": 351}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 355, "column": 14}, "end": {"line": 355, "column": 15}}, "loc": {"start": {"line": 355, "column": 22}, "end": {"line": 355, "column": 37}}, "line": 355}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 358, "column": 2}, "end": {"line": 358, "column": 3}}, "loc": {"start": {"line": 362, "column": 12}, "end": {"line": 366, "column": 3}}, "line": 362}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 368, "column": 2}, "end": {"line": 368, "column": 3}}, "loc": {"start": {"line": 368, "column": 64}, "end": {"line": 372, "column": 3}}, "line": 368}}, "branchMap": {"0": {"loc": {"start": {"line": 27, "column": 6}, "end": {"line": 32, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 24}}, {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 30}}, {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 26}}, {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 25}}, {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 34}}, {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 30}}], "line": 27}, "1": {"loc": {"start": {"line": 40, "column": 6}, "end": {"line": 42, "column": 7}}, "type": "if", "locations": [{"start": {"line": 40, "column": 6}, "end": {"line": 42, "column": 7}}, {"start": {}, "end": {}}], "line": 40}, "2": {"loc": {"start": {"line": 55, "column": 4}, "end": {"line": 60, "column": 5}}, "type": "default-arg", "locations": [{"start": {"line": 55, "column": 36}, "end": {"line": 60, "column": 5}}], "line": 55}, "3": {"loc": {"start": {"line": 68, "column": 6}, "end": {"line": 70, "column": 7}}, "type": "if", "locations": [{"start": {"line": 68, "column": 6}, "end": {"line": 70, "column": 7}}, {"start": {}, "end": {}}], "line": 68}, "4": {"loc": {"start": {"line": 73, "column": 6}, "end": {"line": 75, "column": 7}}, "type": "if", "locations": [{"start": {"line": 73, "column": 6}, "end": {"line": 75, "column": 7}}, {"start": {}, "end": {}}], "line": 73}, "5": {"loc": {"start": {"line": 92, "column": 10}, "end": {"line": 92, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 92, "column": 35}, "end": {"line": 92, "column": 48}}, {"start": {"line": 92, "column": 51}, "end": {"line": 92, "column": 66}}], "line": 92}, "6": {"loc": {"start": {"line": 105, "column": 10}, "end": {"line": 105, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 105, "column": 35}, "end": {"line": 105, "column": 48}}, {"start": {"line": 105, "column": 51}, "end": {"line": 105, "column": 66}}], "line": 105}, "7": {"loc": {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": 50}}, "type": "cond-expr", "locations": [{"start": {"line": 108, "column": 33}, "end": {"line": 108, "column": 38}}, {"start": {"line": 108, "column": 41}, "end": {"line": 108, "column": 50}}], "line": 108}, "8": {"loc": {"start": {"line": 121, "column": 6}, "end": {"line": 123, "column": 27}}, "type": "cond-expr", "locations": [{"start": {"line": 122, "column": 10}, "end": {"line": 122, "column": 29}}, {"start": {"line": 123, "column": 10}, "end": {"line": 123, "column": 27}}], "line": 121}, "9": {"loc": {"start": {"line": 121, "column": 6}, "end": {"line": 121, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 121, "column": 6}, "end": {"line": 121, "column": 26}}, {"start": {"line": 121, "column": 30}, "end": {"line": 121, "column": 49}}], "line": 121}, "10": {"loc": {"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": 68}}, "type": "cond-expr", "locations": [{"start": {"line": 125, "column": 39}, "end": {"line": 125, "column": 50}}, {"start": {"line": 125, "column": 53}, "end": {"line": 125, "column": 68}}], "line": 125}, "11": {"loc": {"start": {"line": 141, "column": 8}, "end": {"line": 141, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 141, "column": 8}, "end": {"line": 141, "column": 29}}, {"start": {"line": 141, "column": 33}, "end": {"line": 141, "column": 75}}], "line": 141}, "12": {"loc": {"start": {"line": 167, "column": 6}, "end": {"line": 169, "column": 7}}, "type": "if", "locations": [{"start": {"line": 167, "column": 6}, "end": {"line": 169, "column": 7}}, {"start": {}, "end": {}}], "line": 167}, "13": {"loc": {"start": {"line": 173, "column": 6}, "end": {"line": 175, "column": 7}}, "type": "if", "locations": [{"start": {"line": 173, "column": 6}, "end": {"line": 175, "column": 7}}, {"start": {}, "end": {}}], "line": 173}, "14": {"loc": {"start": {"line": 179, "column": 6}, "end": {"line": 181, "column": 7}}, "type": "if", "locations": [{"start": {"line": 179, "column": 6}, "end": {"line": 181, "column": 7}}, {"start": {}, "end": {}}], "line": 179}, "15": {"loc": {"start": {"line": 202, "column": 6}, "end": {"line": 205, "column": 7}}, "type": "if", "locations": [{"start": {"line": 202, "column": 6}, "end": {"line": 205, "column": 7}}, {"start": {}, "end": {}}], "line": 202}, "16": {"loc": {"start": {"line": 209, "column": 13}, "end": {"line": 209, "column": 76}}, "type": "cond-expr", "locations": [{"start": {"line": 209, "column": 50}, "end": {"line": 209, "column": 64}}, {"start": {"line": 209, "column": 67}, "end": {"line": 209, "column": 76}}], "line": 209}, "17": {"loc": {"start": {"line": 210, "column": 14}, "end": {"line": 210, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 210, "column": 52}, "end": {"line": 210, "column": 67}}, {"start": {"line": 210, "column": 70}, "end": {"line": 210, "column": 79}}], "line": 210}, "18": {"loc": {"start": {"line": 211, "column": 12}, "end": {"line": 211, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 211, "column": 48}, "end": {"line": 211, "column": 61}}, {"start": {"line": 211, "column": 64}, "end": {"line": 211, "column": 73}}], "line": 211}, "19": {"loc": {"start": {"line": 213, "column": 8}, "end": {"line": 215, "column": 21}}, "type": "cond-expr", "locations": [{"start": {"line": 214, "column": 12}, "end": {"line": 214, "column": 32}}, {"start": {"line": 215, "column": 12}, "end": {"line": 215, "column": 21}}], "line": 213}, "20": {"loc": {"start": {"line": 243, "column": 6}, "end": {"line": 245, "column": 7}}, "type": "if", "locations": [{"start": {"line": 243, "column": 6}, "end": {"line": 245, "column": 7}}, {"start": {}, "end": {}}], "line": 243}, "21": {"loc": {"start": {"line": 243, "column": 10}, "end": {"line": 243, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 243, "column": 10}, "end": {"line": 243, "column": 31}}, {"start": {"line": 243, "column": 35}, "end": {"line": 243, "column": 73}}], "line": 243}, "22": {"loc": {"start": {"line": 369, "column": 21}, "end": {"line": 369, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 369, "column": 21}, "end": {"line": 369, "column": 46}}, {"start": {"line": 369, "column": 50}, "end": {"line": 369, "column": 58}}], "line": 369}, "23": {"loc": {"start": {"line": 371, "column": 11}, "end": {"line": 371, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 371, "column": 33}, "end": {"line": 371, "column": 68}}, {"start": {"line": 371, "column": 71}, "end": {"line": 371, "column": 79}}], "line": 371}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "b": {"0": [0, 0, 0, 0, 0, 0], "1": [0, 0], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\document\\parsers\\PDFParser.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\document\\parsers\\PDFParser.ts", "statementMap": {"0": {"start": {"line": 21, "column": 39}, "end": {"line": 21, "column": 59}}, "1": {"start": {"line": 24, "column": 22}, "end": {"line": 24, "column": 61}}, "2": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 65}}, "3": {"start": {"line": 29, "column": 4}, "end": {"line": 42, "column": 5}}, "4": {"start": {"line": 31, "column": 21}, "end": {"line": 31, "column": 48}}, "5": {"start": {"line": 32, "column": 6}, "end": {"line": 34, "column": 7}}, "6": {"start": {"line": 33, "column": 8}, "end": {"line": 33, "column": 21}}, "7": {"start": {"line": 37, "column": 21}, "end": {"line": 37, "column": 60}}, "8": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 40}}, "9": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 52}}, "10": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 19}}, "11": {"start": {"line": 54, "column": 4}, "end": {"line": 80, "column": 5}}, "12": {"start": {"line": 55, "column": 23}, "end": {"line": 55, "column": 67}}, "13": {"start": {"line": 56, "column": 37}, "end": {"line": 56, "column": 49}}, "14": {"start": {"line": 59, "column": 6}, "end": {"line": 61, "column": 7}}, "15": {"start": {"line": 60, "column": 8}, "end": {"line": 60, "column": 63}}, "16": {"start": {"line": 63, "column": 45}, "end": {"line": 66, "column": 7}}, "17": {"start": {"line": 68, "column": 6}, "end": {"line": 71, "column": 8}}, "18": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 49}}, "19": {"start": {"line": 74, "column": 6}, "end": {"line": 79, "column": 8}}, "20": {"start": {"line": 84, "column": 4}, "end": {"line": 95, "column": 5}}, "21": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 58}}, "22": {"start": {"line": 87, "column": 6}, "end": {"line": 94, "column": 8}}, "23": {"start": {"line": 101, "column": 18}, "end": {"line": 101, "column": 43}}, "24": {"start": {"line": 104, "column": 15}, "end": {"line": 104, "column": 72}}, "25": {"start": {"line": 107, "column": 26}, "end": {"line": 107, "column": 70}}, "26": {"start": {"line": 109, "column": 4}, "end": {"line": 124, "column": 6}}, "27": {"start": {"line": 135, "column": 4}, "end": {"line": 183, "column": 5}}, "28": {"start": {"line": 137, "column": 23}, "end": {"line": 137, "column": 55}}, "29": {"start": {"line": 138, "column": 23}, "end": {"line": 138, "column": 47}}, "30": {"start": {"line": 139, "column": 22}, "end": {"line": 139, "column": 68}}, "31": {"start": {"line": 141, "column": 48}, "end": {"line": 141, "column": 50}}, "32": {"start": {"line": 144, "column": 25}, "end": {"line": 144, "column": 63}}, "33": {"start": {"line": 145, "column": 6}, "end": {"line": 147, "column": 7}}, "34": {"start": {"line": 146, "column": 8}, "end": {"line": 146, "column": 61}}, "35": {"start": {"line": 150, "column": 26}, "end": {"line": 150, "column": 65}}, "36": {"start": {"line": 151, "column": 6}, "end": {"line": 153, "column": 7}}, "37": {"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": 63}}, "38": {"start": {"line": 156, "column": 27}, "end": {"line": 156, "column": 67}}, "39": {"start": {"line": 157, "column": 6}, "end": {"line": 159, "column": 7}}, "40": {"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 65}}, "41": {"start": {"line": 162, "column": 28}, "end": {"line": 162, "column": 69}}, "42": {"start": {"line": 163, "column": 6}, "end": {"line": 165, "column": 7}}, "43": {"start": {"line": 164, "column": 8}, "end": {"line": 164, "column": 67}}, "44": {"start": {"line": 168, "column": 32}, "end": {"line": 168, "column": 77}}, "45": {"start": {"line": 169, "column": 6}, "end": {"line": 171, "column": 7}}, "46": {"start": {"line": 170, "column": 8}, "end": {"line": 170, "column": 72}}, "47": {"start": {"line": 174, "column": 29}, "end": {"line": 174, "column": 61}}, "48": {"start": {"line": 175, "column": 6}, "end": {"line": 177, "column": 7}}, "49": {"start": {"line": 176, "column": 8}, "end": {"line": 176, "column": 61}}, "50": {"start": {"line": 179, "column": 6}, "end": {"line": 179, "column": 22}}, "51": {"start": {"line": 181, "column": 6}, "end": {"line": 181, "column": 61}}, "52": {"start": {"line": 182, "column": 6}, "end": {"line": 182, "column": 16}}, "53": {"start": {"line": 188, "column": 4}, "end": {"line": 196, "column": 30}}, "54": {"start": {"line": 201, "column": 18}, "end": {"line": 201, "column": 79}}, "55": {"start": {"line": 202, "column": 4}, "end": {"line": 212, "column": 5}}, "56": {"start": {"line": 203, "column": 57}, "end": {"line": 203, "column": 62}}, "57": {"start": {"line": 204, "column": 6}, "end": {"line": 211, "column": 22}}, "58": {"start": {"line": 213, "column": 4}, "end": {"line": 213, "column": 19}}, "59": {"start": {"line": 220, "column": 4}, "end": {"line": 254, "column": 5}}, "60": {"start": {"line": 225, "column": 36}, "end": {"line": 225, "column": 38}}, "61": {"start": {"line": 226, "column": 21}, "end": {"line": 226, "column": 23}}, "62": {"start": {"line": 230, "column": 30}, "end": {"line": 232, "column": 10}}, "63": {"start": {"line": 234, "column": 6}, "end": {"line": 234, "column": 33}}, "64": {"start": {"line": 237, "column": 6}, "end": {"line": 242, "column": 9}}, "65": {"start": {"line": 244, "column": 6}, "end": {"line": 247, "column": 8}}, "66": {"start": {"line": 249, "column": 6}, "end": {"line": 249, "column": 61}}, "67": {"start": {"line": 250, "column": 6}, "end": {"line": 253, "column": 8}}, "68": {"start": {"line": 258, "column": 4}, "end": {"line": 261, "column": 46}}, "69": {"start": {"line": 261, "column": 22}, "end": {"line": 261, "column": 37}}, "70": {"start": {"line": 268, "column": 21}, "end": {"line": 268, "column": 63}}, "71": {"start": {"line": 269, "column": 22}, "end": {"line": 269, "column": 58}}, "72": {"start": {"line": 270, "column": 4}, "end": {"line": 270, "column": 42}}, "73": {"start": {"line": 274, "column": 21}, "end": {"line": 274, "column": 58}}, "74": {"start": {"line": 275, "column": 25}, "end": {"line": 275, "column": 50}}, "75": {"start": {"line": 276, "column": 4}, "end": {"line": 276, "column": 80}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 3}}, "loc": {"start": {"line": 23, "column": 57}, "end": {"line": 26, "column": 3}}, "line": 23}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 3}}, "loc": {"start": {"line": 28, "column": 61}, "end": {"line": 43, "column": 3}}, "line": 28}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 3}}, "loc": {"start": {"line": 53, "column": 34}, "end": {"line": 81, "column": 3}}, "line": 53}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": 3}}, "loc": {"start": {"line": 83, "column": 69}, "end": {"line": 96, "column": 3}}, "line": 83}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 98, "column": 2}, "end": {"line": 98, "column": 3}}, "loc": {"start": {"line": 100, "column": 31}, "end": {"line": 125, "column": 3}}, "line": 100}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 127, "column": 2}, "end": {"line": 127, "column": 3}}, "loc": {"start": {"line": 134, "column": 5}, "end": {"line": 184, "column": 3}}, "line": 134}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 186, "column": 2}, "end": {"line": 186, "column": 3}}, "loc": {"start": {"line": 186, "column": 53}, "end": {"line": 197, "column": 3}}, "line": 186}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 199, "column": 2}, "end": {"line": 199, "column": 3}}, "loc": {"start": {"line": 199, "column": 48}, "end": {"line": 214, "column": 3}}, "line": 199}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 216, "column": 2}, "end": {"line": 216, "column": 3}}, "loc": {"start": {"line": 219, "column": 30}, "end": {"line": 255, "column": 3}}, "line": 219}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 257, "column": 2}, "end": {"line": 257, "column": 3}}, "loc": {"start": {"line": 257, "column": 43}, "end": {"line": 262, "column": 3}}, "line": 257}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 261, "column": 14}, "end": {"line": 261, "column": 15}}, "loc": {"start": {"line": 261, "column": 22}, "end": {"line": 261, "column": 37}}, "line": 261}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 264, "column": 2}, "end": {"line": 264, "column": 3}}, "loc": {"start": {"line": 267, "column": 12}, "end": {"line": 271, "column": 3}}, "line": 267}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 273, "column": 2}, "end": {"line": 273, "column": 3}}, "loc": {"start": {"line": 273, "column": 64}, "end": {"line": 277, "column": 3}}, "line": 273}}, "branchMap": {"0": {"loc": {"start": {"line": 25, "column": 11}, "end": {"line": 25, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 25, "column": 11}, "end": {"line": 25, "column": 30}}, {"start": {"line": 25, "column": 34}, "end": {"line": 25, "column": 64}}], "line": 25}, "1": {"loc": {"start": {"line": 32, "column": 6}, "end": {"line": 34, "column": 7}}, "type": "if", "locations": [{"start": {"line": 32, "column": 6}, "end": {"line": 34, "column": 7}}, {"start": {}, "end": {}}], "line": 32}, "2": {"loc": {"start": {"line": 47, "column": 4}, "end": {"line": 52, "column": 5}}, "type": "default-arg", "locations": [{"start": {"line": 47, "column": 36}, "end": {"line": 52, "column": 5}}], "line": 47}, "3": {"loc": {"start": {"line": 59, "column": 6}, "end": {"line": 61, "column": 7}}, "type": "if", "locations": [{"start": {"line": 59, "column": 6}, "end": {"line": 61, "column": 7}}, {"start": {}, "end": {}}], "line": 59}, "4": {"loc": {"start": {"line": 77, "column": 10}, "end": {"line": 77, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 77, "column": 35}, "end": {"line": 77, "column": 48}}, {"start": {"line": 77, "column": 51}, "end": {"line": 77, "column": 66}}], "line": 77}, "5": {"loc": {"start": {"line": 90, "column": 10}, "end": {"line": 90, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 90, "column": 35}, "end": {"line": 90, "column": 48}}, {"start": {"line": 90, "column": 51}, "end": {"line": 90, "column": 66}}], "line": 90}, "6": {"loc": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 50}}, "type": "cond-expr", "locations": [{"start": {"line": 93, "column": 33}, "end": {"line": 93, "column": 38}}, {"start": {"line": 93, "column": 41}, "end": {"line": 93, "column": 50}}], "line": 93}, "7": {"loc": {"start": {"line": 111, "column": 13}, "end": {"line": 111, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 111, "column": 13}, "end": {"line": 111, "column": 32}}, {"start": {"line": 111, "column": 36}, "end": {"line": 111, "column": 78}}], "line": 111}, "8": {"loc": {"start": {"line": 145, "column": 6}, "end": {"line": 147, "column": 7}}, "type": "if", "locations": [{"start": {"line": 145, "column": 6}, "end": {"line": 147, "column": 7}}, {"start": {}, "end": {}}], "line": 145}, "9": {"loc": {"start": {"line": 151, "column": 6}, "end": {"line": 153, "column": 7}}, "type": "if", "locations": [{"start": {"line": 151, "column": 6}, "end": {"line": 153, "column": 7}}, {"start": {}, "end": {}}], "line": 151}, "10": {"loc": {"start": {"line": 157, "column": 6}, "end": {"line": 159, "column": 7}}, "type": "if", "locations": [{"start": {"line": 157, "column": 6}, "end": {"line": 159, "column": 7}}, {"start": {}, "end": {}}], "line": 157}, "11": {"loc": {"start": {"line": 163, "column": 6}, "end": {"line": 165, "column": 7}}, "type": "if", "locations": [{"start": {"line": 163, "column": 6}, "end": {"line": 165, "column": 7}}, {"start": {}, "end": {}}], "line": 163}, "12": {"loc": {"start": {"line": 169, "column": 6}, "end": {"line": 171, "column": 7}}, "type": "if", "locations": [{"start": {"line": 169, "column": 6}, "end": {"line": 171, "column": 7}}, {"start": {}, "end": {}}], "line": 169}, "13": {"loc": {"start": {"line": 175, "column": 6}, "end": {"line": 177, "column": 7}}, "type": "if", "locations": [{"start": {"line": 175, "column": 6}, "end": {"line": 177, "column": 7}}, {"start": {}, "end": {}}], "line": 175}, "14": {"loc": {"start": {"line": 202, "column": 4}, "end": {"line": 212, "column": 5}}, "type": "if", "locations": [{"start": {"line": 202, "column": 4}, "end": {"line": 212, "column": 5}}, {"start": {}, "end": {}}], "line": 202}, "15": {"loc": {"start": {"line": 274, "column": 21}, "end": {"line": 274, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 274, "column": 21}, "end": {"line": 274, "column": 46}}, {"start": {"line": 274, "column": 50}, "end": {"line": 274, "column": 58}}], "line": 274}, "16": {"loc": {"start": {"line": 276, "column": 11}, "end": {"line": 276, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 276, "column": 33}, "end": {"line": 276, "column": 68}}, {"start": {"line": 276, "column": 71}, "end": {"line": 276, "column": 79}}], "line": 276}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\document\\parsers\\RTFParser.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\document\\parsers\\RTFParser.ts", "statementMap": {"0": {"start": {"line": 20, "column": 39}, "end": {"line": 20, "column": 59}}, "1": {"start": {"line": 23, "column": 22}, "end": {"line": 23, "column": 61}}, "2": {"start": {"line": 24, "column": 4}, "end": {"line": 28, "column": 6}}, "3": {"start": {"line": 32, "column": 4}, "end": {"line": 45, "column": 5}}, "4": {"start": {"line": 34, "column": 21}, "end": {"line": 34, "column": 48}}, "5": {"start": {"line": 35, "column": 6}, "end": {"line": 37, "column": 7}}, "6": {"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": 21}}, "7": {"start": {"line": 40, "column": 21}, "end": {"line": 40, "column": 61}}, "8": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 41}}, "9": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 52}}, "10": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 19}}, "11": {"start": {"line": 57, "column": 4}, "end": {"line": 83, "column": 5}}, "12": {"start": {"line": 58, "column": 23}, "end": {"line": 58, "column": 67}}, "13": {"start": {"line": 59, "column": 37}, "end": {"line": 59, "column": 49}}, "14": {"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}, "15": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 63}}, "16": {"start": {"line": 66, "column": 45}, "end": {"line": 69, "column": 7}}, "17": {"start": {"line": 71, "column": 6}, "end": {"line": 74, "column": 8}}, "18": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 49}}, "19": {"start": {"line": 77, "column": 6}, "end": {"line": 82, "column": 8}}, "20": {"start": {"line": 87, "column": 4}, "end": {"line": 98, "column": 5}}, "21": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 58}}, "22": {"start": {"line": 90, "column": 6}, "end": {"line": 97, "column": 8}}, "23": {"start": {"line": 104, "column": 18}, "end": {"line": 104, "column": 43}}, "24": {"start": {"line": 107, "column": 15}, "end": {"line": 107, "column": 72}}, "25": {"start": {"line": 110, "column": 24}, "end": {"line": 110, "column": 63}}, "26": {"start": {"line": 112, "column": 4}, "end": {"line": 126, "column": 6}}, "27": {"start": {"line": 136, "column": 4}, "end": {"line": 169, "column": 5}}, "28": {"start": {"line": 138, "column": 22}, "end": {"line": 138, "column": 64}}, "29": {"start": {"line": 139, "column": 48}, "end": {"line": 139, "column": 50}}, "30": {"start": {"line": 142, "column": 25}, "end": {"line": 142, "column": 60}}, "31": {"start": {"line": 143, "column": 6}, "end": {"line": 145, "column": 7}}, "32": {"start": {"line": 144, "column": 8}, "end": {"line": 144, "column": 58}}, "33": {"start": {"line": 148, "column": 26}, "end": {"line": 148, "column": 62}}, "34": {"start": {"line": 149, "column": 6}, "end": {"line": 151, "column": 7}}, "35": {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 60}}, "36": {"start": {"line": 154, "column": 27}, "end": {"line": 154, "column": 64}}, "37": {"start": {"line": 155, "column": 6}, "end": {"line": 157, "column": 7}}, "38": {"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 62}}, "39": {"start": {"line": 160, "column": 27}, "end": {"line": 160, "column": 64}}, "40": {"start": {"line": 161, "column": 6}, "end": {"line": 163, "column": 7}}, "41": {"start": {"line": 162, "column": 8}, "end": {"line": 162, "column": 62}}, "42": {"start": {"line": 165, "column": 6}, "end": {"line": 165, "column": 22}}, "43": {"start": {"line": 167, "column": 6}, "end": {"line": 167, "column": 61}}, "44": {"start": {"line": 168, "column": 6}, "end": {"line": 168, "column": 16}}, "45": {"start": {"line": 176, "column": 4}, "end": {"line": 195, "column": 5}}, "46": {"start": {"line": 177, "column": 25}, "end": {"line": 177, "column": 62}}, "47": {"start": {"line": 178, "column": 24}, "end": {"line": 178, "column": 55}}, "48": {"start": {"line": 181, "column": 22}, "end": {"line": 181, "column": 31}}, "49": {"start": {"line": 182, "column": 6}, "end": {"line": 184, "column": 7}}, "50": {"start": {"line": 183, "column": 8}, "end": {"line": 183, "column": 66}}, "51": {"start": {"line": 186, "column": 6}, "end": {"line": 189, "column": 8}}, "52": {"start": {"line": 191, "column": 6}, "end": {"line": 191, "column": 61}}, "53": {"start": {"line": 192, "column": 6}, "end": {"line": 194, "column": 8}}, "54": {"start": {"line": 199, "column": 4}, "end": {"line": 226, "column": 5}}, "55": {"start": {"line": 201, "column": 17}, "end": {"line": 201, "column": 27}}, "56": {"start": {"line": 204, "column": 6}, "end": {"line": 204, "column": 47}}, "57": {"start": {"line": 207, "column": 6}, "end": {"line": 207, "column": 50}}, "58": {"start": {"line": 210, "column": 6}, "end": {"line": 210, "column": 46}}, "59": {"start": {"line": 213, "column": 6}, "end": {"line": 213, "column": 39}}, "60": {"start": {"line": 216, "column": 6}, "end": {"line": 216, "column": 45}}, "61": {"start": {"line": 217, "column": 6}, "end": {"line": 217, "column": 46}}, "62": {"start": {"line": 220, "column": 6}, "end": {"line": 220, "column": 46}}, "63": {"start": {"line": 222, "column": 6}, "end": {"line": 222, "column": 18}}, "64": {"start": {"line": 224, "column": 6}, "end": {"line": 224, "column": 67}}, "65": {"start": {"line": 225, "column": 6}, "end": {"line": 225, "column": 16}}, "66": {"start": {"line": 230, "column": 4}, "end": {"line": 242, "column": 5}}, "67": {"start": {"line": 232, "column": 17}, "end": {"line": 232, "column": 48}}, "68": {"start": {"line": 235, "column": 6}, "end": {"line": 235, "column": 41}}, "69": {"start": {"line": 238, "column": 6}, "end": {"line": 238, "column": 34}}, "70": {"start": {"line": 240, "column": 6}, "end": {"line": 240, "column": 61}}, "71": {"start": {"line": 241, "column": 6}, "end": {"line": 241, "column": 16}}, "72": {"start": {"line": 247, "column": 4}, "end": {"line": 251, "column": 14}}, "73": {"start": {"line": 258, "column": 21}, "end": {"line": 258, "column": 63}}, "74": {"start": {"line": 259, "column": 22}, "end": {"line": 259, "column": 58}}, "75": {"start": {"line": 260, "column": 4}, "end": {"line": 260, "column": 42}}, "76": {"start": {"line": 264, "column": 21}, "end": {"line": 264, "column": 58}}, "77": {"start": {"line": 265, "column": 25}, "end": {"line": 265, "column": 50}}, "78": {"start": {"line": 266, "column": 4}, "end": {"line": 266, "column": 80}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 3}}, "loc": {"start": {"line": 22, "column": 57}, "end": {"line": 29, "column": 3}}, "line": 22}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 3}}, "loc": {"start": {"line": 31, "column": 61}, "end": {"line": 46, "column": 3}}, "line": 31}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 3}}, "loc": {"start": {"line": 56, "column": 34}, "end": {"line": 84, "column": 3}}, "line": 56}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 3}}, "loc": {"start": {"line": 86, "column": 69}, "end": {"line": 99, "column": 3}}, "line": 86}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 101, "column": 2}, "end": {"line": 101, "column": 3}}, "loc": {"start": {"line": 103, "column": 31}, "end": {"line": 127, "column": 3}}, "line": 103}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 129, "column": 2}, "end": {"line": 129, "column": 3}}, "loc": {"start": {"line": 135, "column": 5}, "end": {"line": 170, "column": 3}}, "line": 135}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 172, "column": 2}, "end": {"line": 172, "column": 3}}, "loc": {"start": {"line": 175, "column": 30}, "end": {"line": 196, "column": 3}}, "line": 175}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 198, "column": 2}, "end": {"line": 198, "column": 3}}, "loc": {"start": {"line": 198, "column": 53}, "end": {"line": 227, "column": 3}}, "line": 198}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 229, "column": 2}, "end": {"line": 229, "column": 3}}, "loc": {"start": {"line": 229, "column": 48}, "end": {"line": 243, "column": 3}}, "line": 229}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 245, "column": 2}, "end": {"line": 245, "column": 3}}, "loc": {"start": {"line": 245, "column": 45}, "end": {"line": 252, "column": 3}}, "line": 245}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 254, "column": 2}, "end": {"line": 254, "column": 3}}, "loc": {"start": {"line": 257, "column": 12}, "end": {"line": 261, "column": 3}}, "line": 257}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 263, "column": 2}, "end": {"line": 263, "column": 3}}, "loc": {"start": {"line": 263, "column": 64}, "end": {"line": 267, "column": 3}}, "line": 263}}, "branchMap": {"0": {"loc": {"start": {"line": 25, "column": 6}, "end": {"line": 27, "column": 29}}, "type": "binary-expr", "locations": [{"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 25}}, {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 36}}, {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 29}}], "line": 25}, "1": {"loc": {"start": {"line": 35, "column": 6}, "end": {"line": 37, "column": 7}}, "type": "if", "locations": [{"start": {"line": 35, "column": 6}, "end": {"line": 37, "column": 7}}, {"start": {}, "end": {}}], "line": 35}, "2": {"loc": {"start": {"line": 50, "column": 4}, "end": {"line": 55, "column": 5}}, "type": "default-arg", "locations": [{"start": {"line": 50, "column": 36}, "end": {"line": 55, "column": 5}}], "line": 50}, "3": {"loc": {"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}, "type": "if", "locations": [{"start": {"line": 62, "column": 6}, "end": {"line": 64, "column": 7}}, {"start": {}, "end": {}}], "line": 62}, "4": {"loc": {"start": {"line": 80, "column": 10}, "end": {"line": 80, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 80, "column": 35}, "end": {"line": 80, "column": 48}}, {"start": {"line": 80, "column": 51}, "end": {"line": 80, "column": 66}}], "line": 80}, "5": {"loc": {"start": {"line": 93, "column": 10}, "end": {"line": 93, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 93, "column": 35}, "end": {"line": 93, "column": 48}}, {"start": {"line": 93, "column": 51}, "end": {"line": 93, "column": 66}}], "line": 93}, "6": {"loc": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 50}}, "type": "cond-expr", "locations": [{"start": {"line": 96, "column": 33}, "end": {"line": 96, "column": 38}}, {"start": {"line": 96, "column": 41}, "end": {"line": 96, "column": 50}}], "line": 96}, "7": {"loc": {"start": {"line": 114, "column": 13}, "end": {"line": 114, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 114, "column": 13}, "end": {"line": 114, "column": 30}}, {"start": {"line": 114, "column": 34}, "end": {"line": 114, "column": 76}}], "line": 114}, "8": {"loc": {"start": {"line": 143, "column": 6}, "end": {"line": 145, "column": 7}}, "type": "if", "locations": [{"start": {"line": 143, "column": 6}, "end": {"line": 145, "column": 7}}, {"start": {}, "end": {}}], "line": 143}, "9": {"loc": {"start": {"line": 149, "column": 6}, "end": {"line": 151, "column": 7}}, "type": "if", "locations": [{"start": {"line": 149, "column": 6}, "end": {"line": 151, "column": 7}}, {"start": {}, "end": {}}], "line": 149}, "10": {"loc": {"start": {"line": 155, "column": 6}, "end": {"line": 157, "column": 7}}, "type": "if", "locations": [{"start": {"line": 155, "column": 6}, "end": {"line": 157, "column": 7}}, {"start": {}, "end": {}}], "line": 155}, "11": {"loc": {"start": {"line": 161, "column": 6}, "end": {"line": 163, "column": 7}}, "type": "if", "locations": [{"start": {"line": 161, "column": 6}, "end": {"line": 163, "column": 7}}, {"start": {}, "end": {}}], "line": 161}, "12": {"loc": {"start": {"line": 182, "column": 6}, "end": {"line": 184, "column": 7}}, "type": "if", "locations": [{"start": {"line": 182, "column": 6}, "end": {"line": 184, "column": 7}}, {"start": {}, "end": {}}], "line": 182}, "13": {"loc": {"start": {"line": 182, "column": 10}, "end": {"line": 182, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 182, "column": 10}, "end": {"line": 182, "column": 31}}, {"start": {"line": 182, "column": 35}, "end": {"line": 182, "column": 75}}], "line": 182}, "14": {"loc": {"start": {"line": 264, "column": 21}, "end": {"line": 264, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 264, "column": 21}, "end": {"line": 264, "column": 46}}, {"start": {"line": 264, "column": 50}, "end": {"line": 264, "column": 58}}], "line": 264}, "15": {"loc": {"start": {"line": 266, "column": 11}, "end": {"line": 266, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 266, "column": 33}, "end": {"line": 266, "column": 68}}, {"start": {"line": 266, "column": 71}, "end": {"line": 266, "column": 79}}], "line": 266}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "b": {"0": [0, 0, 0], "1": [0, 0], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\document\\parsers\\TextParser.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\document\\parsers\\TextParser.ts", "statementMap": {"0": {"start": {"line": 20, "column": 39}, "end": {"line": 20, "column": 79}}, "1": {"start": {"line": 23, "column": 22}, "end": {"line": 23, "column": 61}}, "2": {"start": {"line": 24, "column": 4}, "end": {"line": 29, "column": 6}}, "3": {"start": {"line": 33, "column": 4}, "end": {"line": 49, "column": 5}}, "4": {"start": {"line": 35, "column": 21}, "end": {"line": 35, "column": 48}}, "5": {"start": {"line": 36, "column": 6}, "end": {"line": 38, "column": 7}}, "6": {"start": {"line": 37, "column": 8}, "end": {"line": 37, "column": 21}}, "7": {"start": {"line": 41, "column": 21}, "end": {"line": 41, "column": 63}}, "8": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 40}}, "9": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 53}}, "10": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 19}}, "11": {"start": {"line": 61, "column": 4}, "end": {"line": 87, "column": 5}}, "12": {"start": {"line": 62, "column": 23}, "end": {"line": 62, "column": 67}}, "13": {"start": {"line": 63, "column": 37}, "end": {"line": 63, "column": 49}}, "14": {"start": {"line": 66, "column": 6}, "end": {"line": 68, "column": 7}}, "15": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 63}}, "16": {"start": {"line": 70, "column": 45}, "end": {"line": 73, "column": 7}}, "17": {"start": {"line": 75, "column": 6}, "end": {"line": 78, "column": 8}}, "18": {"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": 50}}, "19": {"start": {"line": 81, "column": 6}, "end": {"line": 86, "column": 8}}, "20": {"start": {"line": 91, "column": 4}, "end": {"line": 102, "column": 5}}, "21": {"start": {"line": 92, "column": 6}, "end": {"line": 92, "column": 58}}, "22": {"start": {"line": 94, "column": 6}, "end": {"line": 101, "column": 8}}, "23": {"start": {"line": 108, "column": 18}, "end": {"line": 108, "column": 43}}, "24": {"start": {"line": 109, "column": 22}, "end": {"line": 109, "column": 61}}, "25": {"start": {"line": 113, "column": 6}, "end": {"line": 113, "column": 67}}, "26": {"start": {"line": 114, "column": 21}, "end": {"line": 114, "column": 78}}, "27": {"start": {"line": 117, "column": 15}, "end": {"line": 121, "column": 5}}, "28": {"start": {"line": 124, "column": 22}, "end": {"line": 124, "column": 64}}, "29": {"start": {"line": 126, "column": 4}, "end": {"line": 138, "column": 6}}, "30": {"start": {"line": 146, "column": 4}, "end": {"line": 165, "column": 5}}, "31": {"start": {"line": 147, "column": 22}, "end": {"line": 147, "column": 59}}, "32": {"start": {"line": 149, "column": 29}, "end": {"line": 149, "column": 43}}, "33": {"start": {"line": 150, "column": 24}, "end": {"line": 150, "column": 50}}, "34": {"start": {"line": 151, "column": 24}, "end": {"line": 151, "column": 48}}, "35": {"start": {"line": 153, "column": 6}, "end": {"line": 157, "column": 8}}, "36": {"start": {"line": 159, "column": 6}, "end": {"line": 159, "column": 64}}, "37": {"start": {"line": 160, "column": 6}, "end": {"line": 164, "column": 8}}, "38": {"start": {"line": 169, "column": 4}, "end": {"line": 209, "column": 5}}, "39": {"start": {"line": 171, "column": 21}, "end": {"line": 171, "column": 63}}, "40": {"start": {"line": 177, "column": 27}, "end": {"line": 190, "column": 7}}, "41": {"start": {"line": 191, "column": 27}, "end": {"line": 194, "column": 11}}, "42": {"start": {"line": 192, "column": 22}, "end": {"line": 192, "column": 55}}, "43": {"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": 58}}, "44": {"start": {"line": 198, "column": 26}, "end": {"line": 198, "column": 53}}, "45": {"start": {"line": 200, "column": 6}, "end": {"line": 202, "column": 7}}, "46": {"start": {"line": 201, "column": 8}, "end": {"line": 201, "column": 20}}, "47": {"start": {"line": 205, "column": 6}, "end": {"line": 205, "column": 23}}, "48": {"start": {"line": 207, "column": 6}, "end": {"line": 207, "column": 48}}, "49": {"start": {"line": 208, "column": 6}, "end": {"line": 208, "column": 23}}, "50": {"start": {"line": 216, "column": 4}, "end": {"line": 239, "column": 5}}, "51": {"start": {"line": 217, "column": 20}, "end": {"line": 217, "column": 77}}, "52": {"start": {"line": 220, "column": 6}, "end": {"line": 222, "column": 7}}, "53": {"start": {"line": 221, "column": 8}, "end": {"line": 221, "column": 62}}, "54": {"start": {"line": 225, "column": 24}, "end": {"line": 225, "column": 63}}, "55": {"start": {"line": 226, "column": 6}, "end": {"line": 229, "column": 7}}, "56": {"start": {"line": 227, "column": 24}, "end": {"line": 227, "column": 57}}, "57": {"start": {"line": 228, "column": 8}, "end": {"line": 228, "column": 65}}, "58": {"start": {"line": 231, "column": 6}, "end": {"line": 233, "column": 8}}, "59": {"start": {"line": 235, "column": 6}, "end": {"line": 235, "column": 62}}, "60": {"start": {"line": 236, "column": 6}, "end": {"line": 238, "column": 8}}, "61": {"start": {"line": 243, "column": 4}, "end": {"line": 263, "column": 5}}, "62": {"start": {"line": 244, "column": 20}, "end": {"line": 244, "column": 67}}, "63": {"start": {"line": 244, "column": 55}, "end": {"line": 244, "column": 66}}, "64": {"start": {"line": 245, "column": 6}, "end": {"line": 247, "column": 7}}, "65": {"start": {"line": 246, "column": 8}, "end": {"line": 246, "column": 32}}, "66": {"start": {"line": 249, "column": 24}, "end": {"line": 249, "column": 32}}, "67": {"start": {"line": 250, "column": 24}, "end": {"line": 250, "column": 58}}, "68": {"start": {"line": 251, "column": 22}, "end": {"line": 251, "column": 48}}, "69": {"start": {"line": 253, "column": 6}, "end": {"line": 260, "column": 19}}, "70": {"start": {"line": 258, "column": 22}, "end": {"line": 258, "column": 50}}, "71": {"start": {"line": 262, "column": 6}, "end": {"line": 262, "column": 47}}, "72": {"start": {"line": 267, "column": 23}, "end": {"line": 267, "column": 44}}, "73": {"start": {"line": 268, "column": 19}, "end": {"line": 268, "column": 20}}, "74": {"start": {"line": 269, "column": 24}, "end": {"line": 269, "column": 27}}, "75": {"start": {"line": 271, "column": 4}, "end": {"line": 278, "column": 5}}, "76": {"start": {"line": 272, "column": 20}, "end": {"line": 273, "column": 15}}, "77": {"start": {"line": 274, "column": 6}, "end": {"line": 277, "column": 7}}, "78": {"start": {"line": 275, "column": 8}, "end": {"line": 275, "column": 25}}, "79": {"start": {"line": 276, "column": 8}, "end": {"line": 276, "column": 34}}, "80": {"start": {"line": 280, "column": 4}, "end": {"line": 280, "column": 25}}, "81": {"start": {"line": 284, "column": 4}, "end": {"line": 287, "column": 46}}, "82": {"start": {"line": 287, "column": 22}, "end": {"line": 287, "column": 37}}, "83": {"start": {"line": 295, "column": 21}, "end": {"line": 295, "column": 63}}, "84": {"start": {"line": 296, "column": 22}, "end": {"line": 296, "column": 58}}, "85": {"start": {"line": 297, "column": 4}, "end": {"line": 297, "column": 48}}, "86": {"start": {"line": 301, "column": 21}, "end": {"line": 301, "column": 58}}, "87": {"start": {"line": 302, "column": 25}, "end": {"line": 302, "column": 50}}, "88": {"start": {"line": 303, "column": 4}, "end": {"line": 303, "column": 80}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 3}}, "loc": {"start": {"line": 22, "column": 57}, "end": {"line": 30, "column": 3}}, "line": 22}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": 3}}, "loc": {"start": {"line": 32, "column": 61}, "end": {"line": 50, "column": 3}}, "line": 32}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": 3}}, "loc": {"start": {"line": 60, "column": 34}, "end": {"line": 88, "column": 3}}, "line": 60}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 90, "column": 2}, "end": {"line": 90, "column": 3}}, "loc": {"start": {"line": 90, "column": 69}, "end": {"line": 103, "column": 3}}, "line": 90}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 105, "column": 2}, "end": {"line": 105, "column": 3}}, "loc": {"start": {"line": 107, "column": 31}, "end": {"line": 139, "column": 3}}, "line": 107}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 141, "column": 2}, "end": {"line": 141, "column": 3}}, "loc": {"start": {"line": 145, "column": 5}, "end": {"line": 166, "column": 3}}, "line": 145}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 168, "column": 2}, "end": {"line": 168, "column": 3}}, "loc": {"start": {"line": 168, "column": 66}, "end": {"line": 210, "column": 3}}, "line": 168}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 191, "column": 47}, "end": {"line": 191, "column": 48}}, "loc": {"start": {"line": 191, "column": 64}, "end": {"line": 194, "column": 7}}, "line": 191}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 212, "column": 2}, "end": {"line": 212, "column": 3}}, "loc": {"start": {"line": 215, "column": 30}, "end": {"line": 240, "column": 3}}, "line": 215}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 242, "column": 2}, "end": {"line": 242, "column": 3}}, "loc": {"start": {"line": 242, "column": 55}, "end": {"line": 264, "column": 3}}, "line": 242}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 244, "column": 47}, "end": {"line": 244, "column": 48}}, "loc": {"start": {"line": 244, "column": 55}, "end": {"line": 244, "column": 66}}, "line": 244}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 258, "column": 15}, "end": {"line": 258, "column": 16}}, "loc": {"start": {"line": 258, "column": 22}, "end": {"line": 258, "column": 50}}, "line": 258}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 266, "column": 2}, "end": {"line": 266, "column": 3}}, "loc": {"start": {"line": 266, "column": 51}, "end": {"line": 281, "column": 3}}, "line": 266}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 283, "column": 2}, "end": {"line": 283, "column": 3}}, "loc": {"start": {"line": 283, "column": 43}, "end": {"line": 288, "column": 3}}, "line": 283}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 287, "column": 14}, "end": {"line": 287, "column": 15}}, "loc": {"start": {"line": 287, "column": 22}, "end": {"line": 287, "column": 37}}, "line": 287}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 290, "column": 2}, "end": {"line": 290, "column": 3}}, "loc": {"start": {"line": 294, "column": 12}, "end": {"line": 298, "column": 3}}, "line": 294}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 300, "column": 2}, "end": {"line": 300, "column": 3}}, "loc": {"start": {"line": 300, "column": 64}, "end": {"line": 304, "column": 3}}, "line": 300}}, "branchMap": {"0": {"loc": {"start": {"line": 25, "column": 6}, "end": {"line": 28, "column": 29}}, "type": "binary-expr", "locations": [{"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 25}}, {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 25}}, {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 31}}, {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 29}}], "line": 25}, "1": {"loc": {"start": {"line": 36, "column": 6}, "end": {"line": 38, "column": 7}}, "type": "if", "locations": [{"start": {"line": 36, "column": 6}, "end": {"line": 38, "column": 7}}, {"start": {}, "end": {}}], "line": 36}, "2": {"loc": {"start": {"line": 54, "column": 4}, "end": {"line": 59, "column": 5}}, "type": "default-arg", "locations": [{"start": {"line": 54, "column": 36}, "end": {"line": 59, "column": 5}}], "line": 54}, "3": {"loc": {"start": {"line": 66, "column": 6}, "end": {"line": 68, "column": 7}}, "type": "if", "locations": [{"start": {"line": 66, "column": 6}, "end": {"line": 68, "column": 7}}, {"start": {}, "end": {}}], "line": 66}, "4": {"loc": {"start": {"line": 84, "column": 10}, "end": {"line": 84, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 84, "column": 35}, "end": {"line": 84, "column": 48}}, {"start": {"line": 84, "column": 51}, "end": {"line": 84, "column": 66}}], "line": 84}, "5": {"loc": {"start": {"line": 97, "column": 10}, "end": {"line": 97, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 97, "column": 35}, "end": {"line": 97, "column": 48}}, {"start": {"line": 97, "column": 51}, "end": {"line": 97, "column": 66}}], "line": 97}, "6": {"loc": {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 50}}, "type": "cond-expr", "locations": [{"start": {"line": 100, "column": 33}, "end": {"line": 100, "column": 38}}, {"start": {"line": 100, "column": 41}, "end": {"line": 100, "column": 50}}], "line": 100}, "7": {"loc": {"start": {"line": 113, "column": 6}, "end": {"line": 113, "column": 67}}, "type": "cond-expr", "locations": [{"start": {"line": 113, "column": 28}, "end": {"line": 113, "column": 46}}, {"start": {"line": 113, "column": 49}, "end": {"line": 113, "column": 67}}], "line": 113}, "8": {"loc": {"start": {"line": 114, "column": 21}, "end": {"line": 114, "column": 78}}, "type": "cond-expr", "locations": [{"start": {"line": 114, "column": 53}, "end": {"line": 114, "column": 63}}, {"start": {"line": 114, "column": 66}, "end": {"line": 114, "column": 78}}], "line": 114}, "9": {"loc": {"start": {"line": 193, "column": 24}, "end": {"line": 193, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 193, "column": 24}, "end": {"line": 193, "column": 43}}, {"start": {"line": 193, "column": 47}, "end": {"line": 193, "column": 49}}], "line": 193}, "10": {"loc": {"start": {"line": 200, "column": 6}, "end": {"line": 202, "column": 7}}, "type": "if", "locations": [{"start": {"line": 200, "column": 6}, "end": {"line": 202, "column": 7}}, {"start": {}, "end": {}}], "line": 200}, "11": {"loc": {"start": {"line": 200, "column": 10}, "end": {"line": 200, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 200, "column": 10}, "end": {"line": 200, "column": 26}}, {"start": {"line": 200, "column": 30}, "end": {"line": 200, "column": 42}}], "line": 200}, "12": {"loc": {"start": {"line": 217, "column": 50}, "end": {"line": 217, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 217, "column": 50}, "end": {"line": 217, "column": 66}}, {"start": {"line": 217, "column": 70}, "end": {"line": 217, "column": 76}}], "line": 217}, "13": {"loc": {"start": {"line": 220, "column": 6}, "end": {"line": 222, "column": 7}}, "type": "if", "locations": [{"start": {"line": 220, "column": 6}, "end": {"line": 222, "column": 7}}, {"start": {}, "end": {}}], "line": 220}, "14": {"loc": {"start": {"line": 220, "column": 10}, "end": {"line": 220, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 220, "column": 10}, "end": {"line": 220, "column": 31}}, {"start": {"line": 220, "column": 35}, "end": {"line": 220, "column": 73}}], "line": 220}, "15": {"loc": {"start": {"line": 226, "column": 6}, "end": {"line": 229, "column": 7}}, "type": "if", "locations": [{"start": {"line": 226, "column": 6}, "end": {"line": 229, "column": 7}}, {"start": {}, "end": {}}], "line": 226}, "16": {"loc": {"start": {"line": 245, "column": 6}, "end": {"line": 247, "column": 7}}, "type": "if", "locations": [{"start": {"line": 245, "column": 6}, "end": {"line": 247, "column": 7}}, {"start": {}, "end": {}}], "line": 245}, "17": {"loc": {"start": {"line": 272, "column": 21}, "end": {"line": 272, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 272, "column": 21}, "end": {"line": 272, "column": 66}}, {"start": {"line": 272, "column": 70}, "end": {"line": 272, "column": 72}}], "line": 272}, "18": {"loc": {"start": {"line": 274, "column": 6}, "end": {"line": 277, "column": 7}}, "type": "if", "locations": [{"start": {"line": 274, "column": 6}, "end": {"line": 277, "column": 7}}, {"start": {}, "end": {}}], "line": 274}, "19": {"loc": {"start": {"line": 301, "column": 21}, "end": {"line": 301, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 301, "column": 21}, "end": {"line": 301, "column": 46}}, {"start": {"line": 301, "column": 50}, "end": {"line": 301, "column": 58}}], "line": 301}, "20": {"loc": {"start": {"line": 303, "column": 11}, "end": {"line": 303, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 303, "column": 33}, "end": {"line": 303, "column": 68}}, {"start": {"line": 303, "column": 71}, "end": {"line": 303, "column": 79}}], "line": 303}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "b": {"0": [0, 0, 0, 0], "1": [0, 0], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\reading\\ReadingService.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\services\\reading\\ReadingService.ts", "statementMap": {"0": {"start": {"line": 23, "column": 45}, "end": {"line": 33, "column": 3}}, "1": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 61}}, "2": {"start": {"line": 40, "column": 4}, "end": {"line": 123, "column": 5}}, "3": {"start": {"line": 42, "column": 26}, "end": {"line": 50, "column": 7}}, "4": {"start": {"line": 52, "column": 6}, "end": {"line": 58, "column": 7}}, "5": {"start": {"line": 53, "column": 8}, "end": {"line": 57, "column": 10}}, "6": {"start": {"line": 60, "column": 36}, "end": {"line": 60, "column": 56}}, "7": {"start": {"line": 63, "column": 31}, "end": {"line": 63, "column": 72}}, "8": {"start": {"line": 66, "column": 25}, "end": {"line": 66, "column": 75}}, "9": {"start": {"line": 69, "column": 47}, "end": {"line": 84, "column": 7}}, "10": {"start": {"line": 87, "column": 35}, "end": {"line": 92, "column": 7}}, "11": {"start": {"line": 95, "column": 47}, "end": {"line": 102, "column": 7}}, "12": {"start": {"line": 104, "column": 6}, "end": {"line": 113, "column": 8}}, "13": {"start": {"line": 115, "column": 6}, "end": {"line": 122, "column": 8}}, "14": {"start": {"line": 127, "column": 4}, "end": {"line": 139, "column": 5}}, "15": {"start": {"line": 128, "column": 18}, "end": {"line": 128, "column": 59}}, "16": {"start": {"line": 129, "column": 6}, "end": {"line": 129, "column": 64}}, "17": {"start": {"line": 131, "column": 6}, "end": {"line": 138, "column": 8}}, "18": {"start": {"line": 143, "column": 4}, "end": {"line": 162, "column": 5}}, "19": {"start": {"line": 145, "column": 31}, "end": {"line": 147, "column": 7}}, "20": {"start": {"line": 148, "column": 6}, "end": {"line": 152, "column": 7}}, "21": {"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": 52}}, "22": {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 49}}, "23": {"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 59}}, "24": {"start": {"line": 154, "column": 6}, "end": {"line": 161, "column": 8}}, "25": {"start": {"line": 166, "column": 4}, "end": {"line": 173, "column": 5}}, "26": {"start": {"line": 167, "column": 18}, "end": {"line": 167, "column": 49}}, "27": {"start": {"line": 168, "column": 26}, "end": {"line": 168, "column": 57}}, "28": {"start": {"line": 169, "column": 6}, "end": {"line": 169, "column": 56}}, "29": {"start": {"line": 171, "column": 6}, "end": {"line": 171, "column": 61}}, "30": {"start": {"line": 172, "column": 6}, "end": {"line": 172, "column": 16}}, "31": {"start": {"line": 177, "column": 4}, "end": {"line": 186, "column": 5}}, "32": {"start": {"line": 178, "column": 30}, "end": {"line": 178, "column": 54}}, "33": {"start": {"line": 179, "column": 30}, "end": {"line": 179, "column": 65}}, "34": {"start": {"line": 180, "column": 6}, "end": {"line": 183, "column": 8}}, "35": {"start": {"line": 185, "column": 6}, "end": {"line": 185, "column": 62}}, "36": {"start": {"line": 190, "column": 4}, "end": {"line": 198, "column": 5}}, "37": {"start": {"line": 191, "column": 27}, "end": {"line": 191, "column": 73}}, "38": {"start": {"line": 192, "column": 6}, "end": {"line": 194, "column": 31}}, "39": {"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": 62}}, "40": {"start": {"line": 197, "column": 6}, "end": {"line": 197, "column": 34}}, "41": {"start": {"line": 204, "column": 4}, "end": {"line": 211, "column": 5}}, "42": {"start": {"line": 205, "column": 18}, "end": {"line": 205, "column": 50}}, "43": {"start": {"line": 206, "column": 27}, "end": {"line": 206, "column": 58}}, "44": {"start": {"line": 207, "column": 6}, "end": {"line": 207, "column": 60}}, "45": {"start": {"line": 209, "column": 6}, "end": {"line": 209, "column": 62}}, "46": {"start": {"line": 210, "column": 6}, "end": {"line": 210, "column": 18}}, "47": {"start": {"line": 218, "column": 4}, "end": {"line": 228, "column": 5}}, "48": {"start": {"line": 220, "column": 8}, "end": {"line": 220, "column": 42}}, "49": {"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": 45}}, "50": {"start": {"line": 225, "column": 36}, "end": {"line": 225, "column": 39}}, "51": {"start": {"line": 226, "column": 26}, "end": {"line": 226, "column": 58}}, "52": {"start": {"line": 227, "column": 8}, "end": {"line": 227, "column": 71}}, "53": {"start": {"line": 236, "column": 4}, "end": {"line": 236, "column": 40}}, "54": {"start": {"line": 236, "column": 31}, "end": {"line": 236, "column": 40}}, "55": {"start": {"line": 237, "column": 4}, "end": {"line": 240, "column": 6}}, "56": {"start": {"line": 248, "column": 4}, "end": {"line": 254, "column": 6}}, "57": {"start": {"line": 258, "column": 4}, "end": {"line": 258, "column": 43}}, "58": {"start": {"line": 262, "column": 4}, "end": {"line": 262, "column": 41}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": 3}}, "loc": {"start": {"line": 35, "column": 16}, "end": {"line": 37, "column": 3}}, "line": 35}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 3}}, "loc": {"start": {"line": 39, "column": 71}, "end": {"line": 124, "column": 3}}, "line": 39}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 126, "column": 2}, "end": {"line": 126, "column": 3}}, "loc": {"start": {"line": 126, "column": 72}, "end": {"line": 140, "column": 3}}, "line": 126}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 142, "column": 2}, "end": {"line": 142, "column": 3}}, "loc": {"start": {"line": 142, "column": 70}, "end": {"line": 163, "column": 3}}, "line": 142}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 165, "column": 2}, "end": {"line": 165, "column": 3}}, "loc": {"start": {"line": 165, "column": 74}, "end": {"line": 174, "column": 3}}, "line": 165}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 176, "column": 2}, "end": {"line": 176, "column": 3}}, "loc": {"start": {"line": 176, "column": 74}, "end": {"line": 187, "column": 3}}, "line": 176}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 189, "column": 2}, "end": {"line": 189, "column": 3}}, "loc": {"start": {"line": 189, "column": 48}, "end": {"line": 199, "column": 3}}, "line": 189}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 201, "column": 2}, "end": {"line": 201, "column": 3}}, "loc": {"start": {"line": 203, "column": 37}, "end": {"line": 212, "column": 3}}, "line": 203}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 214, "column": 2}, "end": {"line": 214, "column": 3}}, "loc": {"start": {"line": 217, "column": 12}, "end": {"line": 229, "column": 3}}, "line": 217}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 232, "column": 2}, "end": {"line": 232, "column": 3}}, "loc": {"start": {"line": 235, "column": 12}, "end": {"line": 241, "column": 3}}, "line": 235}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 243, "column": 2}, "end": {"line": 243, "column": 3}}, "loc": {"start": {"line": 247, "column": 21}, "end": {"line": 255, "column": 3}}, "line": 247}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 257, "column": 2}, "end": {"line": 257, "column": 3}}, "loc": {"start": {"line": 257, "column": 64}, "end": {"line": 259, "column": 3}}, "line": 257}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 261, "column": 2}, "end": {"line": 261, "column": 3}}, "loc": {"start": {"line": 261, "column": 44}, "end": {"line": 263, "column": 3}}, "line": 261}}, "branchMap": {"0": {"loc": {"start": {"line": 52, "column": 6}, "end": {"line": 58, "column": 7}}, "type": "if", "locations": [{"start": {"line": 52, "column": 6}, "end": {"line": 58, "column": 7}}, {"start": {}, "end": {}}], "line": 52}, "1": {"loc": {"start": {"line": 52, "column": 10}, "end": {"line": 52, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 52, "column": 10}, "end": {"line": 52, "column": 30}}, {"start": {"line": 52, "column": 34}, "end": {"line": 52, "column": 55}}], "line": 52}, "2": {"loc": {"start": {"line": 55, "column": 10}, "end": {"line": 55, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 55, "column": 10}, "end": {"line": 55, "column": 27}}, {"start": {"line": 55, "column": 31}, "end": {"line": 55, "column": 56}}], "line": 55}, "3": {"loc": {"start": {"line": 69, "column": 47}, "end": {"line": 84, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 69, "column": 47}, "end": {"line": 69, "column": 63}}, {"start": {"line": 69, "column": 67}, "end": {"line": 84, "column": 7}}], "line": 69}, "4": {"loc": {"start": {"line": 96, "column": 21}, "end": {"line": 96, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 96, "column": 21}, "end": {"line": 96, "column": 63}}, {"start": {"line": 96, "column": 67}, "end": {"line": 96, "column": 68}}], "line": 96}, "5": {"loc": {"start": {"line": 100, "column": 11}, "end": {"line": 100, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 11}, "end": {"line": 100, "column": 53}}, {"start": {"line": 100, "column": 57}, "end": {"line": 100, "column": 58}}], "line": 100}, "6": {"loc": {"start": {"line": 101, "column": 24}, "end": {"line": 101, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 101, "column": 24}, "end": {"line": 101, "column": 66}}, {"start": {"line": 101, "column": 70}, "end": {"line": 101, "column": 71}}], "line": 101}, "7": {"loc": {"start": {"line": 118, "column": 10}, "end": {"line": 118, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 118, "column": 35}, "end": {"line": 118, "column": 48}}, {"start": {"line": 118, "column": 51}, "end": {"line": 118, "column": 66}}], "line": 118}, "8": {"loc": {"start": {"line": 121, "column": 8}, "end": {"line": 121, "column": 50}}, "type": "cond-expr", "locations": [{"start": {"line": 121, "column": 33}, "end": {"line": 121, "column": 38}}, {"start": {"line": 121, "column": 41}, "end": {"line": 121, "column": 50}}], "line": 121}, "9": {"loc": {"start": {"line": 134, "column": 10}, "end": {"line": 134, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 134, "column": 35}, "end": {"line": 134, "column": 48}}, {"start": {"line": 134, "column": 51}, "end": {"line": 134, "column": 66}}], "line": 134}, "10": {"loc": {"start": {"line": 137, "column": 8}, "end": {"line": 137, "column": 50}}, "type": "cond-expr", "locations": [{"start": {"line": 137, "column": 33}, "end": {"line": 137, "column": 38}}, {"start": {"line": 137, "column": 41}, "end": {"line": 137, "column": 50}}], "line": 137}, "11": {"loc": {"start": {"line": 148, "column": 6}, "end": {"line": 152, "column": 7}}, "type": "if", "locations": [{"start": {"line": 148, "column": 6}, "end": {"line": 152, "column": 7}}, {"start": {}, "end": {}}], "line": 148}, "12": {"loc": {"start": {"line": 157, "column": 10}, "end": {"line": 157, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 157, "column": 35}, "end": {"line": 157, "column": 48}}, {"start": {"line": 157, "column": 51}, "end": {"line": 157, "column": 66}}], "line": 157}, "13": {"loc": {"start": {"line": 160, "column": 8}, "end": {"line": 160, "column": 50}}, "type": "cond-expr", "locations": [{"start": {"line": 160, "column": 33}, "end": {"line": 160, "column": 38}}, {"start": {"line": 160, "column": 41}, "end": {"line": 160, "column": 50}}], "line": 160}, "14": {"loc": {"start": {"line": 169, "column": 13}, "end": {"line": 169, "column": 55}}, "type": "cond-expr", "locations": [{"start": {"line": 169, "column": 27}, "end": {"line": 169, "column": 50}}, {"start": {"line": 169, "column": 53}, "end": {"line": 169, "column": 55}}], "line": 169}, "15": {"loc": {"start": {"line": 192, "column": 13}, "end": {"line": 194, "column": 30}}, "type": "cond-expr", "locations": [{"start": {"line": 193, "column": 10}, "end": {"line": 193, "column": 66}}, {"start": {"line": 194, "column": 10}, "end": {"line": 194, "column": 30}}], "line": 192}, "16": {"loc": {"start": {"line": 207, "column": 13}, "end": {"line": 207, "column": 59}}, "type": "cond-expr", "locations": [{"start": {"line": 207, "column": 28}, "end": {"line": 207, "column": 52}}, {"start": {"line": 207, "column": 55}, "end": {"line": 207, "column": 59}}], "line": 207}, "17": {"loc": {"start": {"line": 218, "column": 4}, "end": {"line": 228, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 219, "column": 6}, "end": {"line": 220, "column": 42}}, {"start": {"line": 221, "column": 6}, "end": {"line": 222, "column": 45}}, {"start": {"line": 223, "column": 6}, "end": {"line": 227, "column": 71}}], "line": 218}, "18": {"loc": {"start": {"line": 220, "column": 15}, "end": {"line": 220, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 220, "column": 15}, "end": {"line": 220, "column": 36}}, {"start": {"line": 220, "column": 40}, "end": {"line": 220, "column": 41}}], "line": 220}, "19": {"loc": {"start": {"line": 222, "column": 15}, "end": {"line": 222, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 222, "column": 15}, "end": {"line": 222, "column": 39}}, {"start": {"line": 222, "column": 43}, "end": {"line": 222, "column": 44}}], "line": 222}, "20": {"loc": {"start": {"line": 236, "column": 4}, "end": {"line": 236, "column": 40}}, "type": "if", "locations": [{"start": {"line": 236, "column": 4}, "end": {"line": 236, "column": 40}}, {"start": {}, "end": {}}], "line": 236}, "21": {"loc": {"start": {"line": 258, "column": 11}, "end": {"line": 258, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 258, "column": 11}, "end": {"line": 258, "column": 20}}, {"start": {"line": 258, "column": 24}, "end": {"line": 258, "column": 42}}], "line": 258}, "22": {"loc": {"start": {"line": 262, "column": 11}, "end": {"line": 262, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 262, "column": 11}, "end": {"line": 262, "column": 23}}, {"start": {"line": 262, "column": 27}, "end": {"line": 262, "column": 40}}], "line": 262}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\store\\index.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\store\\index.ts", "statementMap": {"0": {"start": {"line": 18, "column": 20}, "end": {"line": 23, "column": 2}}, "1": {"start": {"line": 26, "column": 22}, "end": {"line": 31, "column": 1}}, "2": {"start": {"line": 33, "column": 25}, "end": {"line": 33, "column": 67}}, "3": {"start": {"line": 36, "column": 21}, "end": {"line": 45, "column": 2}}, "4": {"start": {"line": 39, "column": 4}, "end": {"line": 43, "column": 6}}, "5": {"start": {"line": 48, "column": 25}, "end": {"line": 48, "column": 44}}, "6": {"start": {"line": 55, "column": 30}, "end": {"line": 55, "column": 62}}, "7": {"start": {"line": 55, "column": 36}, "end": {"line": 55, "column": 62}}, "8": {"start": {"line": 56, "column": 63}, "end": {"line": 56, "column": 74}}, "9": {"start": {"line": 59, "column": 29}, "end": {"line": 59, "column": 51}}, "10": {"start": {"line": 59, "column": 35}, "end": {"line": 59, "column": 51}}, "11": {"start": {"line": 60, "column": 30}, "end": {"line": 60, "column": 69}}, "12": {"start": {"line": 60, "column": 47}, "end": {"line": 60, "column": 69}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 38, "column": 14}, "end": {"line": 38, "column": 15}}, "loc": {"start": {"line": 39, "column": 4}, "end": {"line": 43, "column": 6}}, "line": 39}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 55, "column": 30}, "end": {"line": 55, "column": 31}}, "loc": {"start": {"line": 55, "column": 36}, "end": {"line": 55, "column": 62}}, "line": 55}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 59, "column": 29}, "end": {"line": 59, "column": 30}}, "loc": {"start": {"line": 59, "column": 35}, "end": {"line": 59, "column": 51}}, "line": 59}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 60, "column": 30}, "end": {"line": 60, "column": 31}}, "loc": {"start": {"line": 60, "column": 47}, "end": {"line": 60, "column": 69}}, "line": 60}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\store\\rootReducer.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\store\\rootReducer.ts", "statementMap": {"0": {"start": {"line": 18, "column": 27}, "end": {"line": 28, "column": 1}}, "1": {"start": {"line": 22, "column": 2}, "end": {"line": 27, "column": 3}}, "2": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 55}}, "3": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 19}}, "4": {"start": {"line": 31, "column": 20}, "end": {"line": 45, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 18, "column": 27}, "end": {"line": 18, "column": 28}}, "loc": {"start": {"line": 21, "column": 5}, "end": {"line": 28, "column": 1}}, "line": 21}}, "branchMap": {"0": {"loc": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 31}}, "type": "default-arg", "locations": [{"start": {"line": 19, "column": 10}, "end": {"line": 19, "column": 31}}], "line": 19}, "1": {"loc": {"start": {"line": 22, "column": 2}, "end": {"line": 27, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 23, "column": 4}, "end": {"line": 24, "column": 55}}, {"start": {"line": 25, "column": 4}, "end": {"line": 26, "column": 19}}], "line": 22}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {"0": 0}, "b": {"0": [0], "1": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\store\\slices\\annotationSlice.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\store\\slices\\annotationSlice.ts", "statementMap": {"0": {"start": {"line": 57, "column": 38}, "end": {"line": 85, "column": 1}}, "1": {"start": {"line": 88, "column": 42}, "end": {"line": 96, "column": 1}}, "2": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": 59}}, "3": {"start": {"line": 98, "column": 32}, "end": {"line": 114, "column": 1}}, "4": {"start": {"line": 106, "column": 35}, "end": {"line": 111, "column": 19}}, "5": {"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": 22}}, "6": {"start": {"line": 116, "column": 32}, "end": {"line": 124, "column": 1}}, "7": {"start": {"line": 122, "column": 4}, "end": {"line": 122, "column": 27}}, "8": {"start": {"line": 126, "column": 32}, "end": {"line": 134, "column": 1}}, "9": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 14}}, "10": {"start": {"line": 136, "column": 33}, "end": {"line": 148, "column": 1}}, "11": {"start": {"line": 142, "column": 4}, "end": {"line": 146, "column": 32}}, "12": {"start": {"line": 150, "column": 33}, "end": {"line": 164, "column": 1}}, "13": {"start": {"line": 162, "column": 4}, "end": {"line": 162, "column": 43}}, "14": {"start": {"line": 167, "column": 24}, "end": {"line": 382, "column": 2}}, "15": {"start": {"line": 173, "column": 6}, "end": {"line": 173, "column": 47}}, "16": {"start": {"line": 174, "column": 6}, "end": {"line": 174, "column": 40}}, "17": {"start": {"line": 175, "column": 6}, "end": {"line": 175, "column": 35}}, "18": {"start": {"line": 179, "column": 6}, "end": {"line": 179, "column": 50}}, "19": {"start": {"line": 186, "column": 6}, "end": {"line": 186, "column": 45}}, "20": {"start": {"line": 187, "column": 6}, "end": {"line": 189, "column": 7}}, "21": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 42}}, "22": {"start": {"line": 193, "column": 6}, "end": {"line": 193, "column": 44}}, "23": {"start": {"line": 197, "column": 6}, "end": {"line": 197, "column": 49}}, "24": {"start": {"line": 201, "column": 6}, "end": {"line": 201, "column": 40}}, "25": {"start": {"line": 205, "column": 6}, "end": {"line": 205, "column": 41}}, "26": {"start": {"line": 206, "column": 6}, "end": {"line": 206, "column": 35}}, "27": {"start": {"line": 214, "column": 6}, "end": {"line": 214, "column": 44}}, "28": {"start": {"line": 218, "column": 6}, "end": {"line": 218, "column": 33}}, "29": {"start": {"line": 219, "column": 6}, "end": {"line": 219, "column": 34}}, "30": {"start": {"line": 224, "column": 6}, "end": {"line": 224, "column": 25}}, "31": {"start": {"line": 228, "column": 6}, "end": {"line": 228, "column": 35}}, "32": {"start": {"line": 229, "column": 6}, "end": {"line": 229, "column": 39}}, "33": {"start": {"line": 234, "column": 6}, "end": {"line": 234, "column": 41}}, "34": {"start": {"line": 239, "column": 4}, "end": {"line": 266, "column": 9}}, "35": {"start": {"line": 241, "column": 8}, "end": {"line": 241, "column": 31}}, "36": {"start": {"line": 242, "column": 8}, "end": {"line": 242, "column": 27}}, "37": {"start": {"line": 245, "column": 8}, "end": {"line": 245, "column": 32}}, "38": {"start": {"line": 246, "column": 44}, "end": {"line": 246, "column": 58}}, "39": {"start": {"line": 249, "column": 28}, "end": {"line": 249, "column": 71}}, "40": {"start": {"line": 250, "column": 8}, "end": {"line": 252, "column": 11}}, "41": {"start": {"line": 251, "column": 10}, "end": {"line": 251, "column": 39}}, "42": {"start": {"line": 255, "column": 40}, "end": {"line": 255, "column": 42}}, "43": {"start": {"line": 256, "column": 8}, "end": {"line": 259, "column": 11}}, "44": {"start": {"line": 257, "column": 10}, "end": {"line": 257, "column": 56}}, "45": {"start": {"line": 258, "column": 10}, "end": {"line": 258, "column": 44}}, "46": {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 62}}, "47": {"start": {"line": 264, "column": 8}, "end": {"line": 264, "column": 32}}, "48": {"start": {"line": 265, "column": 8}, "end": {"line": 265, "column": 75}}, "49": {"start": {"line": 269, "column": 4}, "end": {"line": 292, "column": 9}}, "50": {"start": {"line": 271, "column": 8}, "end": {"line": 271, "column": 30}}, "51": {"start": {"line": 272, "column": 8}, "end": {"line": 272, "column": 27}}, "52": {"start": {"line": 275, "column": 8}, "end": {"line": 275, "column": 31}}, "53": {"start": {"line": 276, "column": 8}, "end": {"line": 276, "column": 43}}, "54": {"start": {"line": 277, "column": 8}, "end": {"line": 277, "column": 37}}, "55": {"start": {"line": 279, "column": 27}, "end": {"line": 279, "column": 41}}, "56": {"start": {"line": 280, "column": 8}, "end": {"line": 280, "column": 54}}, "57": {"start": {"line": 283, "column": 27}, "end": {"line": 283, "column": 48}}, "58": {"start": {"line": 284, "column": 8}, "end": {"line": 286, "column": 9}}, "59": {"start": {"line": 285, "column": 10}, "end": {"line": 285, "column": 53}}, "60": {"start": {"line": 287, "column": 8}, "end": {"line": 287, "column": 66}}, "61": {"start": {"line": 290, "column": 8}, "end": {"line": 290, "column": 31}}, "62": {"start": {"line": 291, "column": 8}, "end": {"line": 291, "column": 76}}, "63": {"start": {"line": 295, "column": 4}, "end": {"line": 316, "column": 9}}, "64": {"start": {"line": 297, "column": 8}, "end": {"line": 297, "column": 30}}, "65": {"start": {"line": 298, "column": 8}, "end": {"line": 298, "column": 27}}, "66": {"start": {"line": 301, "column": 8}, "end": {"line": 301, "column": 31}}, "67": {"start": {"line": 302, "column": 32}, "end": {"line": 302, "column": 46}}, "68": {"start": {"line": 304, "column": 8}, "end": {"line": 311, "column": 9}}, "69": {"start": {"line": 305, "column": 37}, "end": {"line": 305, "column": 58}}, "70": {"start": {"line": 306, "column": 10}, "end": {"line": 310, "column": 26}}, "71": {"start": {"line": 314, "column": 8}, "end": {"line": 314, "column": 31}}, "72": {"start": {"line": 315, "column": 8}, "end": {"line": 315, "column": 76}}, "73": {"start": {"line": 319, "column": 4}, "end": {"line": 350, "column": 9}}, "74": {"start": {"line": 321, "column": 8}, "end": {"line": 321, "column": 32}}, "75": {"start": {"line": 322, "column": 8}, "end": {"line": 322, "column": 27}}, "76": {"start": {"line": 325, "column": 8}, "end": {"line": 325, "column": 33}}, "77": {"start": {"line": 326, "column": 19}, "end": {"line": 326, "column": 33}}, "78": {"start": {"line": 329, "column": 27}, "end": {"line": 329, "column": 48}}, "79": {"start": {"line": 330, "column": 8}, "end": {"line": 345, "column": 9}}, "80": {"start": {"line": 331, "column": 10}, "end": {"line": 331, "column": 39}}, "81": {"start": {"line": 334, "column": 29}, "end": {"line": 334, "column": 50}}, "82": {"start": {"line": 335, "column": 10}, "end": {"line": 339, "column": 11}}, "83": {"start": {"line": 336, "column": 12}, "end": {"line": 338, "column": 58}}, "84": {"start": {"line": 338, "column": 37}, "end": {"line": 338, "column": 56}}, "85": {"start": {"line": 342, "column": 10}, "end": {"line": 344, "column": 11}}, "86": {"start": {"line": 343, "column": 12}, "end": {"line": 343, "column": 46}}, "87": {"start": {"line": 348, "column": 8}, "end": {"line": 348, "column": 33}}, "88": {"start": {"line": 349, "column": 8}, "end": {"line": 349, "column": 76}}, "89": {"start": {"line": 353, "column": 4}, "end": {"line": 365, "column": 9}}, "90": {"start": {"line": 355, "column": 8}, "end": {"line": 355, "column": 33}}, "91": {"start": {"line": 356, "column": 8}, "end": {"line": 356, "column": 27}}, "92": {"start": {"line": 359, "column": 8}, "end": {"line": 359, "column": 34}}, "93": {"start": {"line": 360, "column": 8}, "end": {"line": 360, "column": 45}}, "94": {"start": {"line": 363, "column": 8}, "end": {"line": 363, "column": 34}}, "95": {"start": {"line": 364, "column": 8}, "end": {"line": 364, "column": 77}}, "96": {"start": {"line": 368, "column": 4}, "end": {"line": 380, "column": 9}}, "97": {"start": {"line": 370, "column": 8}, "end": {"line": 370, "column": 33}}, "98": {"start": {"line": 371, "column": 8}, "end": {"line": 371, "column": 27}}, "99": {"start": {"line": 374, "column": 8}, "end": {"line": 374, "column": 34}}, "100": {"start": {"line": 375, "column": 8}, "end": {"line": 375, "column": 46}}, "101": {"start": {"line": 378, "column": 8}, "end": {"line": 378, "column": 34}}, "102": {"start": {"line": 379, "column": 8}, "end": {"line": 379, "column": 77}}, "103": {"start": {"line": 398, "column": 4}, "end": {"line": 398, "column": 27}}, "104": {"start": {"line": 401, "column": 39}, "end": {"line": 403, "column": 40}}, "105": {"start": {"line": 403, "column": 6}, "end": {"line": 403, "column": 40}}, "106": {"start": {"line": 405, "column": 51}, "end": {"line": 414, "column": 1}}, "107": {"start": {"line": 409, "column": 4}, "end": {"line": 409, "column": 20}}, "108": {"start": {"line": 410, "column": 2}, "end": {"line": 410, "column": 36}}, "109": {"start": {"line": 410, "column": 26}, "end": {"line": 410, "column": 36}}, "110": {"start": {"line": 412, "column": 24}, "end": {"line": 412, "column": 68}}, "111": {"start": {"line": 413, "column": 2}, "end": {"line": 413, "column": 66}}, "112": {"start": {"line": 413, "column": 33}, "end": {"line": 413, "column": 48}}, "113": {"start": {"line": 416, "column": 40}, "end": {"line": 421, "column": 1}}, "114": {"start": {"line": 419, "column": 48}, "end": {"line": 419, "column": 64}}, "115": {"start": {"line": 420, "column": 2}, "end": {"line": 420, "column": 73}}, "116": {"start": {"line": 423, "column": 37}, "end": {"line": 424, "column": 34}}, "117": {"start": {"line": 424, "column": 2}, "end": {"line": 424, "column": 34}}, "118": {"start": {"line": 426, "column": 36}, "end": {"line": 427, "column": 33}}, "119": {"start": {"line": 427, "column": 2}, "end": {"line": 427, "column": 33}}, "120": {"start": {"line": 429, "column": 42}, "end": {"line": 431, "column": 43}}, "121": {"start": {"line": 431, "column": 6}, "end": {"line": 431, "column": 43}}, "122": {"start": {"line": 433, "column": 41}, "end": {"line": 435, "column": 42}}, "123": {"start": {"line": 435, "column": 6}, "end": {"line": 435, "column": 42}}, "124": {"start": {"line": 437, "column": 35}, "end": {"line": 438, "column": 32}}, "125": {"start": {"line": 438, "column": 2}, "end": {"line": 438, "column": 32}}, "126": {"start": {"line": 440, "column": 31}, "end": {"line": 441, "column": 28}}, "127": {"start": {"line": 441, "column": 2}, "end": {"line": 441, "column": 28}}, "128": {"start": {"line": 443, "column": 27}, "end": {"line": 444, "column": 24}}, "129": {"start": {"line": 444, "column": 2}, "end": {"line": 444, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 90, "column": 2}, "end": {"line": 90, "column": 3}}, "loc": {"start": {"line": 90, "column": 32}, "end": {"line": 95, "column": 3}}, "line": 90}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 100, "column": 2}, "end": {"line": 100, "column": 3}}, "loc": {"start": {"line": 102, "column": 7}, "end": {"line": 113, "column": 3}}, "line": 102}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 118, "column": 2}, "end": {"line": 118, "column": 3}}, "loc": {"start": {"line": 118, "column": 75}, "end": {"line": 123, "column": 3}}, "line": 118}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 128, "column": 2}, "end": {"line": 128, "column": 3}}, "loc": {"start": {"line": 128, "column": 24}, "end": {"line": 133, "column": 3}}, "line": 128}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": 3}}, "loc": {"start": {"line": 138, "column": 49}, "end": {"line": 147, "column": 3}}, "line": 138}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 152, "column": 2}, "end": {"line": 152, "column": 3}}, "loc": {"start": {"line": 158, "column": 8}, "end": {"line": 163, "column": 3}}, "line": 158}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 172, "column": 24}, "end": {"line": 172, "column": 25}}, "loc": {"start": {"line": 172, "column": 66}, "end": {"line": 176, "column": 5}}, "line": 172}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 178, "column": 27}, "end": {"line": 178, "column": 28}}, "loc": {"start": {"line": 178, "column": 76}, "end": {"line": 180, "column": 5}}, "line": 178}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 182, "column": 24}, "end": {"line": 182, "column": 25}}, "loc": {"start": {"line": 185, "column": 9}, "end": {"line": 190, "column": 5}}, "line": 185}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 192, "column": 23}, "end": {"line": 192, "column": 24}}, "loc": {"start": {"line": 192, "column": 73}, "end": {"line": 194, "column": 5}}, "line": 192}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 196, "column": 28}, "end": {"line": 196, "column": 29}}, "loc": {"start": {"line": 196, "column": 71}, "end": {"line": 198, "column": 5}}, "line": 196}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 200, "column": 29}, "end": {"line": 200, "column": 30}}, "loc": {"start": {"line": 200, "column": 38}, "end": {"line": 202, "column": 5}}, "line": 200}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 204, "column": 30}, "end": {"line": 204, "column": 31}}, "loc": {"start": {"line": 204, "column": 39}, "end": {"line": 207, "column": 5}}, "line": 204}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 210, "column": 23}, "end": {"line": 210, "column": 24}}, "loc": {"start": {"line": 213, "column": 9}, "end": {"line": 215, "column": 5}}, "line": 213}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 217, "column": 24}, "end": {"line": 217, "column": 25}}, "loc": {"start": {"line": 217, "column": 33}, "end": {"line": 220, "column": 5}}, "line": 217}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 223, "column": 16}, "end": {"line": 223, "column": 17}}, "loc": {"start": {"line": 223, "column": 25}, "end": {"line": 225, "column": 5}}, "line": 223}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 227, "column": 14}, "end": {"line": 227, "column": 15}}, "loc": {"start": {"line": 227, "column": 56}, "end": {"line": 230, "column": 5}}, "line": 227}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 233, "column": 26}, "end": {"line": 233, "column": 27}}, "loc": {"start": {"line": 233, "column": 35}, "end": {"line": 235, "column": 5}}, "line": 233}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 237, "column": 17}, "end": {"line": 237, "column": 18}}, "loc": {"start": {"line": 237, "column": 28}, "end": {"line": 381, "column": 3}}, "line": 237}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 240, "column": 51}, "end": {"line": 240, "column": 52}}, "loc": {"start": {"line": 240, "column": 60}, "end": {"line": 243, "column": 7}}, "line": 240}, "20": {"name": "(anonymous_20)", "decl": {"start": {"line": 244, "column": 53}, "end": {"line": 244, "column": 54}}, "loc": {"start": {"line": 244, "column": 72}, "end": {"line": 262, "column": 7}}, "line": 244}, "21": {"name": "(anonymous_21)", "decl": {"start": {"line": 250, "column": 28}, "end": {"line": 250, "column": 29}}, "loc": {"start": {"line": 250, "column": 34}, "end": {"line": 252, "column": 9}}, "line": 250}, "22": {"name": "(anonymous_22)", "decl": {"start": {"line": 256, "column": 28}, "end": {"line": 256, "column": 29}}, "loc": {"start": {"line": 256, "column": 42}, "end": {"line": 259, "column": 9}}, "line": 256}, "23": {"name": "(anonymous_23)", "decl": {"start": {"line": 263, "column": 52}, "end": {"line": 263, "column": 53}}, "loc": {"start": {"line": 263, "column": 71}, "end": {"line": 266, "column": 7}}, "line": 263}, "24": {"name": "(anonymous_24)", "decl": {"start": {"line": 270, "column": 41}, "end": {"line": 270, "column": 42}}, "loc": {"start": {"line": 270, "column": 50}, "end": {"line": 273, "column": 7}}, "line": 270}, "25": {"name": "(anonymous_25)", "decl": {"start": {"line": 274, "column": 43}, "end": {"line": 274, "column": 44}}, "loc": {"start": {"line": 274, "column": 62}, "end": {"line": 288, "column": 7}}, "line": 274}, "26": {"name": "(anonymous_26)", "decl": {"start": {"line": 289, "column": 42}, "end": {"line": 289, "column": 43}}, "loc": {"start": {"line": 289, "column": 61}, "end": {"line": 292, "column": 7}}, "line": 289}, "27": {"name": "(anonymous_27)", "decl": {"start": {"line": 296, "column": 41}, "end": {"line": 296, "column": 42}}, "loc": {"start": {"line": 296, "column": 50}, "end": {"line": 299, "column": 7}}, "line": 296}, "28": {"name": "(anonymous_28)", "decl": {"start": {"line": 300, "column": 43}, "end": {"line": 300, "column": 44}}, "loc": {"start": {"line": 300, "column": 62}, "end": {"line": 312, "column": 7}}, "line": 300}, "29": {"name": "(anonymous_29)", "decl": {"start": {"line": 313, "column": 42}, "end": {"line": 313, "column": 43}}, "loc": {"start": {"line": 313, "column": 61}, "end": {"line": 316, "column": 7}}, "line": 313}, "30": {"name": "(anonymous_30)", "decl": {"start": {"line": 320, "column": 41}, "end": {"line": 320, "column": 42}}, "loc": {"start": {"line": 320, "column": 50}, "end": {"line": 323, "column": 7}}, "line": 320}, "31": {"name": "(anonymous_31)", "decl": {"start": {"line": 324, "column": 43}, "end": {"line": 324, "column": 44}}, "loc": {"start": {"line": 324, "column": 62}, "end": {"line": 346, "column": 7}}, "line": 324}, "32": {"name": "(anonymous_32)", "decl": {"start": {"line": 338, "column": 21}, "end": {"line": 338, "column": 22}}, "loc": {"start": {"line": 338, "column": 37}, "end": {"line": 338, "column": 56}}, "line": 338}, "33": {"name": "(anonymous_33)", "decl": {"start": {"line": 347, "column": 42}, "end": {"line": 347, "column": 43}}, "loc": {"start": {"line": 347, "column": 61}, "end": {"line": 350, "column": 7}}, "line": 347}, "34": {"name": "(anonymous_34)", "decl": {"start": {"line": 354, "column": 42}, "end": {"line": 354, "column": 43}}, "loc": {"start": {"line": 354, "column": 51}, "end": {"line": 357, "column": 7}}, "line": 354}, "35": {"name": "(anonymous_35)", "decl": {"start": {"line": 358, "column": 44}, "end": {"line": 358, "column": 45}}, "loc": {"start": {"line": 358, "column": 63}, "end": {"line": 361, "column": 7}}, "line": 358}, "36": {"name": "(anonymous_36)", "decl": {"start": {"line": 362, "column": 43}, "end": {"line": 362, "column": 44}}, "loc": {"start": {"line": 362, "column": 62}, "end": {"line": 365, "column": 7}}, "line": 362}, "37": {"name": "(anonymous_37)", "decl": {"start": {"line": 369, "column": 42}, "end": {"line": 369, "column": 43}}, "loc": {"start": {"line": 369, "column": 51}, "end": {"line": 372, "column": 7}}, "line": 369}, "38": {"name": "(anonymous_38)", "decl": {"start": {"line": 373, "column": 44}, "end": {"line": 373, "column": 45}}, "loc": {"start": {"line": 373, "column": 63}, "end": {"line": 376, "column": 7}}, "line": 373}, "39": {"name": "(anonymous_39)", "decl": {"start": {"line": 377, "column": 43}, "end": {"line": 377, "column": 44}}, "loc": {"start": {"line": 377, "column": 62}, "end": {"line": 380, "column": 7}}, "line": 377}, "40": {"name": "(anonymous_40)", "decl": {"start": {"line": 401, "column": 39}, "end": {"line": 401, "column": 40}}, "loc": {"start": {"line": 403, "column": 6}, "end": {"line": 403, "column": 40}}, "line": 403}, "41": {"name": "(anonymous_41)", "decl": {"start": {"line": 405, "column": 51}, "end": {"line": 405, "column": 52}}, "loc": {"start": {"line": 407, "column": 6}, "end": {"line": 414, "column": 1}}, "line": 407}, "42": {"name": "(anonymous_42)", "decl": {"start": {"line": 413, "column": 27}, "end": {"line": 413, "column": 28}}, "loc": {"start": {"line": 413, "column": 33}, "end": {"line": 413, "column": 48}}, "line": 413}, "43": {"name": "(anonymous_43)", "decl": {"start": {"line": 416, "column": 40}, "end": {"line": 416, "column": 41}}, "loc": {"start": {"line": 418, "column": 6}, "end": {"line": 421, "column": 1}}, "line": 418}, "44": {"name": "(anonymous_44)", "decl": {"start": {"line": 423, "column": 37}, "end": {"line": 423, "column": 38}}, "loc": {"start": {"line": 424, "column": 2}, "end": {"line": 424, "column": 34}}, "line": 424}, "45": {"name": "(anonymous_45)", "decl": {"start": {"line": 426, "column": 36}, "end": {"line": 426, "column": 37}}, "loc": {"start": {"line": 427, "column": 2}, "end": {"line": 427, "column": 33}}, "line": 427}, "46": {"name": "(anonymous_46)", "decl": {"start": {"line": 429, "column": 42}, "end": {"line": 429, "column": 43}}, "loc": {"start": {"line": 431, "column": 6}, "end": {"line": 431, "column": 43}}, "line": 431}, "47": {"name": "(anonymous_47)", "decl": {"start": {"line": 433, "column": 41}, "end": {"line": 433, "column": 42}}, "loc": {"start": {"line": 435, "column": 6}, "end": {"line": 435, "column": 42}}, "line": 435}, "48": {"name": "(anonymous_48)", "decl": {"start": {"line": 437, "column": 35}, "end": {"line": 437, "column": 36}}, "loc": {"start": {"line": 438, "column": 2}, "end": {"line": 438, "column": 32}}, "line": 438}, "49": {"name": "(anonymous_49)", "decl": {"start": {"line": 440, "column": 31}, "end": {"line": 440, "column": 32}}, "loc": {"start": {"line": 441, "column": 2}, "end": {"line": 441, "column": 28}}, "line": 441}, "50": {"name": "(anonymous_50)", "decl": {"start": {"line": 443, "column": 27}, "end": {"line": 443, "column": 28}}, "loc": {"start": {"line": 444, "column": 2}, "end": {"line": 444, "column": 24}}, "line": 444}}, "branchMap": {"0": {"loc": {"start": {"line": 187, "column": 6}, "end": {"line": 189, "column": 7}}, "type": "if", "locations": [{"start": {"line": 187, "column": 6}, "end": {"line": 189, "column": 7}}, {"start": {}, "end": {}}], "line": 187}, "1": {"loc": {"start": {"line": 249, "column": 28}, "end": {"line": 249, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 249, "column": 28}, "end": {"line": 249, "column": 65}}, {"start": {"line": 249, "column": 69}, "end": {"line": 249, "column": 71}}], "line": 249}, "2": {"loc": {"start": {"line": 265, "column": 22}, "end": {"line": 265, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 265, "column": 22}, "end": {"line": 265, "column": 42}}, {"start": {"line": 265, "column": 46}, "end": {"line": 265, "column": 74}}], "line": 265}, "3": {"loc": {"start": {"line": 284, "column": 8}, "end": {"line": 286, "column": 9}}, "type": "if", "locations": [{"start": {"line": 284, "column": 8}, "end": {"line": 286, "column": 9}}, {"start": {}, "end": {}}], "line": 284}, "4": {"loc": {"start": {"line": 291, "column": 22}, "end": {"line": 291, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 291, "column": 22}, "end": {"line": 291, "column": 42}}, {"start": {"line": 291, "column": 46}, "end": {"line": 291, "column": 75}}], "line": 291}, "5": {"loc": {"start": {"line": 304, "column": 8}, "end": {"line": 311, "column": 9}}, "type": "if", "locations": [{"start": {"line": 304, "column": 8}, "end": {"line": 311, "column": 9}}, {"start": {}, "end": {}}], "line": 304}, "6": {"loc": {"start": {"line": 315, "column": 22}, "end": {"line": 315, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 315, "column": 22}, "end": {"line": 315, "column": 42}}, {"start": {"line": 315, "column": 46}, "end": {"line": 315, "column": 75}}], "line": 315}, "7": {"loc": {"start": {"line": 330, "column": 8}, "end": {"line": 345, "column": 9}}, "type": "if", "locations": [{"start": {"line": 330, "column": 8}, "end": {"line": 345, "column": 9}}, {"start": {}, "end": {}}], "line": 330}, "8": {"loc": {"start": {"line": 335, "column": 10}, "end": {"line": 339, "column": 11}}, "type": "if", "locations": [{"start": {"line": 335, "column": 10}, "end": {"line": 339, "column": 11}}, {"start": {}, "end": {}}], "line": 335}, "9": {"loc": {"start": {"line": 342, "column": 10}, "end": {"line": 344, "column": 11}}, "type": "if", "locations": [{"start": {"line": 342, "column": 10}, "end": {"line": 344, "column": 11}}, {"start": {}, "end": {}}], "line": 342}, "10": {"loc": {"start": {"line": 349, "column": 22}, "end": {"line": 349, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 349, "column": 22}, "end": {"line": 349, "column": 42}}, {"start": {"line": 349, "column": 46}, "end": {"line": 349, "column": 75}}], "line": 349}, "11": {"loc": {"start": {"line": 364, "column": 22}, "end": {"line": 364, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 364, "column": 22}, "end": {"line": 364, "column": 42}}, {"start": {"line": 364, "column": 46}, "end": {"line": 364, "column": 76}}], "line": 364}, "12": {"loc": {"start": {"line": 379, "column": 22}, "end": {"line": 379, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 379, "column": 22}, "end": {"line": 379, "column": 42}}, {"start": {"line": 379, "column": 46}, "end": {"line": 379, "column": 76}}], "line": 379}, "13": {"loc": {"start": {"line": 410, "column": 2}, "end": {"line": 410, "column": 36}}, "type": "if", "locations": [{"start": {"line": 410, "column": 2}, "end": {"line": 410, "column": 36}}, {"start": {}, "end": {}}], "line": 410}, "14": {"loc": {"start": {"line": 412, "column": 24}, "end": {"line": 412, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 412, "column": 24}, "end": {"line": 412, "column": 62}}, {"start": {"line": 412, "column": 66}, "end": {"line": 412, "column": 68}}], "line": 412}, "15": {"loc": {"start": {"line": 420, "column": 9}, "end": {"line": 420, "column": 72}}, "type": "cond-expr", "locations": [{"start": {"line": 420, "column": 32}, "end": {"line": 420, "column": 65}}, {"start": {"line": 420, "column": 68}, "end": {"line": 420, "column": 72}}], "line": 420}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\store\\slices\\settingsSlice.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\store\\slices\\settingsSlice.ts", "statementMap": {"0": {"start": {"line": 48, "column": 36}, "end": {"line": 84, "column": 1}}, "1": {"start": {"line": 87, "column": 22}, "end": {"line": 166, "column": 2}}, "2": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 35}}, "3": {"start": {"line": 94, "column": 6}, "end": {"line": 94, "column": 51}}, "4": {"start": {"line": 98, "column": 6}, "end": {"line": 98, "column": 66}}, "5": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 51}}, "6": {"start": {"line": 103, "column": 6}, "end": {"line": 103, "column": 40}}, "7": {"start": {"line": 104, "column": 6}, "end": {"line": 104, "column": 51}}, "8": {"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 41}}, "9": {"start": {"line": 110, "column": 6}, "end": {"line": 110, "column": 51}}, "10": {"start": {"line": 114, "column": 6}, "end": {"line": 114, "column": 38}}, "11": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 51}}, "12": {"start": {"line": 120, "column": 6}, "end": {"line": 120, "column": 48}}, "13": {"start": {"line": 121, "column": 6}, "end": {"line": 121, "column": 51}}, "14": {"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": 49}}, "15": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 51}}, "16": {"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": 43}}, "17": {"start": {"line": 132, "column": 6}, "end": {"line": 132, "column": 51}}, "18": {"start": {"line": 136, "column": 6}, "end": {"line": 136, "column": 44}}, "19": {"start": {"line": 137, "column": 6}, "end": {"line": 137, "column": 51}}, "20": {"start": {"line": 142, "column": 6}, "end": {"line": 142, "column": 42}}, "21": {"start": {"line": 143, "column": 6}, "end": {"line": 143, "column": 51}}, "22": {"start": {"line": 147, "column": 6}, "end": {"line": 147, "column": 39}}, "23": {"start": {"line": 148, "column": 6}, "end": {"line": 148, "column": 51}}, "24": {"start": {"line": 153, "column": 6}, "end": {"line": 153, "column": 41}}, "25": {"start": {"line": 154, "column": 6}, "end": {"line": 154, "column": 51}}, "26": {"start": {"line": 159, "column": 6}, "end": {"line": 159, "column": 43}}, "27": {"start": {"line": 160, "column": 6}, "end": {"line": 160, "column": 51}}, "28": {"start": {"line": 164, "column": 25}, "end": {"line": 164, "column": 37}}, "29": {"start": {"line": 184, "column": 4}, "end": {"line": 184, "column": 25}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 92, "column": 14}, "end": {"line": 92, "column": 15}}, "loc": {"start": {"line": 92, "column": 77}, "end": {"line": 95, "column": 5}}, "line": 92}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 97, "column": 17}, "end": {"line": 97, "column": 18}}, "loc": {"start": {"line": 97, "column": 59}, "end": {"line": 100, "column": 5}}, "line": 97}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 102, "column": 19}, "end": {"line": 102, "column": 20}}, "loc": {"start": {"line": 102, "column": 61}, "end": {"line": 105, "column": 5}}, "line": 102}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 108, "column": 20}, "end": {"line": 108, "column": 21}}, "loc": {"start": {"line": 108, "column": 73}, "end": {"line": 111, "column": 5}}, "line": 108}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 113, "column": 17}, "end": {"line": 113, "column": 18}}, "loc": {"start": {"line": 113, "column": 60}, "end": {"line": 116, "column": 5}}, "line": 113}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 119, "column": 27}, "end": {"line": 119, "column": 28}}, "loc": {"start": {"line": 119, "column": 70}, "end": {"line": 122, "column": 5}}, "line": 119}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 124, "column": 28}, "end": {"line": 124, "column": 29}}, "loc": {"start": {"line": 124, "column": 70}, "end": {"line": 127, "column": 5}}, "line": 124}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 130, "column": 22}, "end": {"line": 130, "column": 23}}, "loc": {"start": {"line": 130, "column": 65}, "end": {"line": 133, "column": 5}}, "line": 130}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 135, "column": 23}, "end": {"line": 135, "column": 24}}, "loc": {"start": {"line": 135, "column": 66}, "end": {"line": 138, "column": 5}}, "line": 135}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 141, "column": 21}, "end": {"line": 141, "column": 22}}, "loc": {"start": {"line": 141, "column": 64}, "end": {"line": 144, "column": 5}}, "line": 141}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 146, "column": 18}, "end": {"line": 146, "column": 19}}, "loc": {"start": {"line": 146, "column": 61}, "end": {"line": 149, "column": 5}}, "line": 146}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 152, "column": 20}, "end": {"line": 152, "column": 21}}, "loc": {"start": {"line": 152, "column": 63}, "end": {"line": 155, "column": 5}}, "line": 152}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 158, "column": 20}, "end": {"line": 158, "column": 21}}, "loc": {"start": {"line": 158, "column": 78}, "end": {"line": 161, "column": 5}}, "line": 158}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 164, "column": 19}, "end": {"line": 164, "column": 20}}, "loc": {"start": {"line": 164, "column": 25}, "end": {"line": 164, "column": 37}}, "line": 164}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "b": {}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\theme\\index.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\theme\\index.ts", "statementMap": {"0": {"start": {"line": 135, "column": 33}, "end": {"line": 171, "column": 1}}, "1": {"start": {"line": 174, "column": 32}, "end": {"line": 210, "column": 1}}, "2": {"start": {"line": 213, "column": 37}, "end": {"line": 319, "column": 1}}, "3": {"start": {"line": 322, "column": 31}, "end": {"line": 329, "column": 1}}, "4": {"start": {"line": 332, "column": 35}, "end": {"line": 375, "column": 1}}, "5": {"start": {"line": 378, "column": 27}, "end": {"line": 388, "column": 1}}, "6": {"start": {"line": 391, "column": 33}, "end": {"line": 398, "column": 1}}, "7": {"start": {"line": 400, "column": 32}, "end": {"line": 407, "column": 1}}, "8": {"start": {"line": 410, "column": 24}, "end": {"line": 412, "column": 1}}, "9": {"start": {"line": 411, "column": 2}, "end": {"line": 411, "column": 41}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 410, "column": 24}, "end": {"line": 410, "column": 25}}, "loc": {"start": {"line": 410, "column": 52}, "end": {"line": 412, "column": 1}}, "line": 410}}, "branchMap": {"0": {"loc": {"start": {"line": 411, "column": 9}, "end": {"line": 411, "column": 40}}, "type": "cond-expr", "locations": [{"start": {"line": 411, "column": 18}, "end": {"line": 411, "column": 27}}, {"start": {"line": 411, "column": 30}, "end": {"line": 411, "column": 40}}], "line": 411}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\types\\annotation.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\types\\annotation.ts", "statementMap": {}, "fnMap": {}, "branchMap": {}, "s": {}, "f": {}, "b": {}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\types\\document.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\types\\document.ts", "statementMap": {"0": {"start": {"line": 186, "column": 4}, "end": {"line": 186, "column": 19}}, "1": {"start": {"line": 187, "column": 4}, "end": {"line": 187, "column": 42}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 180, "column": 2}, "end": {"line": 180, "column": 3}}, "loc": {"start": {"line": 185, "column": 4}, "end": {"line": 188, "column": 3}}, "line": 185}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\types\\reading.ts": {"path": "C:\\Users\\<USER>\\Documents\\augment-projects\\InkSight\\implementation\\InkSight\\src\\types\\reading.ts", "statementMap": {"0": {"start": {"line": 193, "column": 4}, "end": {"line": 193, "column": 19}}, "1": {"start": {"line": 194, "column": 4}, "end": {"line": 194, "column": 35}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 187, "column": 2}, "end": {"line": 187, "column": 3}}, "loc": {"start": {"line": 192, "column": 4}, "end": {"line": 195, "column": 3}}, "line": 192}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}}