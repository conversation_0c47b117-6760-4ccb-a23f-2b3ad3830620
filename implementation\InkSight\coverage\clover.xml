<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1750616015538" clover="3.2.0">
  <project timestamp="1750616015538" name="All files">
    <metrics statements="1768" coveredstatements="0" conditionals="961" coveredconditionals="0" methods="423" coveredmethods="0" elements="3152" coveredelements="0" complexity="0" loc="1768" ncloc="1768" packages="14" files="41" classes="41"/>
    <package name="components.annotation">
      <metrics statements="180" coveredstatements="0" conditionals="149" coveredconditionals="0" methods="42" coveredmethods="0"/>
      <file name="AnnotationManager.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\components\annotation\AnnotationManager.tsx">
        <metrics statements="53" coveredstatements="0" conditionals="37" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
      </file>
      <file name="ColorPicker.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\components\annotation\ColorPicker.tsx">
        <metrics statements="19" coveredstatements="0" conditionals="23" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="21" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
      </file>
      <file name="HighlightSelector.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\components\annotation\HighlightSelector.tsx">
        <metrics statements="59" coveredstatements="0" conditionals="31" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="42" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
      </file>
      <file name="NoteEditor.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\components\annotation\NoteEditor.tsx">
        <metrics statements="49" coveredstatements="0" conditionals="58" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="43" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="172" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\components\annotation\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="components.common">
      <metrics statements="40" coveredstatements="0" conditionals="33" coveredconditionals="0" methods="11" coveredmethods="0"/>
      <file name="Button.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\components\common\Button.tsx">
        <metrics statements="12" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="24" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
      </file>
      <file name="Card.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\components\common\Card.tsx">
        <metrics statements="7" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="19" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
      </file>
      <file name="ErrorBoundary.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\components\common\ErrorBoundary.tsx">
        <metrics statements="21" coveredstatements="0" conditionals="13" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="94" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\components\common\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="components.reading">
      <metrics statements="194" coveredstatements="0" conditionals="158" coveredconditionals="0" methods="40" coveredmethods="0"/>
      <file name="DocumentViewer.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\components\reading\DocumentViewer.tsx">
        <metrics statements="61" coveredstatements="0" conditionals="27" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="99" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="182" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="229" count="0" type="stmt"/>
        <line num="237" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="238" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
      </file>
      <file name="NavigationControls.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\components\reading\NavigationControls.tsx">
        <metrics statements="27" coveredstatements="0" conditionals="33" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
      </file>
      <file name="ProgressTracker.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\components\reading\ProgressTracker.tsx">
        <metrics statements="37" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
      </file>
      <file name="TextSelection.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\components\reading\TextSelection.tsx">
        <metrics statements="50" coveredstatements="0" conditionals="21" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="19" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="82" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
      </file>
      <file name="ZoomControls.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\components\reading\ZoomControls.tsx">
        <metrics statements="19" coveredstatements="0" conditionals="53" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="10" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
      </file>
    </package>
    <package name="navigation">
      <metrics statements="20" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="10" coveredmethods="0"/>
      <file name="AppNavigator.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\navigation\AppNavigator.tsx">
        <metrics statements="20" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
      </file>
      <file name="types.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\navigation\types.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="screens">
      <metrics statements="27" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="9" coveredmethods="0"/>
      <file name="LibraryScreen.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\screens\LibraryScreen.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
      </file>
      <file name="NotesScreen.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\screens\NotesScreen.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
      </file>
      <file name="RecentScreen.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\screens\RecentScreen.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
      </file>
      <file name="SettingsScreen.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\screens\SettingsScreen.tsx">
        <metrics statements="13" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
      </file>
      <file name="WelcomeScreen.tsx" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\screens\WelcomeScreen.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
      </file>
    </package>
    <package name="services">
      <metrics statements="306" coveredstatements="0" conditionals="148" coveredconditionals="0" methods="88" coveredmethods="0"/>
      <file name="ErrorHandler.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\services\ErrorHandler.ts">
        <metrics statements="107" coveredstatements="0" conditionals="59" coveredconditionals="0" methods="25" coveredmethods="0"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="8"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="252" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="253" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="278" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="280" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="292" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="294" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="309" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="310" count="0" type="stmt"/>
        <line num="313" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="314" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="335" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="365" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="366" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="377" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
      </file>
      <file name="Logger.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\services\Logger.ts">
        <metrics statements="81" coveredstatements="0" conditionals="42" coveredconditionals="0" methods="29" coveredmethods="0"/>
        <line num="41" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="151" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="214" count="0" type="stmt"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="218" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="268" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="273" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
      </file>
      <file name="PerformanceMonitor.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\services\PerformanceMonitor.ts">
        <metrics statements="101" coveredstatements="0" conditionals="47" coveredconditionals="0" methods="32" coveredmethods="0"/>
        <line num="58" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="90" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="240" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="stmt"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="257" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="318" count="0" type="stmt"/>
        <line num="322" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="323" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="381" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="382" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="407" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\services\index.ts">
        <metrics statements="17" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
      </file>
    </package>
    <package name="services.annotation">
      <metrics statements="181" coveredstatements="0" conditionals="67" coveredconditionals="0" methods="43" coveredmethods="0"/>
      <file name="AnnotationService.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\services\annotation\AnnotationService.ts">
        <metrics statements="181" coveredstatements="0" conditionals="67" coveredconditionals="0" methods="43" coveredmethods="0"/>
        <line num="25" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="253" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="259" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="271" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="281" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="287" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="288" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="289" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="316" count="0" type="stmt"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="318" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="323" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="329" count="0" type="stmt"/>
        <line num="333" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="335" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="387" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="397" count="0" type="stmt"/>
        <line num="404" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="408" count="0" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="442" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="443" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="453" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="454" count="0" type="stmt"/>
        <line num="455" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="456" count="0" type="stmt"/>
        <line num="463" count="0" type="stmt"/>
        <line num="469" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="470" count="0" type="stmt"/>
        <line num="471" count="0" type="stmt"/>
        <line num="472" count="0" type="stmt"/>
        <line num="474" count="0" type="stmt"/>
        <line num="483" count="0" type="stmt"/>
        <line num="489" count="0" type="stmt"/>
        <line num="490" count="0" type="stmt"/>
        <line num="491" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="492" count="0" type="stmt"/>
        <line num="494" count="0" type="stmt"/>
        <line num="495" count="0" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="497" count="0" type="stmt"/>
        <line num="498" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="503" count="0" type="stmt"/>
        <line num="509" count="0" type="stmt"/>
        <line num="519" count="0" type="stmt"/>
        <line num="520" count="0" type="stmt"/>
        <line num="522" count="0" type="stmt"/>
        <line num="526" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="532" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="533" count="0" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
        <line num="547" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="562" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="563" count="0" type="stmt"/>
        <line num="566" count="0" type="stmt"/>
        <line num="567" count="0" type="stmt"/>
        <line num="568" count="0" type="stmt"/>
        <line num="575" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="576" count="0" type="stmt"/>
        <line num="578" count="0" type="stmt"/>
        <line num="579" count="0" type="stmt"/>
        <line num="580" count="0" type="stmt"/>
        <line num="585" count="0" type="stmt"/>
      </file>
    </package>
    <package name="services.document">
      <metrics statements="109" coveredstatements="0" conditionals="37" coveredconditionals="0" methods="18" coveredmethods="0"/>
      <file name="DocumentParserService.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\services\document\DocumentParserService.ts">
        <metrics statements="109" coveredstatements="0" conditionals="37" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="182" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="211" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="241" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="250" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="251" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="264" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="265" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="287" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="292" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
      </file>
    </package>
    <package name="services.document.parsers">
      <metrics statements="466" coveredstatements="0" conditionals="271" coveredconditionals="0" methods="76" coveredmethods="0"/>
      <file name="EPUBParser.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\services\document\parsers\EPUBParser.ts">
        <metrics statements="119" coveredstatements="0" conditionals="112" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="129" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="193" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="246" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="253" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="254" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="268" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="301" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="308" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="320" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="327" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="328" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="333" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="334" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="339" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="342" count="0" type="stmt"/>
        <line num="343" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="344" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="349" count="0" type="stmt"/>
        <line num="351" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="367" count="0" type="stmt"/>
        <line num="368" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="373" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="MarkdownParser.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\services\document\parsers\MarkdownParser.ts">
        <metrics statements="104" coveredstatements="0" conditionals="51" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="180" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="243" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="244" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="339" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="369" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="370" count="0" type="stmt"/>
        <line num="371" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="PDFParser.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\services\document\parsers\PDFParser.ts">
        <metrics statements="76" coveredstatements="0" conditionals="33" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="152" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="RTFParser.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\services\document\parsers\RTFParser.ts">
        <metrics statements="79" coveredstatements="0" conditionals="32" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="162" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="183" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="264" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="TextParser.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\services\document\parsers\TextParser.ts">
        <metrics statements="88" coveredstatements="0" conditionals="43" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="201" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="220" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="221" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="246" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="301" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
    </package>
    <package name="services.reading">
      <metrics statements="58" coveredstatements="0" conditionals="47" coveredconditionals="0" methods="13" coveredmethods="0"/>
      <file name="ReadingService.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\services\reading\ReadingService.ts">
        <metrics statements="58" coveredstatements="0" conditionals="47" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="23" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="53" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="220" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
    </package>
    <package name="store">
      <metrics statements="15" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="5" coveredmethods="0"/>
      <file name="index.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\store\index.ts">
        <metrics statements="10" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
      </file>
      <file name="rootReducer.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\store\rootReducer.ts">
        <metrics statements="5" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
      </file>
    </package>
    <package name="store.slices">
      <metrics statements="158" coveredstatements="0" conditionals="32" coveredconditionals="0" methods="65" coveredmethods="0"/>
      <file name="annotationSlice.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\store\slices\annotationSlice.ts">
        <metrics statements="128" coveredstatements="0" conditionals="32" coveredconditionals="0" methods="51" coveredmethods="0"/>
        <line num="57" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="269" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="285" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="295" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="304" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="315" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="319" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="330" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="331" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="336" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="342" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="343" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="353" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="364" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="368" count="0" type="stmt"/>
        <line num="370" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="374" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="379" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="398" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="410" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="412" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="413" count="0" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="420" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="423" count="0" type="stmt"/>
        <line num="424" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="437" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
      </file>
      <file name="settingsSlice.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\store\slices\settingsSlice.ts">
        <metrics statements="30" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="48" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
      </file>
    </package>
    <package name="theme">
      <metrics statements="10" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="index.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\theme\index.ts">
        <metrics statements="10" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="135" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="411" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
    </package>
    <package name="types">
      <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="annotation.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\types\annotation.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="document.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\types\document.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
      </file>
      <file name="reading.ts" path="C:\Users\<USER>\Documents\augment-projects\InkSight\implementation\InkSight\src\types\reading.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
