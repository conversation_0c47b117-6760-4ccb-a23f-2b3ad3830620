/**
 * KeyManager - Secure key management service for InkSight
 * Handles encryption key generation, storage, rotation, and lifecycle management
 */

import * as Keychain from 'react-native-keychain';
import {
  EncryptionKey,
  KeyPurpose,
  SecurityLevel,
  KeyRotationPolicy,
  SecurityContext,
  SecurityError,
  SecurityErrorType,
  SecureStorageOptions,
  HSMCapabilities,
} from '../../types/security';
import { EncryptionService } from './EncryptionService';

export class KeyManager {
  private encryptionService: EncryptionService;
  private keyCache: Map<string, EncryptionKey> = new Map();
  private isInitialized: boolean = false;
  private hsmCapabilities: HSMCapabilities | null = null;

  constructor(encryptionService: EncryptionService) {
    this.encryptionService = encryptionService;
  }

  /**
   * Initialize the key manager
   */
  public async initialize(): Promise<void> {
    try {
      // Check hardware security capabilities
      this.hsmCapabilities = await this.checkHSMCapabilities();

      // Load existing keys from secure storage
      await this.loadKeysFromStorage();

      this.isInitialized = true;
    } catch (error) {
      throw new SecurityError(
        'KEY_GENERATION_FAILED',
        `Failed to initialize key manager: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        undefined,
        { originalError: error },
      );
    }
  }

  /**
   * Generate a new encryption key
   */
  public async generateKey(
    purpose: KeyPurpose,
    securityLevel: SecurityLevel,
    context: SecurityContext,
  ): Promise<EncryptionKey> {
    this.ensureInitialized();

    try {
      // Generate the key using encryption service
      const key = await this.encryptionService.generateKey(
        purpose,
        securityLevel,
      );

      // Store key securely
      await this.storeKey(key, securityLevel);

      // Cache the key
      this.keyCache.set(key.id, key);

      return key;
    } catch (error) {
      throw new SecurityError(
        'KEY_GENERATION_FAILED',
        `Key generation failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        context,
        { purpose, securityLevel, originalError: error },
      );
    }
  }

  /**
   * Retrieve a key by ID
   */
  public async getKey(keyId: string): Promise<EncryptionKey | null> {
    this.ensureInitialized();

    // Check cache first
    if (this.keyCache.has(keyId)) {
      const key = this.keyCache.get(keyId)!;

      // Update last used timestamp
      key.lastUsed = Date.now();
      await this.updateKey(key);

      return key;
    }

    // Load from secure storage
    try {
      const key = await this.loadKeyFromStorage(keyId);
      if (key) {
        this.keyCache.set(keyId, key);

        // Update last used timestamp
        key.lastUsed = Date.now();
        await this.updateKey(key);
      }
      return key;
    } catch (error) {
      console.warn(`Failed to load key ${keyId}:`, error);
      return null;
    }
  }

  /**
   * Get master key for a specific purpose
   */
  public async getMasterKey(
    purpose: KeyPurpose,
  ): Promise<EncryptionKey | null> {
    this.ensureInitialized();

    // Find master key for the specified purpose
    for (const [, key] of this.keyCache) {
      if (key.purpose === purpose && key.purpose === 'master-key') {
        return key;
      }
    }

    // If not found in cache, search in storage
    const allKeys = await this.getAllKeys();
    return (
      allKeys.find(
        key => key.purpose === purpose && key.purpose === 'master-key',
      ) || null
    );
  }

  /**
   * Rotate a key (generate new key and mark old one for deletion)
   */
  public async rotateKey(
    keyId: string,
    context: SecurityContext,
  ): Promise<EncryptionKey> {
    this.ensureInitialized();

    try {
      const oldKey = await this.getKey(keyId);
      if (!oldKey) {
        throw new Error(`Key ${keyId} not found`);
      }

      // Generate new key with same purpose and security level
      const newKey = await this.generateKey(
        oldKey.purpose,
        oldKey.securityLevel,
        context,
      );

      // Mark old key for deletion (don't delete immediately to allow for data migration)
      oldKey.rotationDue = Date.now() - 1; // Mark as expired
      await this.updateKey(oldKey);

      return newKey;
    } catch (error) {
      throw new SecurityError(
        'KEY_GENERATION_FAILED',
        `Key rotation failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        context,
        { keyId, originalError: error },
      );
    }
  }

  /**
   * Check if a key needs rotation
   */
  public async checkKeyRotation(keyId: string): Promise<boolean> {
    const key = await this.getKey(keyId);
    if (!key) {
      return false;
    }

    return Date.now() >= key.rotationDue;
  }

  /**
   * Get all keys that need rotation
   */
  public async getKeysNeedingRotation(): Promise<EncryptionKey[]> {
    const allKeys = await this.getAllKeys();
    return allKeys.filter(key => Date.now() >= key.rotationDue);
  }

  /**
   * Delete a key securely
   */
  public async deleteKey(keyId: string): Promise<boolean> {
    this.ensureInitialized();

    try {
      // Remove from cache
      this.keyCache.delete(keyId);

      // Remove from secure storage
      await this.removeKeyFromStorage(keyId);

      return true;
    } catch (error) {
      console.warn(`Failed to delete key ${keyId}:`, error);
      return false;
    }
  }

  /**
   * Get all keys
   */
  public async getAllKeys(): Promise<EncryptionKey[]> {
    this.ensureInitialized();

    // Return cached keys
    return Array.from(this.keyCache.values());
  }

  /**
   * Store key in secure storage
   */
  private async storeKey(
    key: EncryptionKey,
    securityLevel: SecurityLevel,
  ): Promise<void> {
    const options: SecureStorageOptions = {
      useHardwareBackedKeys: this.hsmCapabilities?.keyStorage || false,
      requireBiometric: securityLevel === 'maximum',
      securityLevel,
    };

    const keychainOptions = {
      service: `inksight.key.${key.id}`,
      accessGroup: options.accessGroup,
    };

    await Keychain.setInternetCredentials(
      key.id,
      key.id,
      JSON.stringify(key),
      keychainOptions,
    );
  }

  /**
   * Load key from secure storage
   */
  private async loadKeyFromStorage(
    keyId: string,
  ): Promise<EncryptionKey | null> {
    try {
      const credentials = await Keychain.getInternetCredentials(keyId);
      if (credentials && credentials.password) {
        return JSON.parse(credentials.password) as EncryptionKey;
      }
      return null;
    } catch (error) {
      console.warn(`Failed to load key ${keyId} from storage:`, error);
      return null;
    }
  }

  /**
   * Update key in storage
   */
  private async updateKey(key: EncryptionKey): Promise<void> {
    await this.storeKey(key, key.securityLevel);
    this.keyCache.set(key.id, key);
  }

  /**
   * Remove key from secure storage
   */
  private async removeKeyFromStorage(keyId: string): Promise<void> {
    // Use resetGenericPassword with service identifier to match how we store keys
    await Keychain.resetGenericPassword({ service: `inksight.key.${keyId}` });
  }

  /**
   * Load all keys from secure storage
   */
  private async loadKeysFromStorage(): Promise<void> {
    try {
      // Note: react-native-keychain doesn't provide a way to list all keys
      // In a real implementation, we would maintain an index of key IDs
      // For now, we'll start with an empty cache
      this.keyCache.clear();
    } catch (error) {
      console.warn('Failed to load keys from storage:', error);
    }
  }

  /**
   * Check hardware security module capabilities
   */
  private async checkHSMCapabilities(): Promise<HSMCapabilities> {
    try {
      const biometryType = await Keychain.getSupportedBiometryType();
      const securityLevel = await Keychain.getSecurityLevel();

      return {
        available: true,
        keyGeneration: true,
        keyStorage: securityLevel === Keychain.SECURITY_LEVEL.SECURE_HARDWARE,
        encryption: true,
        biometric: biometryType !== null,
        secureEnclave:
          securityLevel === Keychain.SECURITY_LEVEL.SECURE_HARDWARE,
      };
    } catch (error) {
      return {
        available: false,
        keyGeneration: false,
        keyStorage: false,
        encryption: false,
        biometric: false,
        secureEnclave: false,
      };
    }
  }

  /**
   * Ensure service is initialized
   */
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new SecurityError(
        'KEY_GENERATION_FAILED',
        'Key manager not initialized',
      );
    }
  }

  /**
   * Get HSM capabilities
   */
  public getHSMCapabilities(): HSMCapabilities | null {
    return this.hsmCapabilities;
  }
}
