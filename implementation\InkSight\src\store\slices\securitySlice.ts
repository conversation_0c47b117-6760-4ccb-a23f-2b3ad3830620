/**
 * Security Redux Slice - Manages security state and operations
 * Handles encryption keys, security settings, and audit logs
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  SecuritySettings,
  EncryptionKey,
  SecurityAuditEntry,
  HSMCapabilities,
  SecurityLevel,
  KeyPurpose,
  SecurityContext,
  DEFAULT_SECURITY_SETTINGS,
} from '../../types/security';

// Security state interface
export interface SecurityState {
  // Security settings
  settings: SecuritySettings;

  // Key management
  keys: Record<string, EncryptionKey>;
  masterKeyId: string | null;
  keyRotationInProgress: boolean;

  // Security status
  isInitialized: boolean;
  isLocked: boolean;
  failedAttempts: number;
  lockoutUntil: number | null;

  // Hardware capabilities
  hsmCapabilities: HSMCapabilities | null;
  biometricAvailable: boolean;

  // Audit logging
  auditLog: SecurityAuditEntry[];
  auditEnabled: boolean;

  // Loading states
  loading: {
    initialization: boolean;
    keyGeneration: boolean;
    keyRotation: boolean;
    settings: boolean;
  };

  // Error state
  error: string | null;
}

// Initial state
const initialState: SecurityState = {
  settings: DEFAULT_SECURITY_SETTINGS,
  keys: {},
  masterKeyId: null,
  keyRotationInProgress: false,
  isInitialized: false,
  isLocked: false,
  failedAttempts: 0,
  lockoutUntil: null,
  hsmCapabilities: null,
  biometricAvailable: false,
  auditLog: [],
  auditEnabled: true,
  loading: {
    initialization: false,
    keyGeneration: false,
    keyRotation: false,
    settings: false,
  },
  error: null,
};

// Async thunks for security operations

/**
 * Initialize security system
 */
export const initializeSecurity = createAsyncThunk(
  'security/initialize',
  async (_, { rejectWithValue }) => {
    try {
      // This would integrate with actual security services
      // For now, return mock data
      const hsmCapabilities: HSMCapabilities = {
        available: true,
        keyGeneration: true,
        keyStorage: true,
        encryption: true,
        biometric: false,
        secureEnclave: false,
      };

      return {
        hsmCapabilities,
        biometricAvailable: false,
      };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error
          ? error.message
          : 'Security initialization failed',
      );
    }
  },
);

/**
 * Generate new encryption key
 */
export const generateEncryptionKey = createAsyncThunk(
  'security/generateKey',
  async (
    params: {
      purpose: KeyPurpose;
      securityLevel: SecurityLevel;
      context: SecurityContext;
    },
    { rejectWithValue },
  ) => {
    try {
      // This would integrate with KeyManager
      // For now, return mock key
      const key: EncryptionKey = {
        id: `key-${Date.now()}`,
        key: 'mock-key-data',
        algorithm: 'AES-256-GCM',
        created: Date.now(),
        lastUsed: Date.now(),
        rotationDue: Date.now() + 90 * 24 * 60 * 60 * 1000,
        purpose: params.purpose,
        securityLevel: params.securityLevel,
      };

      return key;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Key generation failed',
      );
    }
  },
);

/**
 * Rotate encryption key
 */
export const rotateEncryptionKey = createAsyncThunk(
  'security/rotateKey',
  async (
    params: { keyId: string; context: SecurityContext },
    { getState, rejectWithValue },
  ) => {
    try {
      const state = getState() as { security: SecurityState };
      const oldKey = state.security.keys[params.keyId];

      if (!oldKey) {
        throw new Error(`Key ${params.keyId} not found`);
      }

      // Generate new key with same purpose and security level
      const newKey: EncryptionKey = {
        id: `key-${Date.now()}`,
        key: 'mock-rotated-key-data',
        algorithm: oldKey.algorithm,
        created: Date.now(),
        lastUsed: Date.now(),
        rotationDue: Date.now() + 90 * 24 * 60 * 60 * 1000,
        purpose: oldKey.purpose,
        securityLevel: oldKey.securityLevel,
      };

      return { oldKeyId: params.keyId, newKey };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Key rotation failed',
      );
    }
  },
);

/**
 * Update security settings
 */
export const updateSecuritySettings = createAsyncThunk(
  'security/updateSettings',
  async (settings: Partial<SecuritySettings>, { rejectWithValue }) => {
    try {
      // This would persist settings securely
      return settings;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Settings update failed',
      );
    }
  },
);

/**
 * Add audit log entry
 */
export const addAuditEntry = createAsyncThunk(
  'security/addAuditEntry',
  async (entry: Omit<SecurityAuditEntry, 'id'>, { rejectWithValue }) => {
    try {
      const auditEntry: SecurityAuditEntry = {
        ...entry,
        id: `audit-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      };

      return auditEntry;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Audit logging failed',
      );
    }
  },
);

// Security slice
const securitySlice = createSlice({
  name: 'security',
  initialState,
  reducers: {
    // Security status actions
    lockApplication: state => {
      state.isLocked = true;
    },

    unlockApplication: state => {
      state.isLocked = false;
      state.failedAttempts = 0;
      state.lockoutUntil = null;
    },

    incrementFailedAttempts: state => {
      state.failedAttempts += 1;

      if (state.failedAttempts >= state.settings.maxFailedAttempts) {
        state.isLocked = true;
        state.lockoutUntil =
          Date.now() + state.settings.lockoutDuration * 60 * 1000;
      }
    },

    resetFailedAttempts: state => {
      state.failedAttempts = 0;
      state.lockoutUntil = null;
    },

    // Key management actions
    addKey: (state, action: PayloadAction<EncryptionKey>) => {
      state.keys[action.payload.id] = action.payload;
    },

    removeKey: (state, action: PayloadAction<string>) => {
      delete state.keys[action.payload];
    },

    updateKeyLastUsed: (state, action: PayloadAction<string>) => {
      const key = state.keys[action.payload];
      if (key) {
        key.lastUsed = Date.now();
      }
    },

    setMasterKey: (state, action: PayloadAction<string>) => {
      state.masterKeyId = action.payload;
    },

    // Settings actions
    updateSettings: (
      state,
      action: PayloadAction<Partial<SecuritySettings>>,
    ) => {
      state.settings = { ...state.settings, ...action.payload };
    },

    // Audit actions
    clearAuditLog: state => {
      state.auditLog = [];
    },

    setAuditEnabled: (state, action: PayloadAction<boolean>) => {
      state.auditEnabled = action.payload;
    },

    // Error handling
    clearError: state => {
      state.error = null;
    },

    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
  },
  extraReducers: builder => {
    // Initialize security
    builder
      .addCase(initializeSecurity.pending, state => {
        state.loading.initialization = true;
        state.error = null;
      })
      .addCase(initializeSecurity.fulfilled, (state, action) => {
        state.loading.initialization = false;
        state.isInitialized = true;
        state.hsmCapabilities = action.payload.hsmCapabilities;
        state.biometricAvailable = action.payload.biometricAvailable;
      })
      .addCase(initializeSecurity.rejected, (state, action) => {
        state.loading.initialization = false;
        state.error = action.payload as string;
      });

    // Generate key
    builder
      .addCase(generateEncryptionKey.pending, state => {
        state.loading.keyGeneration = true;
        state.error = null;
      })
      .addCase(generateEncryptionKey.fulfilled, (state, action) => {
        state.loading.keyGeneration = false;
        state.keys[action.payload.id] = action.payload;

        // Set as master key if it's the first key or explicitly a master key
        if (!state.masterKeyId || action.payload.purpose === 'master-key') {
          state.masterKeyId = action.payload.id;
        }
      })
      .addCase(generateEncryptionKey.rejected, (state, action) => {
        state.loading.keyGeneration = false;
        state.error = action.payload as string;
      });

    // Rotate key
    builder
      .addCase(rotateEncryptionKey.pending, state => {
        state.loading.keyRotation = true;
        state.keyRotationInProgress = true;
        state.error = null;
      })
      .addCase(rotateEncryptionKey.fulfilled, (state, action) => {
        state.loading.keyRotation = false;
        state.keyRotationInProgress = false;

        // Add new key and mark old key as expired
        state.keys[action.payload.newKey.id] = action.payload.newKey;
        const oldKey = state.keys[action.payload.oldKeyId];
        if (oldKey) {
          oldKey.rotationDue = Date.now() - 1; // Mark as expired
        }

        // Update master key reference if needed
        if (state.masterKeyId === action.payload.oldKeyId) {
          state.masterKeyId = action.payload.newKey.id;
        }
      })
      .addCase(rotateEncryptionKey.rejected, (state, action) => {
        state.loading.keyRotation = false;
        state.keyRotationInProgress = false;
        state.error = action.payload as string;
      });

    // Update settings
    builder
      .addCase(updateSecuritySettings.pending, state => {
        state.loading.settings = true;
        state.error = null;
      })
      .addCase(updateSecuritySettings.fulfilled, (state, action) => {
        state.loading.settings = false;
        state.settings = { ...state.settings, ...action.payload };
      })
      .addCase(updateSecuritySettings.rejected, (state, action) => {
        state.loading.settings = false;
        state.error = action.payload as string;
      });

    // Add audit entry
    builder.addCase(addAuditEntry.fulfilled, (state, action) => {
      if (state.auditEnabled) {
        state.auditLog.push(action.payload);

        // Keep only last 1000 entries
        if (state.auditLog.length > 1000) {
          state.auditLog = state.auditLog.slice(-1000);
        }
      }
    });
  },
});

// Export actions
export const {
  lockApplication,
  unlockApplication,
  incrementFailedAttempts,
  resetFailedAttempts,
  addKey,
  removeKey,
  updateKeyLastUsed,
  setMasterKey,
  updateSettings,
  clearAuditLog,
  setAuditEnabled,
  clearError,
  setError,
} = securitySlice.actions;

// Export reducer
export default securitySlice.reducer;

// Selectors
export const selectSecuritySettings = (state: { security: SecurityState }) =>
  state.security.settings;
export const selectIsSecurityInitialized = (state: {
  security: SecurityState;
}) => state.security.isInitialized;
export const selectIsApplicationLocked = (state: { security: SecurityState }) =>
  state.security.isLocked;
export const selectEncryptionKeys = (state: { security: SecurityState }) =>
  state.security.keys;
export const selectMasterKey = (state: { security: SecurityState }) =>
  state.security.masterKeyId
    ? state.security.keys[state.security.masterKeyId]
    : null;
export const selectHSMCapabilities = (state: { security: SecurityState }) =>
  state.security.hsmCapabilities;
export const selectSecurityError = (state: { security: SecurityState }) =>
  state.security.error;
export const selectSecurityLoading = (state: { security: SecurityState }) =>
  state.security.loading;
