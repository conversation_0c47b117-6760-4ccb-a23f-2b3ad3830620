/**
 * Document Management Redux Slice
 * Handles document library state, collections, tags, and import operations
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  DocumentLibraryState,
  DocumentLibraryItem,
  DocumentCollection,
  DocumentTag,
  DocumentViewMode,
  DocumentSortBy,
  SortOrder,
  DocumentFilter,
  ImportProgress,
  ImportStatus,
  ImportOptions,
  BatchImportState,
  DocumentSearchState,
  DocumentLibraryPreferences,
} from '../../types/document-management';
import { DocumentMetadata } from '../../types/document';

// Initial state
const initialState: DocumentLibraryState &
  BatchImportState &
  DocumentSearchState & { preferences: DocumentLibraryPreferences } = {
  // Document Library
  documents: [],
  collections: [],
  tags: [],
  viewMode: DocumentViewMode.GRID,
  sortBy: DocumentSortBy.DATE_ADDED,
  sortOrder: SortOrder.DESC,
  filter: {},
  searchQuery: '',
  selectedDocuments: [],
  isLoading: false,
  error: null,

  // Import State
  imports: [],
  isImporting: false,
  totalFiles: 0,
  completedFiles: 0,
  failedFiles: 0,

  // Search State
  query: '',
  results: [],
  isSearching: false,
  searchHistory: [],
  suggestions: [],

  // Preferences
  preferences: {
    defaultViewMode: DocumentViewMode.GRID,
    defaultSortBy: DocumentSortBy.DATE_ADDED,
    defaultSortOrder: SortOrder.DESC,
    gridColumns: 2,
    showThumbnails: true,
    showMetadata: true,
    autoGenerateThumbnails: true,
    defaultImportTags: [],
    recentDocumentsLimit: 20,
    searchHistoryLimit: 50,
  },
};

// Async thunks for document operations
export const loadDocuments = createAsyncThunk(
  'documents/loadDocuments',
  async (_, { rejectWithValue }) => {
    try {
      // TODO: Implement document loading from storage
      // This will integrate with the storage service from Week 3
      const documents: DocumentLibraryItem[] = [];
      return documents;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Failed to load documents',
      );
    }
  },
);

export const importDocument = createAsyncThunk(
  'documents/importDocument',
  async (
    { filePath, options }: { filePath: string; options?: ImportOptions },
    { rejectWithValue, dispatch },
  ) => {
    try {
      // Create import progress entry
      const importId = `import_${Date.now()}_${Math.random()
        .toString(36)
        .substr(2, 9)}`;
      const importProgress: ImportProgress = {
        id: importId,
        fileName: filePath.split('/').pop() || 'Unknown',
        filePath,
        fileSize: 0, // TODO: Get actual file size
        status: ImportStatus.PENDING,
        progress: 0,
        startTime: new Date(),
      };

      dispatch(addImportProgress(importProgress));

      // TODO: Implement actual document import
      // This will integrate with:
      // - Document parsers from Week 6
      // - Security/storage services from Week 3
      // - File system operations

      // Simulate import progress
      dispatch(
        updateImportProgress({
          id: importId,
          status: ImportStatus.PROCESSING,
          progress: 50,
        }),
      );

      // TODO: Parse document, extract metadata, encrypt and store
      const documentMetadata: DocumentMetadata = {
        id: `doc_${Date.now()}`,
        title: 'Sample Document',
        fileSize: 1024,
        format: 'pdf' as any,
        mimeType: 'application/pdf',
        createdAt: new Date(),
        modifiedAt: new Date(),
        readingProgress: 0,
        totalReadingTime: 0,
      };

      const documentItem: DocumentLibraryItem = {
        ...documentMetadata,
        collections: options?.addToCollection ? [options.addToCollection] : [],
        tags: options?.addTags || [],
        isFavorite: false,
        isRecent: true,
        importedAt: new Date(),
        filePath,
      };

      dispatch(
        updateImportProgress({
          id: importId,
          status: ImportStatus.COMPLETED,
          progress: 100,
          documentId: documentItem.id,
        }),
      );

      return documentItem;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Failed to import document',
      );
    }
  },
);

export const createCollection = createAsyncThunk(
  'documents/createCollection',
  async (
    {
      name,
      description,
      color,
    }: { name: string; description?: string; color?: string },
    { rejectWithValue },
  ) => {
    try {
      const collection: DocumentCollection = {
        id: `collection_${Date.now()}`,
        name,
        description,
        color,
        documentIds: [],
        createdAt: new Date(),
        modifiedAt: new Date(),
      };

      // TODO: Save to storage
      return collection;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Failed to create collection',
      );
    }
  },
);

export const createTag = createAsyncThunk(
  'documents/createTag',
  async (
    { name, color }: { name: string; color?: string },
    { rejectWithValue },
  ) => {
    try {
      const tag: DocumentTag = {
        id: `tag_${Date.now()}`,
        name,
        color,
        documentIds: [],
        createdAt: new Date(),
        usageCount: 0,
      };

      // TODO: Save to storage
      return tag;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Failed to create tag',
      );
    }
  },
);

export const searchDocuments = createAsyncThunk(
  'documents/searchDocuments',
  async (query: string, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { documents: typeof initialState };
      const { documents } = state.documents;

      // Simple text search implementation
      const results = documents.filter(
        doc =>
          doc.title.toLowerCase().includes(query.toLowerCase()) ||
          doc.author?.toLowerCase().includes(query.toLowerCase()) ||
          doc.description?.toLowerCase().includes(query.toLowerCase()),
      );

      return { query, results };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Search failed',
      );
    }
  },
);

// Document slice
const documentSlice = createSlice({
  name: 'documents',
  initialState,
  reducers: {
    // View and display actions
    setViewMode: (state, action: PayloadAction<DocumentViewMode>) => {
      state.viewMode = action.payload;
    },
    setSortBy: (state, action: PayloadAction<DocumentSortBy>) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action: PayloadAction<SortOrder>) => {
      state.sortOrder = action.payload;
    },
    setFilter: (state, action: PayloadAction<DocumentFilter>) => {
      state.filter = action.payload;
    },
    clearFilter: state => {
      state.filter = {};
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },

    // Document selection
    selectDocument: (state, action: PayloadAction<string>) => {
      if (!state.selectedDocuments.includes(action.payload)) {
        state.selectedDocuments.push(action.payload);
      }
    },
    deselectDocument: (state, action: PayloadAction<string>) => {
      state.selectedDocuments = state.selectedDocuments.filter(
        id => id !== action.payload,
      );
    },
    selectAllDocuments: state => {
      state.selectedDocuments = state.documents.map(doc => doc.id);
    },
    clearSelection: state => {
      state.selectedDocuments = [];
    },

    // Document actions
    toggleFavorite: (state, action: PayloadAction<string>) => {
      const document = state.documents.find(doc => doc.id === action.payload);
      if (document) {
        document.isFavorite = !document.isFavorite;
      }
    },
    addToCollection: (
      state,
      action: PayloadAction<{ documentId: string; collectionId: string }>,
    ) => {
      const document = state.documents.find(
        doc => doc.id === action.payload.documentId,
      );
      if (
        document &&
        !document.collections.includes(action.payload.collectionId)
      ) {
        document.collections.push(action.payload.collectionId);
      }
    },
    removeFromCollection: (
      state,
      action: PayloadAction<{ documentId: string; collectionId: string }>,
    ) => {
      const document = state.documents.find(
        doc => doc.id === action.payload.documentId,
      );
      if (document) {
        document.collections = document.collections.filter(
          id => id !== action.payload.collectionId,
        );
      }
    },
    addTag: (
      state,
      action: PayloadAction<{ documentId: string; tagId: string }>,
    ) => {
      const document = state.documents.find(
        doc => doc.id === action.payload.documentId,
      );
      if (document && !document.tags.includes(action.payload.tagId)) {
        document.tags.push(action.payload.tagId);
      }
    },
    removeTag: (
      state,
      action: PayloadAction<{ documentId: string; tagId: string }>,
    ) => {
      const document = state.documents.find(
        doc => doc.id === action.payload.documentId,
      );
      if (document) {
        document.tags = document.tags.filter(id => id !== action.payload.tagId);
      }
    },

    // Import actions
    addImportProgress: (state, action: PayloadAction<ImportProgress>) => {
      state.imports.push(action.payload);
      state.totalFiles = state.imports.length;
    },
    updateImportProgress: (
      state,
      action: PayloadAction<Partial<ImportProgress> & { id: string }>,
    ) => {
      const importIndex = state.imports.findIndex(
        imp => imp.id === action.payload.id,
      );
      if (importIndex !== -1) {
        state.imports[importIndex] = {
          ...state.imports[importIndex],
          ...action.payload,
        };

        // Update counters
        state.completedFiles = state.imports.filter(
          imp => imp.status === ImportStatus.COMPLETED,
        ).length;
        state.failedFiles = state.imports.filter(
          imp => imp.status === ImportStatus.FAILED,
        ).length;
        state.isImporting = state.imports.some(
          imp =>
            imp.status === ImportStatus.PENDING ||
            imp.status === ImportStatus.PROCESSING,
        );
      }
    },
    clearImportHistory: state => {
      state.imports = [];
      state.totalFiles = 0;
      state.completedFiles = 0;
      state.failedFiles = 0;
      state.isImporting = false;
    },

    // Search actions
    addToSearchHistory: (state, action: PayloadAction<string>) => {
      const query = action.payload.trim();
      if (query && !state.searchHistory.includes(query)) {
        state.searchHistory.unshift(query);
        if (state.searchHistory.length > state.preferences.searchHistoryLimit) {
          state.searchHistory = state.searchHistory.slice(
            0,
            state.preferences.searchHistoryLimit,
          );
        }
      }
    },
    clearSearchHistory: state => {
      state.searchHistory = [];
    },

    // Preferences
    updatePreferences: (
      state,
      action: PayloadAction<Partial<DocumentLibraryPreferences>>,
    ) => {
      state.preferences = { ...state.preferences, ...action.payload };
    },

    // Error handling
    clearError: state => {
      state.error = null;
    },
  },
  extraReducers: builder => {
    builder
      // Load documents
      .addCase(loadDocuments.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadDocuments.fulfilled, (state, action) => {
        state.isLoading = false;
        state.documents = action.payload;
      })
      .addCase(loadDocuments.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Import document
      .addCase(importDocument.fulfilled, (state, action) => {
        state.documents.unshift(action.payload);
      })
      .addCase(importDocument.rejected, (state, action) => {
        state.error = action.payload as string;
      })

      // Create collection
      .addCase(createCollection.fulfilled, (state, action) => {
        state.collections.push(action.payload);
      })
      .addCase(createCollection.rejected, (state, action) => {
        state.error = action.payload as string;
      })

      // Create tag
      .addCase(createTag.fulfilled, (state, action) => {
        state.tags.push(action.payload);
      })
      .addCase(createTag.rejected, (state, action) => {
        state.error = action.payload as string;
      })

      // Search documents
      .addCase(searchDocuments.pending, state => {
        state.isSearching = true;
      })
      .addCase(searchDocuments.fulfilled, (state, action) => {
        state.isSearching = false;
        state.query = action.payload.query;
        state.results = action.payload.results;
      })
      .addCase(searchDocuments.rejected, (state, action) => {
        state.isSearching = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setViewMode,
  setSortBy,
  setSortOrder,
  setFilter,
  clearFilter,
  setSearchQuery,
  selectDocument,
  deselectDocument,
  selectAllDocuments,
  clearSelection,
  toggleFavorite,
  addToCollection,
  removeFromCollection,
  addTag,
  removeTag,
  addImportProgress,
  updateImportProgress,
  clearImportHistory,
  addToSearchHistory,
  clearSearchHistory,
  updatePreferences,
  clearError,
} = documentSlice.actions;

export default documentSlice.reducer;
