/**
 * Document Library Component
 * Main interface for browsing and managing documents
 */

import React, { useEffect, useState, useCallback } from 'react';
import { View, StyleSheet, RefreshControl, Alert } from 'react-native';
import {
  Appbar,
  FAB,
  Searchbar,
  Menu,
  Portal,
  Modal,
  Text,
  Button,
} from 'react-native-paper';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  loadDocuments,
  setViewMode,
  setSortBy,
  setSortOrder,
  setSearchQuery,
  searchDocuments,
  clearSelection,
} from '../../store/slices/documentSlice';
import {
  DocumentViewMode,
  DocumentSortBy,
  SortOrder,
} from '../../types/document-management';
import { DocumentGrid } from './DocumentGrid';
import { DocumentList } from './DocumentList';
import { DocumentImport } from './DocumentImport';
import { ImportService } from '../../services/document/ImportService';

interface DocumentLibraryProps {
  onDocumentPress?: (documentId: string) => void;
  onDocumentLongPress?: (documentId: string) => void;
}

export const DocumentLibrary: React.FC<DocumentLibraryProps> = ({
  onDocumentPress,
  onDocumentLongPress,
}) => {
  const dispatch = useAppDispatch();
  const {
    documents,
    viewMode,
    sortBy,
    sortOrder,
    searchQuery,
    selectedDocuments,
    isLoading,
    error,
    isSearching,
    results,
  } = useAppSelector(state => state.documents);

  const [refreshing, setRefreshing] = useState(false);
  const [searchVisible, setSearchVisible] = useState(false);
  const [sortMenuVisible, setSortMenuVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const importService = ImportService.getInstance();

  // Load documents on component mount
  useEffect(() => {
    dispatch(loadDocuments());
  }, [dispatch]);

  // Handle refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await dispatch(loadDocuments());
    setRefreshing(false);
  }, [dispatch]);

  // Handle search
  const handleSearch = useCallback(
    (query: string) => {
      dispatch(setSearchQuery(query));
      if (query.trim()) {
        dispatch(searchDocuments(query));
      }
    },
    [dispatch],
  );

  // Handle view mode toggle
  const toggleViewMode = useCallback(() => {
    const newMode =
      viewMode === DocumentViewMode.GRID
        ? DocumentViewMode.LIST
        : DocumentViewMode.GRID;
    dispatch(setViewMode(newMode));
  }, [dispatch, viewMode]);

  // Handle sort change
  const handleSortChange = useCallback(
    (newSortBy: DocumentSortBy) => {
      if (sortBy === newSortBy) {
        // Toggle sort order if same field
        const newOrder =
          sortOrder === SortOrder.ASC ? SortOrder.DESC : SortOrder.ASC;
        dispatch(setSortOrder(newOrder));
      } else {
        dispatch(setSortBy(newSortBy));
        dispatch(setSortOrder(SortOrder.ASC));
      }
      setSortMenuVisible(false);
    },
    [dispatch, sortBy, sortOrder],
  );

  // Handle document import
  const handleImport = useCallback(async () => {
    try {
      const files = await importService.pickDocuments(true);
      if (files.length > 0) {
        setImportModalVisible(true);
        // Import will be handled by DocumentImport component
      }
    } catch (_error) {
      Alert.alert(
        'Import Error',
        'Failed to select documents for import. Please try again.',
        [{ text: 'OK' }],
      );
    }
  }, [importService]);

  // Handle document selection
  const handleDocumentPress = useCallback(
    (documentId: string) => {
      if (selectedDocuments.length > 0) {
        // In selection mode, toggle selection
        // This will be handled by the grid/list components
      } else {
        // Normal mode, open document
        onDocumentPress?.(documentId);
      }
    },
    [selectedDocuments, onDocumentPress],
  );

  const handleDocumentLongPress = useCallback(
    (documentId: string) => {
      onDocumentLongPress?.(documentId);
    },
    [onDocumentLongPress],
  );

  // Clear selection when back is pressed
  const _handleBackPress = useCallback(() => {
    if (selectedDocuments.length > 0) {
      dispatch(clearSelection());
      return true; // Prevent default back action
    }
    return false;
  }, [dispatch, selectedDocuments]);

  // Get documents to display (search results or all documents)
  const displayDocuments = searchQuery.trim() ? results : documents;

  // Render app bar
  const renderAppBar = () => (
    <Appbar.Header>
      {selectedDocuments.length > 0 ? (
        <>
          <Appbar.BackAction onPress={() => dispatch(clearSelection())} />
          <Appbar.Content title={`${selectedDocuments.length} selected`} />
          <Appbar.Action
            icon="delete"
            onPress={() => {
              /* TODO: Handle bulk delete */
            }}
          />
          <Appbar.Action
            icon="folder-plus"
            onPress={() => {
              /* TODO: Handle add to collection */
            }}
          />
        </>
      ) : (
        <>
          <Appbar.Content title="Library" />
          <Appbar.Action
            icon="magnify"
            onPress={() => setSearchVisible(!searchVisible)}
          />
          <Menu
            visible={sortMenuVisible}
            onDismiss={() => setSortMenuVisible(false)}
            anchor={
              <Appbar.Action
                icon="sort"
                onPress={() => setSortMenuVisible(true)}
              />
            }
          >
            <Menu.Item
              onPress={() => handleSortChange(DocumentSortBy.NAME)}
              title="Name"
              leadingIcon={sortBy === DocumentSortBy.NAME ? 'check' : undefined}
            />
            <Menu.Item
              onPress={() => handleSortChange(DocumentSortBy.DATE_ADDED)}
              title="Date Added"
              leadingIcon={
                sortBy === DocumentSortBy.DATE_ADDED ? 'check' : undefined
              }
            />
            <Menu.Item
              onPress={() => handleSortChange(DocumentSortBy.LAST_OPENED)}
              title="Last Opened"
              leadingIcon={
                sortBy === DocumentSortBy.LAST_OPENED ? 'check' : undefined
              }
            />
            <Menu.Item
              onPress={() => handleSortChange(DocumentSortBy.FILE_SIZE)}
              title="File Size"
              leadingIcon={
                sortBy === DocumentSortBy.FILE_SIZE ? 'check' : undefined
              }
            />
          </Menu>
          <Appbar.Action
            icon={
              viewMode === DocumentViewMode.GRID ? 'view-list' : 'view-grid'
            }
            onPress={toggleViewMode}
          />
        </>
      )}
    </Appbar.Header>
  );

  // Render search bar
  const renderSearchBar = () => {
    if (!searchVisible) return null;

    return (
      <Searchbar
        placeholder="Search documents..."
        onChangeText={handleSearch}
        value={searchQuery}
        loading={isSearching}
        style={styles.searchBar}
        onClearIconPress={() => {
          handleSearch('');
          setSearchVisible(false);
        }}
      />
    );
  };

  // Render content
  const renderContent = () => {
    if (error) {
      return (
        <View style={styles.errorContainer}>
          <Text variant="bodyLarge" style={styles.errorText}>
            {error}
          </Text>
          <Button
            mode="contained"
            onPress={onRefresh}
            style={styles.retryButton}
          >
            Retry
          </Button>
        </View>
      );
    }

    if (displayDocuments.length === 0 && !isLoading) {
      return (
        <View style={styles.emptyContainer}>
          <Text variant="headlineSmall" style={styles.emptyTitle}>
            {searchQuery.trim() ? 'No documents found' : 'No documents yet'}
          </Text>
          <Text variant="bodyLarge" style={styles.emptySubtitle}>
            {searchQuery.trim()
              ? 'Try adjusting your search terms'
              : 'Import your first document to get started'}
          </Text>
          {!searchQuery.trim() && (
            <Button
              mode="contained"
              onPress={handleImport}
              style={styles.importButton}
              icon="plus"
            >
              Import Documents
            </Button>
          )}
        </View>
      );
    }

    const refreshControl = (
      <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
    );

    return viewMode === DocumentViewMode.GRID ? (
      <DocumentGrid
        documents={displayDocuments}
        onDocumentPress={handleDocumentPress}
        onDocumentLongPress={handleDocumentLongPress}
        refreshControl={refreshControl}
        selectedDocuments={selectedDocuments}
      />
    ) : (
      <DocumentList
        documents={displayDocuments}
        onDocumentPress={handleDocumentPress}
        onDocumentLongPress={handleDocumentLongPress}
        refreshControl={refreshControl}
        selectedDocuments={selectedDocuments}
      />
    );
  };

  return (
    <View style={styles.container}>
      {renderAppBar()}
      {renderSearchBar()}
      {renderContent()}

      {selectedDocuments.length === 0 && (
        <FAB
          icon="plus"
          style={styles.fab}
          onPress={handleImport}
          label="Import"
        />
      )}

      <Portal>
        <Modal
          visible={importModalVisible}
          onDismiss={() => setImportModalVisible(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <DocumentImport
            onClose={() => setImportModalVisible(false)}
            onImportComplete={() => {
              setImportModalVisible(false);
              dispatch(loadDocuments());
            }}
          />
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  searchBar: {
    margin: 16,
    marginBottom: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    textAlign: 'center',
    marginBottom: 16,
    color: '#B00020',
  },
  retryButton: {
    marginTop: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    textAlign: 'center',
    marginBottom: 8,
    color: '#424242',
  },
  emptySubtitle: {
    textAlign: 'center',
    marginBottom: 24,
    color: '#757575',
  },
  importButton: {
    marginTop: 16,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    backgroundColor: 'white',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
});
