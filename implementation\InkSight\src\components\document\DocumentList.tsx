/**
 * Document List Component
 * Displays documents in a list layout with detailed metadata
 */

import React from 'react';
import { FlatList, StyleSheet, RefreshControl, View } from 'react-native';
import { DocumentLibraryItem } from '../../types/document-management';
import { DocumentListItem } from './DocumentListItem';

interface DocumentListProps {
  documents: DocumentLibraryItem[];
  onDocumentPress: (documentId: string) => void;
  onDocumentLongPress: (documentId: string) => void;
  refreshControl?: React.ReactElement<any>;
  selectedDocuments: string[];
}

export const DocumentList: React.FC<DocumentListProps> = ({
  documents,
  onDocumentPress,
  onDocumentLongPress,
  refreshControl,
  selectedDocuments,
}) => {
  const renderDocument = ({ item }: { item: DocumentLibraryItem }) => (
    <DocumentListItem
      document={item}
      onPress={() => onDocumentPress(item.id)}
      onLongPress={() => onDocumentLongPress(item.id)}
      isSelected={selectedDocuments.includes(item.id)}
    />
  );

  return (
    <FlatList
      data={documents}
      style={styles.container}
      renderItem={renderDocument}
      refreshControl={refreshControl}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.contentContainer}
      keyExtractor={item => item.id}
      ItemSeparatorComponent={() => <View style={styles.separator} />}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  contentContainer: {
    paddingVertical: 8,
  },
  separator: {
    height: 1,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 16,
  },
});
