/**
 * @format
 */

import React from 'react';
import { Text } from 'react-native';
import ReactTestRenderer from 'react-test-renderer';

test('basic test without JSX', () => {
  expect(1 + 1).toBe(2);
});

test('React createElement test', () => {
  const element = React.createElement(Text, null, 'Hello World');
  const tree = ReactTestRenderer.create(element);
  expect(tree.toJSON()).toBeTruthy();
});

test('JSX syntax test', () => {
  const element = <Text>Hello JSX</Text>;
  const tree = ReactTestRenderer.create(element);
  expect(tree.toJSON()).toBeTruthy();
});
