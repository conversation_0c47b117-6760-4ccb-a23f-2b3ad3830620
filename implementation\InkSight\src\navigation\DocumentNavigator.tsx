/**
 * Document Navigator
 * Navigation structure for document management features
 */

import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { DocumentLibrary } from '../components/document/DocumentLibrary';

export type DocumentStackParamList = {
  DocumentLibrary: undefined;
};

const Stack = createStackNavigator<DocumentStackParamList>();

export const DocumentNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false, // We handle headers in individual components
      }}
    >
      <Stack.Screen
        name="DocumentLibrary"
        component={DocumentLibrary}
        options={{
          title: 'Library',
        }}
      />
    </Stack.Navigator>
  );
};
