/**
 * DatabaseService - Encrypted SQLite database service for InkSight
 * Provides secure, offline-first data storage with encryption and migration support
 */

import SQLite from 'react-native-sqlite-2';
import {
  DatabaseConfig,
  DatabaseStatus,
  QueryResult,
  QueryParams,
  IDatabaseService,
  StorageError,
  StorageErrorType,
  DEFAULT_DATABASE_CONFIG,
  SchemaVersion,
  DatabaseMigration,
  IsolationLevel,
  BackupConfig,
  BackupMetadata,
  RestoreOptions,
} from '../../types/storage';
import { EncryptionService } from '../security/EncryptionService';
import { SecurityContext } from '../../types/security';

export class DatabaseService implements IDatabaseService {
  private db: any | null = null;
  private config: DatabaseConfig;
  private status: DatabaseStatus = 'disconnected';
  private encryptionService: EncryptionService;
  private currentTransaction: boolean = false;

  constructor(
    encryptionService: EncryptionService,
    config: DatabaseConfig = DEFAULT_DATABASE_CONFIG,
  ) {
    this.encryptionService = encryptionService;
    this.config = config;
  }

  /**
   * Connect to the database
   */
  public async connect(config?: DatabaseConfig): Promise<void> {
    if (config) {
      this.config = { ...this.config, ...config };
    }

    try {
      this.status = 'connecting';

      // Open database connection
      this.db = SQLite.openDatabase(
        this.config.name,
        '1.0',
        'InkSight Database',
        200000,
      );

      // Configure database settings
      await this.configureDatabaseSettings();

      // Initialize schema if needed
      await this.initializeSchema();

      this.status = 'connected';
    } catch (error) {
      this.status = 'error';
      throw new StorageError(
        'DATABASE_CONNECTION_FAILED',
        `Failed to connect to database: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        { config: this.config, originalError: error },
      );
    }
  }

  /**
   * Disconnect from the database
   */
  public async disconnect(): Promise<void> {
    if (this.db) {
      try {
        // Close any open transactions
        if (this.currentTransaction) {
          await this.rollback();
        }

        // Close database connection
        await new Promise<void>((resolve, reject) => {
          this.db!.close(
            () => resolve(),
            (error: any) => reject(error),
          );
        });

        this.db = null;
        this.status = 'disconnected';
      } catch (error) {
        this.status = 'error';
        throw new StorageError(
          'DATABASE_CONNECTION_FAILED',
          `Failed to disconnect from database: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`,
          { originalError: error },
        );
      }
    }
  }

  /**
   * Get database connection status
   */
  public getStatus(): DatabaseStatus {
    return this.status;
  }

  /**
   * Execute a SQL query
   */
  public async query<T>(
    sql: string,
    params: QueryParams = {},
  ): Promise<QueryResult<T>> {
    this.ensureConnected();

    const startTime = Date.now();

    try {
      const result = await new Promise<QueryResult<T>>((resolve, reject) => {
        this.db!.transaction((tx: any) => {
          // Convert params object to array for SQLite
          const paramArray = Object.values(params);

          tx.executeSql(
            sql,
            paramArray,
            (_: any, resultSet: any) => {
              const rows: T[] = [];
              for (let i = 0; i < resultSet.rows.length; i++) {
                rows.push(resultSet.rows.item(i));
              }

              resolve({
                rows,
                rowCount: resultSet.rows.length,
                insertId: resultSet.insertId,
                affectedRows: resultSet.rowsAffected,
                executionTime: Date.now() - startTime,
              });
            },
            (_: any, error: any) => {
              reject(error);
              return false;
            },
          );
        });
      });

      return result;
    } catch (error) {
      throw new StorageError(
        'DATABASE_QUERY_FAILED',
        `Query failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        { sql, params, originalError: error },
      );
    }
  }

  /**
   * Insert a record
   */
  public async insert<T>(table: string, data: Partial<T>): Promise<string> {
    const columns = Object.keys(data);
    const placeholders = columns.map(() => '?').join(', ');
    const values = Object.values(data);

    const sql = `INSERT INTO ${table} (${columns.join(
      ', ',
    )}) VALUES (${placeholders})`;

    const result = await this.query(sql, values as any);
    return result.insertId?.toString() || '';
  }

  /**
   * Update a record
   */
  public async update<T>(
    table: string,
    id: string,
    data: Partial<T>,
  ): Promise<boolean> {
    const columns = Object.keys(data);
    const setClause = columns.map(col => `${col} = ?`).join(', ');
    const values = [...Object.values(data), id];

    const sql = `UPDATE ${table} SET ${setClause} WHERE id = ?`;

    const result = await this.query(sql, values as any);
    return result.affectedRows > 0;
  }

  /**
   * Delete a record
   */
  public async delete(table: string, id: string): Promise<boolean> {
    const sql = `DELETE FROM ${table} WHERE id = ?`;
    const result = await this.query(sql, { id });
    return result.affectedRows > 0;
  }

  /**
   * Begin a transaction
   */
  public async beginTransaction(
    isolationLevel?: IsolationLevel,
  ): Promise<void> {
    this.ensureConnected();

    if (this.currentTransaction) {
      throw new StorageError(
        'DATABASE_QUERY_FAILED',
        'Transaction already in progress',
      );
    }

    try {
      await this.query('BEGIN TRANSACTION');
      this.currentTransaction = true;
    } catch (error) {
      throw new StorageError(
        'DATABASE_QUERY_FAILED',
        `Failed to begin transaction: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        { isolationLevel, originalError: error },
      );
    }
  }

  /**
   * Commit a transaction
   */
  public async commit(): Promise<void> {
    this.ensureConnected();

    if (!this.currentTransaction) {
      throw new StorageError(
        'DATABASE_QUERY_FAILED',
        'No transaction in progress',
      );
    }

    try {
      await this.query('COMMIT');
      this.currentTransaction = false;
    } catch (error) {
      this.currentTransaction = false;
      throw new StorageError(
        'DATABASE_QUERY_FAILED',
        `Failed to commit transaction: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        { originalError: error },
      );
    }
  }

  /**
   * Rollback a transaction
   */
  public async rollback(): Promise<void> {
    this.ensureConnected();

    if (!this.currentTransaction) {
      return; // No transaction to rollback
    }

    try {
      await this.query('ROLLBACK');
      this.currentTransaction = false;
    } catch (error) {
      this.currentTransaction = false;
      throw new StorageError(
        'DATABASE_QUERY_FAILED',
        `Failed to rollback transaction: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        { originalError: error },
      );
    }
  }

  /**
   * Create database tables
   */
  public async createTables(): Promise<void> {
    const createTableQueries = [
      // Documents table
      `CREATE TABLE IF NOT EXISTS documents (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        format TEXT NOT NULL,
        filePath TEXT NOT NULL,
        encryptedContent TEXT,
        metadata TEXT NOT NULL,
        checksum TEXT NOT NULL,
        size INTEGER NOT NULL,
        created INTEGER NOT NULL,
        modified INTEGER NOT NULL,
        lastAccessed INTEGER NOT NULL,
        tags TEXT DEFAULT '[]',
        collectionId TEXT,
        favorite INTEGER DEFAULT 0,
        archived INTEGER DEFAULT 0
      )`,

      // Annotations table
      `CREATE TABLE IF NOT EXISTS annotations (
        id TEXT PRIMARY KEY,
        documentId TEXT NOT NULL,
        type TEXT NOT NULL,
        content TEXT NOT NULL,
        position TEXT NOT NULL,
        color TEXT,
        created INTEGER NOT NULL,
        modified INTEGER NOT NULL,
        tags TEXT DEFAULT '[]',
        private INTEGER DEFAULT 0,
        FOREIGN KEY (documentId) REFERENCES documents(id) ON DELETE CASCADE
      )`,

      // Collections table
      `CREATE TABLE IF NOT EXISTS collections (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        parentId TEXT,
        color TEXT,
        icon TEXT,
        created INTEGER NOT NULL,
        modified INTEGER NOT NULL,
        documentCount INTEGER DEFAULT 0,
        archived INTEGER DEFAULT 0,
        FOREIGN KEY (parentId) REFERENCES collections(id) ON DELETE CASCADE
      )`,

      // Settings table
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        category TEXT NOT NULL,
        type TEXT NOT NULL,
        created INTEGER NOT NULL,
        modified INTEGER NOT NULL
      )`,

      // Audit table
      `CREATE TABLE IF NOT EXISTS audit_log (
        id TEXT PRIMARY KEY,
        operation TEXT NOT NULL,
        tableName TEXT NOT NULL,
        recordId TEXT NOT NULL,
        oldValues TEXT,
        newValues TEXT,
        timestamp INTEGER NOT NULL,
        userId TEXT,
        sessionId TEXT NOT NULL
      )`,
    ];

    try {
      for (const query of createTableQueries) {
        await this.query(query);
      }

      // Create indexes for better performance
      await this.createIndexes();
    } catch (error) {
      throw new StorageError(
        'DATABASE_QUERY_FAILED',
        `Failed to create tables: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        { originalError: error },
      );
    }
  }

  /**
   * Migrate database to target version
   */
  public async migrate(targetVersion: number): Promise<void> {
    // Placeholder for migration logic
    // In a real implementation, this would handle schema migrations
    console.log(`Migrating database to version ${targetVersion}`);
  }

  /**
   * Get current schema version
   */
  public async getSchemaVersion(): Promise<number> {
    try {
      const result = await this.query<{ version: number }>(
        'PRAGMA user_version',
      );
      return result.rows[0]?.version || 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Create backup (placeholder)
   */
  public async createBackup(config: BackupConfig): Promise<BackupMetadata> {
    throw new StorageError(
      'BACKUP_FAILED',
      'Backup functionality not yet implemented',
    );
  }

  /**
   * Restore backup (placeholder)
   */
  public async restoreBackup(options: RestoreOptions): Promise<boolean> {
    throw new StorageError(
      'RESTORE_FAILED',
      'Restore functionality not yet implemented',
    );
  }

  /**
   * List backups (placeholder)
   */
  public async listBackups(): Promise<BackupMetadata[]> {
    return [];
  }

  /**
   * Configure database settings
   */
  private async configureDatabaseSettings(): Promise<void> {
    const settings = [
      `PRAGMA journal_mode = ${this.config.journalMode}`,
      `PRAGMA synchronous = ${this.config.synchronous}`,
      'PRAGMA foreign_keys = ON',
      'PRAGMA temp_store = MEMORY',
    ];

    for (const setting of settings) {
      await this.query(setting);
    }
  }

  /**
   * Initialize database schema
   */
  private async initializeSchema(): Promise<void> {
    const currentVersion = await this.getSchemaVersion();

    if (currentVersion === 0) {
      await this.createTables();
      await this.query(`PRAGMA user_version = ${this.config.version}`);
    } else if (currentVersion < this.config.version) {
      await this.migrate(this.config.version);
    }
  }

  /**
   * Create database indexes
   */
  private async createIndexes(): Promise<void> {
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_documents_format ON documents(format)',
      'CREATE INDEX IF NOT EXISTS idx_documents_created ON documents(created)',
      'CREATE INDEX IF NOT EXISTS idx_documents_collection ON documents(collectionId)',
      'CREATE INDEX IF NOT EXISTS idx_annotations_document ON annotations(documentId)',
      'CREATE INDEX IF NOT EXISTS idx_annotations_type ON annotations(type)',
      'CREATE INDEX IF NOT EXISTS idx_collections_parent ON collections(parentId)',
      'CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)',
      'CREATE INDEX IF NOT EXISTS idx_audit_table ON audit_log(tableName)',
    ];

    for (const index of indexes) {
      await this.query(index);
    }
  }

  /**
   * Ensure database is connected
   */
  private ensureConnected(): void {
    if (!this.db || this.status !== 'connected') {
      throw new StorageError(
        'DATABASE_CONNECTION_FAILED',
        'Database not connected',
      );
    }
  }
}
