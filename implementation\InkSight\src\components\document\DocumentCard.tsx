/**
 * Document Card Component
 * Material Design 3 card for displaying document information in grid view
 */

import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Card, Text, Chip, IconButton, ProgressBar } from 'react-native-paper';
import { DocumentLibraryItem } from '../../types/document-management';

interface DocumentCardProps {
  document: DocumentLibraryItem;
  onPress: () => void;
  onLongPress: () => void;
  isSelected: boolean;
  width: number;
}

export const DocumentCard: React.FC<DocumentCardProps> = ({
  document,
  onPress,
  onLongPress,
  isSelected,
  width,
}) => {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  };

  const getFormatColor = (format: string): string => {
    const colors: Record<string, string> = {
      pdf: '#FF5722',
      epub: '#4CAF50',
      txt: '#607D8B',
      rtf: '#795548',
      docx: '#2196F3',
      doc: '#2196F3',
      md: '#9C27B0',
      html: '#FF9800',
      csv: '#8BC34A',
    };
    return colors[format.toLowerCase()] || '#757575';
  };

  const cardStyle = [styles.card, { width }, isSelected && styles.selectedCard];

  return (
    <TouchableOpacity
      onPress={onPress}
      onLongPress={onLongPress}
      activeOpacity={0.7}
    >
      <Card style={cardStyle} mode="elevated">
        {/* Selection indicator */}
        {isSelected && (
          <View style={styles.selectionIndicator}>
            <IconButton icon="check-circle" iconColor="#FFFFFF" size={20} />
          </View>
        )}

        {/* Document thumbnail or icon */}
        <View style={styles.thumbnailContainer}>
          {document.thumbnailPath ? (
            <Image
              source={{ uri: document.thumbnailPath }}
              style={styles.thumbnail}
              resizeMode="cover"
            />
          ) : (
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: getFormatColor(document.format) },
              ]}
            >
              <Text style={styles.formatText}>
                {document.format.toUpperCase()}
              </Text>
            </View>
          )}
        </View>

        <Card.Content style={styles.content}>
          {/* Document title */}
          <Text variant="titleSmall" numberOfLines={2} style={styles.title}>
            {document.title}
          </Text>

          {/* Author */}
          {document.author && (
            <Text variant="bodySmall" numberOfLines={1} style={styles.author}>
              {document.author}
            </Text>
          )}

          {/* Format chip */}
          <View style={styles.chipContainer}>
            <Chip
              mode="outlined"
              compact
              style={[
                styles.formatChip,
                { borderColor: getFormatColor(document.format) },
              ]}
              textStyle={[
                styles.formatChipText,
                { color: getFormatColor(document.format) },
              ]}
            >
              {document.format.toUpperCase()}
            </Chip>
          </View>

          {/* Reading progress */}
          {document.readingProgress > 0 && (
            <View style={styles.progressContainer}>
              <Text variant="bodySmall" style={styles.progressText}>
                {Math.round(document.readingProgress)}% read
              </Text>
              <ProgressBar
                progress={document.readingProgress / 100}
                style={styles.progressBar}
              />
            </View>
          )}

          {/* File size and date */}
          <View style={styles.metadataContainer}>
            <Text variant="bodySmall" style={styles.metadata}>
              {formatFileSize(document.fileSize)}
            </Text>
            <Text variant="bodySmall" style={styles.metadata}>
              {document.importedAt.toLocaleDateString()}
            </Text>
          </View>

          {/* Favorite indicator */}
          {document.isFavorite && (
            <View style={styles.favoriteIndicator}>
              <IconButton icon="heart" iconColor="#E91E63" size={16} />
            </View>
          )}
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 8,
    backgroundColor: '#FFFFFF',
  },
  selectedCard: {
    backgroundColor: '#E3F2FD',
    borderColor: '#2196F3',
    borderWidth: 2,
  },
  selectionIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 1,
    backgroundColor: '#2196F3',
    borderRadius: 12,
  },
  thumbnailContainer: {
    height: 120,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formatText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  content: {
    paddingTop: 12,
    paddingBottom: 8,
  },
  title: {
    fontWeight: '600',
    marginBottom: 4,
    color: '#212121',
  },
  author: {
    color: '#757575',
    marginBottom: 8,
  },
  chipContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  formatChip: {
    height: 24,
    backgroundColor: 'transparent',
  },
  formatChipText: {
    fontSize: 10,
    fontWeight: '600',
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressText: {
    color: '#757575',
    marginBottom: 4,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
  },
  metadataContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metadata: {
    color: '#9E9E9E',
    fontSize: 11,
  },
  favoriteIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
  },
});
