/**
 * FileSystemService - Secure file system operations for InkSight
 * Provides encrypted file storage, integrity verification, and secure file operations
 */

import RNFS from 'react-native-fs';
import {
  StoredFile,
  FileStorageConfig,
  IFileStorageService,
  StorageError,
  StorageErrorType,
  DEFAULT_FILE_STORAGE_CONFIG,
} from '../../types/storage';
import { EncryptionService } from '../security/EncryptionService';
import {
  SecurityContext,
  FileIntegrity,
  SecureDeletionOptions,
} from '../../types/security';

export class FileSystemService implements IFileStorageService {
  private config: FileStorageConfig;
  private encryptionService: EncryptionService;
  private basePath: string;
  private isInitialized: boolean = false;

  constructor(
    encryptionService: EncryptionService,
    config: FileStorageConfig = DEFAULT_FILE_STORAGE_CONFIG,
  ) {
    this.encryptionService = encryptionService;
    this.config = config;
    this.basePath = `${RNFS.DocumentDirectoryPath}/${config.basePath}`;
  }

  /**
   * Initialize the file system service
   */
  public async initialize(): Promise<void> {
    try {
      // Create base directory if it doesn't exist
      const exists = await RNFS.exists(this.basePath);
      if (!exists) {
        await RNFS.mkdir(this.basePath);
      }

      // Create subdirectories
      await this.createDirectoryStructure();

      this.isInitialized = true;
    } catch (error) {
      throw new StorageError(
        'FILE_ACCESS_DENIED',
        `Failed to initialize file system: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        { basePath: this.basePath, originalError: error },
      );
    }
  }

  /**
   * Store a file with optional encryption
   */
  public async storeFile(
    filePath: string,
    content: Buffer,
  ): Promise<StoredFile> {
    this.ensureInitialized();

    try {
      // Validate file size
      if (content.length > this.config.maxFileSize * 1024 * 1024) {
        throw new StorageError(
          'STORAGE_FULL',
          `File size exceeds maximum allowed size of ${this.config.maxFileSize}MB`,
        );
      }

      // Generate unique file ID and storage path
      const fileId = this.generateFileId();
      const storedPath = `${this.basePath}/files/${fileId}`;
      const originalName = filePath.split('/').pop() || 'unknown';

      // Validate file extension
      const extension = this.getFileExtension(originalName);
      if (!this.config.allowedExtensions.includes(extension)) {
        throw new StorageError(
          'FILE_ACCESS_DENIED',
          `File extension ${extension} is not allowed`,
        );
      }

      let finalContent = content;
      let encrypted = false;

      // Encrypt file if enabled
      if (this.config.encryptFiles) {
        const context: SecurityContext = {
          sessionId: 'file-storage',
          timestamp: Date.now(),
          operation: 'encrypt',
          securityLevel: 'high',
          auditRequired: true,
        };

        const encryptedData = await this.encryptionService.encrypt(
          content.toString('base64'),
          context,
        );

        finalContent = Buffer.from(JSON.stringify(encryptedData), 'utf8');
        encrypted = true;
      }

      // Compress file if enabled
      if (this.config.compressFiles) {
        // Note: Compression would require additional library
        // For now, we'll skip compression
      }

      // Write file to storage
      await RNFS.writeFile(
        storedPath,
        finalContent.toString('base64'),
        'base64',
      );

      // Generate checksum for integrity verification
      const checksum = this.encryptionService.generateHash(
        finalContent.toString('base64'),
      );

      // Create stored file record
      const storedFile: StoredFile = {
        id: fileId,
        originalName,
        storedPath,
        mimeType: this.getMimeType(extension),
        size: finalContent.length,
        checksum,
        encrypted,
        compressed: false,
        created: Date.now(),
        lastAccessed: Date.now(),
        metadata: {
          originalSize: content.length,
          extension,
        },
      };

      return storedFile;
    } catch (error) {
      if (error instanceof StorageError) {
        throw error;
      }

      throw new StorageError(
        'FILE_ACCESS_DENIED',
        `Failed to store file: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        { filePath, originalError: error },
      );
    }
  }

  /**
   * Retrieve a file by ID
   */
  public async retrieveFile(fileId: string): Promise<Buffer> {
    this.ensureInitialized();

    try {
      const filePath = `${this.basePath}/files/${fileId}`;

      // Check if file exists
      const exists = await RNFS.exists(filePath);
      if (!exists) {
        throw new StorageError('FILE_NOT_FOUND', `File ${fileId} not found`);
      }

      // Read file content
      const content = await RNFS.readFile(filePath, 'base64');
      let finalContent = Buffer.from(content, 'base64');

      // Check if file is encrypted (simple heuristic)
      try {
        const possibleEncryptedData = JSON.parse(finalContent.toString('utf8'));
        if (
          possibleEncryptedData.data &&
          possibleEncryptedData.iv &&
          possibleEncryptedData.salt
        ) {
          // File appears to be encrypted, decrypt it
          const context: SecurityContext = {
            sessionId: 'file-retrieval',
            timestamp: Date.now(),
            operation: 'decrypt',
            securityLevel: 'high',
            auditRequired: true,
          };

          const decryptedData = await this.encryptionService.decrypt(
            possibleEncryptedData,
            context,
          );

          finalContent = Buffer.from(decryptedData, 'base64');
        }
      } catch {
        // Not encrypted or decryption failed, use original content
      }

      return finalContent;
    } catch (error) {
      if (error instanceof StorageError) {
        throw error;
      }

      throw new StorageError(
        'FILE_ACCESS_DENIED',
        `Failed to retrieve file: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        { fileId, originalError: error },
      );
    }
  }

  /**
   * Delete a file
   */
  public async deleteFile(fileId: string): Promise<boolean> {
    this.ensureInitialized();

    try {
      const filePath = `${this.basePath}/files/${fileId}`;

      // Check if file exists
      const exists = await RNFS.exists(filePath);
      if (!exists) {
        return false;
      }

      // Perform secure deletion
      await this.secureDelete(filePath);

      return true;
    } catch (error) {
      throw new StorageError(
        'FILE_ACCESS_DENIED',
        `Failed to delete file: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        { fileId, originalError: error },
      );
    }
  }

  /**
   * List files matching a pattern
   */
  public async listFiles(pattern?: string): Promise<StoredFile[]> {
    this.ensureInitialized();

    try {
      const filesPath = `${this.basePath}/files`;
      const files = await RNFS.readDir(filesPath);

      // Filter files by pattern if provided
      const filteredFiles = pattern
        ? files.filter(file => file.name.includes(pattern))
        : files;

      // Convert to StoredFile objects (simplified)
      const storedFiles: StoredFile[] = [];
      for (const file of filteredFiles) {
        const stat = await RNFS.stat(file.path);
        storedFiles.push({
          id: file.name,
          originalName: file.name,
          storedPath: file.path,
          mimeType: 'application/octet-stream',
          size: stat.size,
          checksum: '',
          encrypted: false,
          compressed: false,
          created: typeof stat.ctime === 'number' ? stat.ctime : Date.now(),
          lastAccessed:
            typeof stat.mtime === 'number' ? stat.mtime : Date.now(),
        });
      }

      return storedFiles;
    } catch (error) {
      throw new StorageError(
        'FILE_ACCESS_DENIED',
        `Failed to list files: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        { pattern, originalError: error },
      );
    }
  }

  /**
   * Get file information
   */
  public async getFileInfo(fileId: string): Promise<StoredFile> {
    const files = await this.listFiles();
    const file = files.find(f => f.id === fileId);

    if (!file) {
      throw new StorageError('FILE_NOT_FOUND', `File ${fileId} not found`);
    }

    return file;
  }

  /**
   * Verify file integrity
   */
  public async verifyIntegrity(fileId: string): Promise<boolean> {
    try {
      const filePath = `${this.basePath}/files/${fileId}`;
      const content = await RNFS.readFile(filePath, 'base64');

      // For now, just check if file exists and is readable
      return content.length > 0;
    } catch {
      return false;
    }
  }

  /**
   * Import file from external source
   */
  public async importFile(
    sourcePath: string,
    encrypt: boolean = true,
  ): Promise<StoredFile> {
    try {
      const content = await RNFS.readFile(sourcePath, 'base64');
      const buffer = Buffer.from(content, 'base64');

      return await this.storeFile(sourcePath, buffer);
    } catch (error) {
      throw new StorageError(
        'FILE_ACCESS_DENIED',
        `Failed to import file: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        { sourcePath, originalError: error },
      );
    }
  }

  /**
   * Export file to external location
   */
  public async exportFile(
    fileId: string,
    targetPath: string,
  ): Promise<boolean> {
    try {
      const content = await this.retrieveFile(fileId);
      await RNFS.writeFile(targetPath, content.toString('base64'), 'base64');
      return true;
    } catch (error) {
      throw new StorageError(
        'FILE_ACCESS_DENIED',
        `Failed to export file: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        { fileId, targetPath, originalError: error },
      );
    }
  }

  /**
   * Cleanup old or unused files
   */
  public async cleanup(): Promise<number> {
    // Placeholder for cleanup logic
    return 0;
  }

  /**
   * Optimize storage
   */
  public async optimizeStorage(): Promise<void> {
    // Placeholder for storage optimization
  }

  /**
   * Create directory structure
   */
  private async createDirectoryStructure(): Promise<void> {
    const directories = [
      `${this.basePath}/files`,
      `${this.basePath}/temp`,
      `${this.basePath}/backups`,
      `${this.basePath}/cache`,
    ];

    for (const dir of directories) {
      const exists = await RNFS.exists(dir);
      if (!exists) {
        await RNFS.mkdir(dir);
      }
    }
  }

  /**
   * Generate unique file ID
   */
  private generateFileId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get file extension
   */
  private getFileExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.');
    return lastDot !== -1 ? filename.substring(lastDot) : '';
  }

  /**
   * Get MIME type for file extension
   */
  private getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      '.pdf': 'application/pdf',
      '.epub': 'application/epub+zip',
      '.txt': 'text/plain',
      '.rtf': 'application/rtf',
      '.md': 'text/markdown',
      '.html': 'text/html',
      '.csv': 'text/csv',
      '.docx':
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.odt': 'application/vnd.oasis.opendocument.text',
    };

    return mimeTypes[extension] || 'application/octet-stream';
  }

  /**
   * Secure file deletion
   */
  private async secureDelete(
    filePath: string,
    options?: SecureDeletionOptions,
  ): Promise<void> {
    try {
      // Simple deletion for now
      // In a real implementation, this would overwrite the file multiple times
      await RNFS.unlink(filePath);
    } catch (error) {
      throw new StorageError(
        'FILE_ACCESS_DENIED',
        `Secure deletion failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        { filePath, originalError: error },
      );
    }
  }

  /**
   * Ensure service is initialized
   */
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new StorageError(
        'FILE_ACCESS_DENIED',
        'File system service not initialized',
      );
    }
  }
}
