/**
 * Storage and database type definitions for InkSight
 * Provides comprehensive types for database operations, file storage, and data synchronization
 */

import { EncryptedData } from './security';

// Database configuration
export interface DatabaseConfig {
  name: string;
  version: number;
  encrypted: boolean;
  encryptionKey?: string;
  location: string;
  maxSize: number; // Maximum database size in MB
  journalMode: 'DELETE' | 'WAL' | 'MEMORY';
  synchronous: 'OFF' | 'NORMAL' | 'FULL';
}

// Database connection status
export type DatabaseStatus =
  | 'disconnected'
  | 'connecting'
  | 'connected'
  | 'error';

// Database operation types
export type DatabaseOperation =
  | 'SELECT'
  | 'INSERT'
  | 'UPDATE'
  | 'DELETE'
  | 'CREATE'
  | 'DROP';

// Database transaction isolation levels
export type IsolationLevel =
  | 'READ_UNCOMMITTED'
  | 'READ_COMMITTED'
  | 'REPEATABLE_READ'
  | 'SERIALIZABLE';

// Database schema version
export interface SchemaVersion {
  version: number;
  description: string;
  migrationScript: string;
  timestamp: number;
  checksum: string;
}

// Database migration
export interface DatabaseMigration {
  fromVersion: number;
  toVersion: number;
  script: string;
  rollbackScript?: string;
  description: string;
  timestamp: number;
}

// Query parameters
export interface QueryParams {
  [key: string]: string | number | boolean | null;
}

// Query result
export interface QueryResult<T = any> {
  rows: T[];
  rowCount: number;
  insertId?: number;
  affectedRows: number;
  executionTime: number;
}

// Database table definitions
export interface DocumentRecord {
  id: string;
  title: string;
  format: string;
  filePath: string;
  encryptedContent?: EncryptedData;
  metadata: EncryptedData;
  checksum: string;
  size: number;
  created: number;
  modified: number;
  lastAccessed: number;
  tags: string[];
  collectionId?: string;
  favorite: boolean;
  archived: boolean;
}

export interface AnnotationRecord {
  id: string;
  documentId: string;
  type: 'highlight' | 'note' | 'bookmark';
  content: EncryptedData;
  position: EncryptedData;
  color?: string;
  created: number;
  modified: number;
  tags: string[];
  private: boolean;
}

export interface CollectionRecord {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  color?: string;
  icon?: string;
  created: number;
  modified: number;
  documentCount: number;
  archived: boolean;
}

export interface SettingsRecord {
  key: string;
  value: EncryptedData;
  category: string;
  type: 'string' | 'number' | 'boolean' | 'object';
  created: number;
  modified: number;
}

export interface AuditRecord {
  id: string;
  operation: string;
  tableName: string;
  recordId: string;
  oldValues?: EncryptedData;
  newValues?: EncryptedData;
  timestamp: number;
  userId?: string;
  sessionId: string;
}

// File storage types
export interface FileStorageConfig {
  basePath: string;
  maxFileSize: number; // Maximum file size in MB
  allowedExtensions: string[];
  encryptFiles: boolean;
  compressFiles: boolean;
  backupEnabled: boolean;
  cleanupInterval: number; // Cleanup interval in hours
}

export interface StoredFile {
  id: string;
  originalName: string;
  storedPath: string;
  mimeType: string;
  size: number;
  checksum: string;
  encrypted: boolean;
  compressed: boolean;
  created: number;
  lastAccessed: number;
  metadata?: Record<string, any>;
}

// Backup and restore types
export interface BackupConfig {
  enabled: boolean;
  interval: number; // Backup interval in hours
  maxBackups: number; // Maximum number of backups to keep
  compression: boolean;
  encryption: boolean;
  includeLogs: boolean;
  includeCache: boolean;
}

export interface BackupMetadata {
  id: string;
  timestamp: number;
  version: string;
  size: number;
  checksum: string;
  encrypted: boolean;
  compressed: boolean;
  tables: string[];
  fileCount: number;
  description?: string;
}

export interface RestoreOptions {
  backupId: string;
  overwriteExisting: boolean;
  restoreTables: string[];
  restoreFiles: boolean;
  validateIntegrity: boolean;
}

// Synchronization types
export interface SyncConfig {
  enabled: boolean;
  conflictResolution: ConflictResolutionStrategy;
  batchSize: number;
  retryAttempts: number;
  retryDelay: number; // Retry delay in seconds
}

export type ConflictResolutionStrategy =
  | 'client-wins'
  | 'server-wins'
  | 'last-modified-wins'
  | 'manual-resolution';

export interface SyncOperation {
  id: string;
  type: 'INSERT' | 'UPDATE' | 'DELETE';
  tableName: string;
  recordId: string;
  data?: any;
  timestamp: number;
  checksum: string;
  conflicted: boolean;
}

export interface SyncConflict {
  id: string;
  operation: SyncOperation;
  localData: any;
  remoteData: any;
  timestamp: number;
  resolved: boolean;
  resolution?: ConflictResolutionStrategy;
}

// Storage service interfaces
export interface IDatabaseService {
  // Connection management
  connect(config: DatabaseConfig): Promise<void>;
  disconnect(): Promise<void>;
  getStatus(): DatabaseStatus;

  // Schema management
  createTables(): Promise<void>;
  migrate(targetVersion: number): Promise<void>;
  getSchemaVersion(): Promise<number>;

  // CRUD operations
  query<T>(sql: string, params?: QueryParams): Promise<QueryResult<T>>;
  insert<T>(table: string, data: Partial<T>): Promise<string>;
  update<T>(table: string, id: string, data: Partial<T>): Promise<boolean>;
  delete(table: string, id: string): Promise<boolean>;

  // Transaction management
  beginTransaction(isolationLevel?: IsolationLevel): Promise<void>;
  commit(): Promise<void>;
  rollback(): Promise<void>;

  // Backup and restore
  createBackup(config: BackupConfig): Promise<BackupMetadata>;
  restoreBackup(options: RestoreOptions): Promise<boolean>;
  listBackups(): Promise<BackupMetadata[]>;
}

export interface IFileStorageService {
  // File operations
  storeFile(filePath: string, content: Buffer): Promise<StoredFile>;
  retrieveFile(fileId: string): Promise<Buffer>;
  deleteFile(fileId: string): Promise<boolean>;

  // File management
  listFiles(pattern?: string): Promise<StoredFile[]>;
  getFileInfo(fileId: string): Promise<StoredFile>;
  verifyIntegrity(fileId: string): Promise<boolean>;

  // Import/Export
  importFile(sourcePath: string, encrypt?: boolean): Promise<StoredFile>;
  exportFile(fileId: string, targetPath: string): Promise<boolean>;

  // Cleanup and maintenance
  cleanup(): Promise<number>; // Returns number of files cleaned up
  optimizeStorage(): Promise<void>;
}

// Storage error types
export type StorageErrorType =
  | 'DATABASE_CONNECTION_FAILED'
  | 'DATABASE_QUERY_FAILED'
  | 'DATABASE_MIGRATION_FAILED'
  | 'FILE_NOT_FOUND'
  | 'FILE_ACCESS_DENIED'
  | 'FILE_CORRUPTION'
  | 'STORAGE_FULL'
  | 'BACKUP_FAILED'
  | 'RESTORE_FAILED'
  | 'SYNC_FAILED'
  | 'INTEGRITY_CHECK_FAILED';

export class StorageError extends Error {
  public readonly type: StorageErrorType;
  public readonly details?: Record<string, any>;

  constructor(
    type: StorageErrorType,
    message: string,
    details?: Record<string, any>,
  ) {
    super(message);
    this.name = 'StorageError';
    this.type = type;
    this.details = details;
  }
}

// Default configurations
export const DEFAULT_DATABASE_CONFIG: DatabaseConfig = {
  name: 'inksight.db',
  version: 1,
  encrypted: true,
  location: 'default',
  maxSize: 500, // 500MB
  journalMode: 'WAL',
  synchronous: 'NORMAL',
};

export const DEFAULT_FILE_STORAGE_CONFIG: FileStorageConfig = {
  basePath: 'documents',
  maxFileSize: 100, // 100MB
  allowedExtensions: [
    '.pdf',
    '.epub',
    '.txt',
    '.rtf',
    '.md',
    '.html',
    '.csv',
    '.docx',
    '.odt',
  ],
  encryptFiles: true,
  compressFiles: true,
  backupEnabled: true,
  cleanupInterval: 24, // 24 hours
};

export const DEFAULT_BACKUP_CONFIG: BackupConfig = {
  enabled: true,
  interval: 24, // 24 hours
  maxBackups: 7, // Keep 7 backups
  compression: true,
  encryption: true,
  includeLogs: false,
  includeCache: false,
};
