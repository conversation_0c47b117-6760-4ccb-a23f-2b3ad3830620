// Jest setup file for React Native testing

// Basic setup for React Native Jest environment

// Mock react-native-fs if it's used
jest.mock('react-native-fs', () => ({
  readFile: jest.fn(),
  readDir: jest.fn(),
  stat: jest.fn(),
  read: jest.fn(),
  exists: jest.fn(),
  DocumentDirectoryPath: '/mock/documents',
  CachesDirectoryPath: '/mock/caches',
}));

// Mock any other native dependencies
jest.mock('react-native-pdf', () => ({
  default: 'MockedPDF',
}));

// Silence console warnings during tests
global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn(),
};

// Setup global test environment
global.__DEV__ = true;
