/**
 * Document Import Component
 * Modal interface for importing documents with progress tracking
 */

import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Text,
  Button,
  ProgressBar,
  Divider,
  IconButton,
  Chip,
} from 'react-native-paper';
import { ImportService } from '../../services/document/ImportService';
import { ImportProgress, ImportStatus } from '../../types/document-management';

interface DocumentImportProps {
  onClose: () => void;
  onImportComplete: () => void;
}

export const DocumentImport: React.FC<DocumentImportProps> = ({
  onClose,
  onImportComplete,
}) => {
  const [imports, setImports] = useState<ImportProgress[]>([]);
  const [isImporting, setIsImporting] = useState(false);
  const importService = ImportService.getInstance();

  useEffect(() => {
    // Load any existing imports
    const activeImports = importService.getActiveImports();
    setImports(activeImports);
    setIsImporting(
      activeImports.some(
        imp =>
          imp.status === ImportStatus.PENDING ||
          imp.status === ImportStatus.PROCESSING,
      ),
    );
  }, [importService]);

  const handleSelectFiles = async () => {
    try {
      const files = await importService.pickDocuments(true);
      if (files.length === 0) return;

      setIsImporting(true);

      const results = await importService.batchImport(
        files,
        {
          generateThumbnail: true,
          extractMetadata: true,
          overwriteExisting: false,
        },
        progress => {
          setImports(prev => {
            const updated = [...prev];
            const index = updated.findIndex(imp => imp.id === progress.id);
            if (index >= 0) {
              updated[index] = progress;
            } else {
              updated.push(progress);
            }
            return updated;
          });
        },
      );

      setIsImporting(false);

      // Check if all imports completed successfully
      const allCompleted = results.every(result => result.success);
      if (allCompleted) {
        setTimeout(() => {
          onImportComplete();
        }, 1000);
      }
    } catch (error) {
      setIsImporting(false);
      console.error('Import failed:', error);
    }
  };

  const handleCancelImport = async (importId: string) => {
    await importService.cancelImport(importId);
    setImports(prev =>
      prev.map(imp =>
        imp.id === importId ? { ...imp, status: ImportStatus.CANCELLED } : imp,
      ),
    );
  };

  const handleClearCompleted = () => {
    importService.clearCompletedImports();
    setImports(prev =>
      prev.filter(
        imp =>
          imp.status === ImportStatus.PENDING ||
          imp.status === ImportStatus.PROCESSING,
      ),
    );
  };

  const getStatusColor = (status: ImportStatus): string => {
    switch (status) {
      case ImportStatus.COMPLETED:
        return '#4CAF50';
      case ImportStatus.FAILED:
        return '#F44336';
      case ImportStatus.CANCELLED:
        return '#FF9800';
      case ImportStatus.PROCESSING:
        return '#2196F3';
      default:
        return '#757575';
    }
  };

  const getStatusIcon = (status: ImportStatus): string => {
    switch (status) {
      case ImportStatus.COMPLETED:
        return 'check-circle';
      case ImportStatus.FAILED:
        return 'alert-circle';
      case ImportStatus.CANCELLED:
        return 'cancel';
      case ImportStatus.PROCESSING:
        return 'loading';
      default:
        return 'clock-outline';
    }
  };

  const completedImports = imports.filter(
    imp =>
      imp.status === ImportStatus.COMPLETED ||
      imp.status === ImportStatus.FAILED ||
      imp.status === ImportStatus.CANCELLED,
  );

  const activeImports = imports.filter(
    imp =>
      imp.status === ImportStatus.PENDING ||
      imp.status === ImportStatus.PROCESSING,
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text variant="headlineSmall" style={styles.title}>
          Import Documents
        </Text>
        <IconButton icon="close" onPress={onClose} />
      </View>

      <Divider />

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Import button */}
        {!isImporting && activeImports.length === 0 && (
          <View style={styles.importSection}>
            <Text variant="bodyLarge" style={styles.description}>
              Select documents to import into your library. Supported formats
              include PDF, EPUB, TXT, RTF, DOCX, and more.
            </Text>
            <Button
              mode="contained"
              onPress={handleSelectFiles}
              style={styles.selectButton}
              icon="file-plus"
            >
              Select Files
            </Button>
          </View>
        )}

        {/* Active imports */}
        {activeImports.length > 0 && (
          <View style={styles.section}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Importing ({activeImports.length})
            </Text>
            {activeImports.map(importItem => (
              <View key={importItem.id} style={styles.importItem}>
                <View style={styles.importHeader}>
                  <Text
                    variant="bodyMedium"
                    numberOfLines={1}
                    style={styles.fileName}
                  >
                    {importItem.fileName}
                  </Text>
                  <View style={styles.importActions}>
                    <Chip
                      mode="outlined"
                      compact
                      style={[
                        styles.statusChip,
                        { borderColor: getStatusColor(importItem.status) },
                      ]}
                      textStyle={[
                        styles.statusChipText,
                        { color: getStatusColor(importItem.status) },
                      ]}
                      icon={getStatusIcon(importItem.status)}
                    >
                      {importItem.status.toUpperCase()}
                    </Chip>
                    {importItem.status === ImportStatus.PROCESSING && (
                      <IconButton
                        icon="close"
                        size={16}
                        onPress={() => handleCancelImport(importItem.id)}
                      />
                    )}
                  </View>
                </View>

                {importItem.status === ImportStatus.PROCESSING && (
                  <View style={styles.progressContainer}>
                    <ProgressBar
                      progress={importItem.progress / 100}
                      style={styles.progressBar}
                    />
                    <Text variant="bodySmall" style={styles.progressText}>
                      {importItem.progress}%
                    </Text>
                  </View>
                )}

                {importItem.error && (
                  <Text variant="bodySmall" style={styles.errorText}>
                    {importItem.error}
                  </Text>
                )}
              </View>
            ))}
          </View>
        )}

        {/* Completed imports */}
        {completedImports.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Completed ({completedImports.length})
              </Text>
              <Button mode="text" compact onPress={handleClearCompleted}>
                Clear
              </Button>
            </View>

            {completedImports.map(importItem => (
              <View key={importItem.id} style={styles.importItem}>
                <View style={styles.importHeader}>
                  <Text
                    variant="bodyMedium"
                    numberOfLines={1}
                    style={styles.fileName}
                  >
                    {importItem.fileName}
                  </Text>
                  <Chip
                    mode="outlined"
                    compact
                    style={[
                      styles.statusChip,
                      { borderColor: getStatusColor(importItem.status) },
                    ]}
                    textStyle={[
                      styles.statusChipText,
                      { color: getStatusColor(importItem.status) },
                    ]}
                    icon={getStatusIcon(importItem.status)}
                  >
                    {importItem.status.toUpperCase()}
                  </Chip>
                </View>

                {importItem.error && (
                  <Text variant="bodySmall" style={styles.errorText}>
                    {importItem.error}
                  </Text>
                )}
              </View>
            ))}
          </View>
        )}

        {/* Add more files button */}
        {(activeImports.length > 0 || completedImports.length > 0) &&
          !isImporting && (
            <Button
              mode="outlined"
              onPress={handleSelectFiles}
              style={styles.addMoreButton}
              icon="plus"
            >
              Add More Files
            </Button>
          )}
      </ScrollView>

      {/* Footer */}
      <Divider />
      <View style={styles.footer}>
        <Button mode="text" onPress={onClose} disabled={isImporting}>
          {isImporting ? 'Importing...' : 'Close'}
        </Button>
        {completedImports.some(
          imp => imp.status === ImportStatus.COMPLETED,
        ) && (
          <Button mode="contained" onPress={onImportComplete}>
            Done
          </Button>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  title: {
    fontWeight: '600',
    color: '#212121',
  },
  content: {
    maxHeight: 400,
  },
  importSection: {
    padding: 24,
    alignItems: 'center',
  },
  description: {
    textAlign: 'center',
    color: '#757575',
    marginBottom: 24,
  },
  selectButton: {
    minWidth: 150,
  },
  section: {
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  sectionTitle: {
    fontWeight: '600',
    color: '#424242',
  },
  importItem: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
  },
  importHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  fileName: {
    flex: 1,
    fontWeight: '500',
    color: '#212121',
  },
  importActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusChip: {
    height: 24,
    backgroundColor: 'transparent',
  },
  statusChipText: {
    fontSize: 10,
    fontWeight: '600',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  progressBar: {
    flex: 1,
    height: 4,
    borderRadius: 2,
    marginRight: 8,
  },
  progressText: {
    color: '#757575',
    minWidth: 30,
  },
  errorText: {
    color: '#F44336',
    marginTop: 4,
  },
  addMoreButton: {
    margin: 16,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
});
